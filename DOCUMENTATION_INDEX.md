# MCX3D Financials v2 - Documentation Navigation

## 📖 Documentation Structure

This comprehensive documentation system provides organized access to all project resources, APIs, and development guides.

---

## 🏠 Core Documentation

### Primary Resources
- **[PROJECT_INDEX.md](./PROJECT_INDEX.md)** - Complete project overview and technical reference
- **[README.md](./README.md)** - Project introduction and quick start guide
- **[CLAUDE.md](./CLAUDE.md)** - Development commands and guidelines for Claude Code
- **[PRD.md](./PRD.md)** - Product Requirements Document

### Architecture & Planning
- **[MCX3D_COMPLETE_PROJECT_PLAN.md](./MCX3D_COMPLETE_PROJECT_PLAN.md)** - Comprehensive project planning
- **[API_ENDPOINTS.md](./API_ENDPOINTS.md)** - API endpoint specifications
- **[dependency_analysis_report.md](./dependency_analysis_report.md)** - Dependency analysis and management

---

## 🚀 Deployment & Operations

### Docker & Infrastructure
- **[DOCKER_DEPLOYMENT_GUIDE.md](./DOCKER_DEPLOYMENT_GUIDE.md)** - Complete Docker deployment instructions
- **[DEPLOYMENT_VALIDATION_CHECKLIST.md](./DEPLOYMENT_VALIDATION_CHECKLIST.md)** - Deployment validation procedures
- **[docker-compose.yml](./docker-compose.yml)** - Multi-service container orchestration
- **[Dockerfile](./Dockerfile)** - Container build configuration

### Performance & Monitoring
- **[PERFORMANCE_BENCHMARK_REPORT.md](./PERFORMANCE_BENCHMARK_REPORT.md)** - Performance metrics and benchmarks
- **[performance_baseline.json](./performance_baseline.json)** - Baseline performance data

---

## ⚙️ Configuration Files

### Application Configuration
| File | Purpose | Location |
|------|---------|----------|
| `config.yml` | Application settings | Root directory |
| `.env.example` | Environment template | Root directory |
| `pyproject.toml` | Black formatter config | Root directory |
| `alembic.ini` | Database migrations | Root directory |
| `pytest.ini` | Test framework setup | Root directory |
| `mypy.ini` | Type checking config | Root directory |
| `setup.py` | Package installation | Root directory |

### Node.js & MCP Integration
| File | Purpose | Location |
|------|---------|----------|
| `package.json` | Node.js dependencies for MCP | Root directory |
| `claude_code_config.json` | Claude Code configuration | Root directory |

---

## 🧪 Testing & Quality

### Test Framework
- **[pytest.ini](./pytest.ini)** - Test configuration with comprehensive markers
- **Test Categories**:
  - `unit` - Isolated component tests (≥85% coverage)
  - `integration` - Database & external API tests
  - `e2e` - End-to-end workflow tests
  - `performance` - Benchmark tests
  - `smoke` - Production validation tests

### Code Quality
- **[requirements.txt](./requirements.txt)** - Python dependencies
- **Black Formatting** - 88 character line length (pyproject.toml)
- **Flake8 Linting** - PEP 8 compliance

---

## 📡 API Documentation

### Interactive Documentation
- **Swagger UI**: `http://localhost:8000/docs` (when running)
- **ReDoc**: `http://localhost:8000/redoc` (when running)

### API Categories
1. **Authentication** - Xero OAuth, JWT tokens
2. **Financial Reports** - Income statement, balance sheet, cash flow
3. **SaaS Metrics** - KPIs, analytics, growth metrics
4. **Xero Integration** - Data sync, import, status
5. **Health & Monitoring** - System health checks

---

## 💻 Command Line Interface

### CLI Categories
1. **Data Sync**: `sync` - Xero data synchronization
2. **Report Generation**: `generate` - Financial statements
3. **Valuation Models**: `valuate` - DCF, multiples, SaaS
4. **Analytics**: `analytics` - SaaS KPIs, growth analysis

### Usage Examples
```bash
# Get comprehensive help
docker-compose exec web python -m mcx3d_finance.cli.main --help

# Generate income statement
docker-compose exec web python -m mcx3d_finance.cli.main generate income-statement --help

# Run SaaS analytics
docker-compose exec web python -m mcx3d_finance.cli.main analytics saas-metrics --help
```

---

## 🏗️ Development Resources

### Code Organization
```
mcx3d_finance/
├── api/           # FastAPI endpoints & middleware
├── auth/          # Authentication & OAuth
├── cli/           # Command-line interface
├── core/          # Business logic & calculations
├── db/            # Database models & sessions
├── exceptions/    # Custom exception hierarchy
├── integrations/  # External API integrations
├── reporting/     # PDF/Excel generation
├── tasks/         # Celery background tasks
├── utils/         # Utilities & helpers
└── validation/    # Multi-layer validation
```

### Key Modules Documentation

#### Core Financial Engine
- **`core/financials/`** - Financial statement generators
- **`core/metrics/`** - SaaS KPI calculations
- **`core/valuation/`** - DCF & multiples models
- **`core/data_processors.py`** - Central data processing orchestrator

#### Data Processing Pipeline
1. **Ingestion** - Xero API data retrieval
2. **Validation** - Real-time validation engine
3. **Classification** - ML-based transaction categorization
4. **Transformation** - GAAP compliance & enrichment
5. **Storage** - PostgreSQL with proper indexing

#### Exception Handling
- **Base Exceptions** - `MCX3DException` hierarchy
- **Financial Exceptions** - Calculation & valuation errors
- **Integration Exceptions** - API & sync errors
- **Reporting Exceptions** - PDF/Excel generation errors
- **Validation Exceptions** - Data integrity errors

---

## 🔧 Development Workflow

### Getting Started
1. **Environment Setup**:
   ```bash
   cp .env.example .env
   # Edit .env with your configuration
   ```

2. **Docker Development**:
   ```bash
   docker-compose up --build
   ```

3. **Database Setup**:
   ```bash
   docker-compose exec web alembic upgrade head
   ```

4. **Run Tests**:
   ```bash
   docker-compose exec web pytest
   ```

### Development Tools
- **Database Migrations**: `alembic revision --autogenerate`
- **Code Formatting**: Automatic with Black (88 chars)
- **Type Checking**: MyPy configuration available
- **Test Coverage**: HTML reports generated in htmlcov/

---

## 📚 External Resources

### API Documentation
- **Xero API Documentation**: https://developer.xero.com/documentation/
- **FastAPI Documentation**: https://fastapi.tiangolo.com/
- **Pydantic Documentation**: https://pydantic-docs.helpmanual.io/

### Financial Standards
- **GAAP Guidelines**: Generally Accepted Accounting Principles
- **NASDAQ Reporting**: Public company reporting standards
- **SaaS Metrics**: Industry-standard KPI calculations

### Development Tools
- **SQLAlchemy ORM**: https://docs.sqlalchemy.org/
- **Celery Background Tasks**: https://docs.celeryproject.org/
- **Pytest Testing**: https://docs.pytest.org/

---

## 🆘 Getting Help

### Documentation Hierarchy
1. **Quick Reference** - Check PROJECT_INDEX.md first
2. **Specific APIs** - Use interactive docs at /docs
3. **CLI Commands** - Use --help flag with any command
4. **Development** - Check relevant module documentation
5. **Troubleshooting** - Review exception handling guide

### Debug Resources
- **Enhanced Error Messages** - All exceptions include context and recovery suggestions
- **Debug Mode** - Use `--debug` flag for detailed information
- **Health Checks** - Visit /health endpoints for system status
- **Logging** - Comprehensive logging with configurable levels

---

## 📋 Quick Navigation

| Need | Go To |
|------|-------|
| **Project Overview** | [PROJECT_INDEX.md](./PROJECT_INDEX.md) |
| **API Reference** | `http://localhost:8000/docs` |
| **CLI Commands** | `python -m mcx3d_finance.cli.main --help` |
| **Development Setup** | [DOCKER_DEPLOYMENT_GUIDE.md](./DOCKER_DEPLOYMENT_GUIDE.md) |
| **Performance Data** | [PERFORMANCE_BENCHMARK_REPORT.md](./PERFORMANCE_BENCHMARK_REPORT.md) |
| **Configuration** | `.env.example` + `config.yml` |
| **Testing** | `docker-compose exec web pytest` |
| **Code Structure** | Browse `mcx3d_finance/` directory |

---

*Documentation Last Updated: January 2025*

For questions or contributions, please refer to the development guidelines in CLAUDE.md.