"""
Comprehensive tests for the validation integration system.
Tests real-time validation, validation routing, policy management, and integration workflows.
"""

import pytest
import sys
import os
import asyncio
from datetime import datetime, timedelta
from unittest.mock import Mock, patch

# Add project root to path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '../..')))

from mcx3d_finance.core.validation_integration import (
    RealTimeValidator,
    ValidationRouter,
    IntegratedValidationEngine,
    ValidationPolicy,
    ValidationContext,
    ValidationTrigger,
    ValidationAction,
    DataRoute,
    ValidationRoutingResult
)
from mcx3d_finance.core.data_validation import (
    DataValidationEngine,
    ValidationResult,
    ValidationReport,
    ValidationSeverity,
    ValidationCategory
)


class TestRealTimeValidator:
    """Test real-time validation functionality."""
    
    def setup_method(self):
        """Set up test fixtures."""
        self.mock_validation_engine = Mock(spec=DataValidationEngine)
        self.validator = RealTimeValidator(self.mock_validation_engine)
    
    @pytest.mark.asyncio
    async def test_validate_stream_record_success(self):
        """Test successful real-time validation of a stream record."""
        # Mock validation engine response
        mock_result = ValidationResult(
            check_name="real_time_test",
            category=ValidationCategory.BUSINESS_RULES,
            severity=ValidationSeverity.INFO,
            passed=True,
            message="Validation passed",
            timestamp=datetime.utcnow()
        )
        
        mock_report = ValidationReport(
            organization_id="test_org",
            validation_timestamp=datetime.utcnow(),
            total_checks=1,
            passed_checks=1,
            failed_checks=0,
            results=[mock_result],
            summary={}
        )
        
        self.mock_validation_engine.validate_data.return_value = mock_report
        
        # Test record
        record = {
            "id": "txn_1",
            "amount": 100.50,
            "date": "2024-01-01",
            "description": "Test transaction"
        }
        
        context = ValidationContext(
            organization_id="test_org",
            data_type="transaction",
            trigger=ValidationTrigger.REAL_TIME
        )
        
        result = await self.validator.validate_stream_record(record, context)
        
        assert result.passed
        assert result.check_name == "real_time_test"
        assert result.severity == ValidationSeverity.INFO
    
    @pytest.mark.asyncio
    async def test_validate_stream_record_timeout(self):
        """Test real-time validation timeout handling."""
        # Mock validation engine to simulate timeout
        async def slow_validation(*args, **kwargs):
            await asyncio.sleep(10)  # Longer than 5 second timeout
            return Mock()
        
        with patch.object(self.validator, '_async_validate', side_effect=slow_validation):
            record = {"id": "txn_1", "amount": 100.50}
            context = ValidationContext(
                organization_id="test_org",
                data_type="transaction",
                trigger=ValidationTrigger.REAL_TIME
            )
            
            result = await self.validator.validate_stream_record(record, context)
            
            assert not result.passed
            assert result.check_name == "real_time_timeout"
            assert result.severity == ValidationSeverity.WARNING
    
    def test_cache_functionality(self):
        """Test validation result caching."""
        record = {"id": "txn_1", "amount": 100.50}
        context = ValidationContext(
            organization_id="test_org",
            data_type="transaction",
            trigger=ValidationTrigger.REAL_TIME
        )
        
        # Generate cache key
        cache_key = self.validator._generate_cache_key(record, context)
        assert isinstance(cache_key, str)
        
        # Test caching
        mock_result = ValidationResult(
            check_name="cached_test",
            category=ValidationCategory.BUSINESS_RULES,
            severity=ValidationSeverity.INFO,
            passed=True,
            message="Cached result",
            timestamp=datetime.utcnow()
        )
        
        self.validator._cache_validation_result(cache_key, mock_result)
        
        # Retrieve from cache
        cached_result = self.validator._get_cached_validation(cache_key)
        assert cached_result is not None
        assert cached_result.check_name == "cached_test"
    
    def test_cache_expiration(self):
        """Test cache expiration functionality."""
        cache_key = "test_key"
        mock_result = ValidationResult(
            check_name="expired_test",
            category=ValidationCategory.BUSINESS_RULES,
            severity=ValidationSeverity.INFO,
            passed=True,
            message="Expired result",
            timestamp=datetime.utcnow()
        )
        
        # Manually add expired cache entry
        self.validator.validation_cache[cache_key] = {
            'result': mock_result,
            'timestamp': datetime.utcnow() - timedelta(seconds=400)  # Expired
        }
        
        # Should return None for expired cache
        cached_result = self.validator._get_cached_validation(cache_key)
        assert cached_result is None
        assert cache_key not in self.validator.validation_cache


class TestValidationRouter:
    """Test validation routing functionality."""
    
    def setup_method(self):
        """Set up test fixtures."""
        self.router = ValidationRouter()
    
    def test_add_and_remove_policies(self):
        """Test adding and removing validation policies."""
        policy = ValidationPolicy(
            policy_id="test_policy",
            name="Test Policy",
            description="Test policy description",
            data_types=["transaction"],
            triggers=[ValidationTrigger.ON_INGESTION],
            severity_thresholds={
                ValidationSeverity.CRITICAL: ValidationAction.REJECT,
                ValidationSeverity.ERROR: ValidationAction.QUARANTINE
            },
            routing_rules={
                ValidationAction.REJECT: DataRoute.INVALID_QUEUE,
                ValidationAction.QUARANTINE: DataRoute.REVIEW_QUEUE
            }
        )
        
        # Add policy
        self.router.add_policy(policy)
        assert "test_policy" in self.router.policies
        
        # Remove policy
        success = self.router.remove_policy("test_policy")
        assert success
        assert "test_policy" not in self.router.policies
        
        # Try to remove non-existent policy
        success = self.router.remove_policy("non_existent")
        assert not success
    
    def test_route_data_with_policy(self):
        """Test data routing with applicable policy."""
        # Create policy
        policy = ValidationPolicy(
            policy_id="strict_policy",
            name="Strict Policy",
            description="Strict validation policy",
            data_types=["transaction"],
            triggers=[ValidationTrigger.ON_INGESTION],
            severity_thresholds={
                ValidationSeverity.CRITICAL: ValidationAction.REJECT,
                ValidationSeverity.ERROR: ValidationAction.QUARANTINE,
                ValidationSeverity.WARNING: ValidationAction.ACCEPT
            },
            routing_rules={
                ValidationAction.REJECT: DataRoute.INVALID_QUEUE,
                ValidationAction.QUARANTINE: DataRoute.REVIEW_QUEUE,
                ValidationAction.ACCEPT: DataRoute.VALID_QUEUE
            }
        )
        self.router.add_policy(policy)
        
        # Create validation report with error
        error_result = ValidationResult(
            check_name="test_error",
            category=ValidationCategory.BUSINESS_RULES,
            severity=ValidationSeverity.ERROR,
            passed=False,
            message="Test error",
            timestamp=datetime.utcnow()
        )
        
        validation_report = ValidationReport(
            organization_id="test_org",
            validation_timestamp=datetime.utcnow(),
            total_checks=1,
            passed_checks=0,
            failed_checks=1,
            results=[error_result],
            summary={}
        )
        
        context = ValidationContext(
            organization_id="test_org",
            data_type="transaction",
            trigger=ValidationTrigger.ON_INGESTION
        )
        
        # Route data
        result = self.router.route_data(validation_report, context)
        
        assert result.action == ValidationAction.QUARANTINE
        assert result.route == DataRoute.REVIEW_QUEUE
        assert result.policy_applied == "strict_policy"
    
    def test_route_data_default_policy(self):
        """Test data routing with default policy when no policy matches."""
        # Create validation report with critical error
        critical_result = ValidationResult(
            check_name="critical_error",
            category=ValidationCategory.BUSINESS_RULES,
            severity=ValidationSeverity.CRITICAL,
            passed=False,
            message="Critical error",
            timestamp=datetime.utcnow()
        )
        
        validation_report = ValidationReport(
            organization_id="test_org",
            validation_timestamp=datetime.utcnow(),
            total_checks=1,
            passed_checks=0,
            failed_checks=1,
            results=[critical_result],
            summary={}
        )
        
        context = ValidationContext(
            organization_id="test_org",
            data_type="unknown_type",  # No policy for this type
            trigger=ValidationTrigger.ON_INGESTION
        )
        
        # Route data
        result = self.router.route_data(validation_report, context)
        
        assert result.action == ValidationAction.REJECT
        assert result.route == DataRoute.INVALID_QUEUE
        assert result.policy_applied == "default"
    
    def test_routing_statistics(self):
        """Test routing statistics collection."""
        # Initial stats
        stats = self.router.get_routing_statistics()
        assert stats['total_routed'] == 0
        
        # Create and route some data
        validation_report = ValidationReport(
            organization_id="test_org",
            validation_timestamp=datetime.utcnow(),
            total_checks=1,
            passed_checks=1,
            failed_checks=0,
            results=[],
            summary={}
        )
        
        context = ValidationContext(
            organization_id="test_org",
            data_type="transaction",
            trigger=ValidationTrigger.ON_INGESTION
        )
        
        # Route multiple times
        for _ in range(3):
            self.router.route_data(validation_report, context)
        
        # Check updated stats
        stats = self.router.get_routing_statistics()
        assert stats['total_routed'] == 3
        assert stats['route_distribution'][DataRoute.VALID_QUEUE.value] == 3


class TestIntegratedValidationEngine:
    """Test integrated validation engine."""
    
    def setup_method(self):
        """Set up test fixtures."""
        self.mock_validation_engine = Mock(spec=DataValidationEngine)
        self.engine = IntegratedValidationEngine(self.mock_validation_engine)
    
    @pytest.mark.asyncio
    async def test_validate_and_route_data_batch(self):
        """Test batch validation and routing."""
        # Mock validation engine response
        mock_result = ValidationResult(
            check_name="batch_test",
            category=ValidationCategory.BUSINESS_RULES,
            severity=ValidationSeverity.INFO,
            passed=True,
            message="Batch validation passed",
            timestamp=datetime.utcnow()
        )
        
        mock_report = ValidationReport(
            organization_id="test_org",
            validation_timestamp=datetime.utcnow(),
            total_checks=1,
            passed_checks=1,
            failed_checks=0,
            results=[mock_result],
            summary={}
        )
        
        self.mock_validation_engine.validate_data.return_value = mock_report
        
        # Test data
        data = {
            "transactions": [
                {"id": "txn_1", "amount": 100.50},
                {"id": "txn_2", "amount": 200.75}
            ]
        }
        
        context = ValidationContext(
            organization_id="test_org",
            data_type="mixed",
            trigger=ValidationTrigger.ON_INGESTION
        )
        
        result = await self.engine.validate_and_route_data(data, context)
        
        assert result.action == ValidationAction.ACCEPT
        assert result.route == DataRoute.VALID_QUEUE
        assert result.validation_report.passed_checks == 1
    
    @pytest.mark.asyncio
    async def test_validate_and_route_data_real_time(self):
        """Test real-time validation and routing."""
        # Test data
        data = {
            "transactions": [
                {"id": "txn_1", "amount": 100.50, "date": "2024-01-01"}
            ]
        }
        
        context = ValidationContext(
            organization_id="test_org",
            data_type="mixed",
            trigger=ValidationTrigger.REAL_TIME
        )
        
        result = await self.engine.validate_and_route_data(data, context)
        
        # Should use real-time validation path
        assert isinstance(result, ValidationRoutingResult)
        assert result.validation_report.organization_id == "test_org"
    
    def test_processing_statistics(self):
        """Test processing statistics collection."""
        stats = self.engine.get_processing_statistics()
        
        assert 'processing_stats' in stats
        assert 'routing_stats' in stats
        assert 'cache_stats' in stats
        assert 'policy_stats' in stats
        
        # Check initial values
        assert stats['processing_stats']['total_processed'] == 0
        assert stats['policy_stats']['active_policies'] >= 2  # Default policies


if __name__ == "__main__":
    pytest.main([__file__])
