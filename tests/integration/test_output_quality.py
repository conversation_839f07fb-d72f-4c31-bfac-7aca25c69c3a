# tests/integration/test_output_quality.py

import os
import tempfile
import shutil
import pytest
import re
from pathlib import Path
from decimal import Decimal
from unittest.mock import Mock

try:
    import PyPDF2
    from PyPDF2 import PdfReader
except ImportError:
    PyPDF2 = None
    PdfReader = None

try:
    from openpyxl import load_workbook
    from openpyxl.styles import Font, PatternFill
except ImportError:
    load_workbook = None

from mcx3d_finance.reporting.generator import ReportGenerator
from mcx3d_finance.core.valuation.dcf import DCFValuation
from mcx3d_finance.core.valuation.saas_valuation import SaaSValuation


@pytest.mark.integration
@pytest.mark.pdf
@pytest.mark.excel
class TestOutputQuality:
    """
    Tests for validating the quality and accuracy of generated PDF and Excel reports.
    Focuses on structure, content accuracy, and professional formatting.
    """
    
    @pytest.fixture
    def temp_output_dir(self):
        """Create a temporary directory for test outputs."""
        temp_dir = tempfile.mkdtemp(prefix="mcx3d_quality_test_")
        yield temp_dir
        shutil.rmtree(temp_dir, ignore_errors=True)
    
    @pytest.fixture
    def report_generator(self):
        """Create a ReportGenerator instance for testing."""
        return ReportGenerator()
    
    @pytest.fixture
    def comprehensive_dcf_data(self):
        """Detailed DCF test data for quality validation."""
        return {
            "company_name": "Quality Validation Corp",
            "valuation_date": "2024-01-15",
            "currency": "USD",
            "industry": "Technology",
            "projections": {
                "revenue": [
                    Decimal("3500000"),   # $3.5M
                    Decimal("4550000"),   # $4.55M (30% growth)
                    Decimal("5460000"),   # $5.46M (20% growth)
                    Decimal("6552000"),   # $6.552M (20% growth)
                    Decimal("7862400")    # $7.862M (20% growth)
                ],
                "operating_expenses": [
                    Decimal("2450000"),   # 70% of revenue
                    Decimal("2730000"),   # 60% of revenue
                    Decimal("3003000"),   # 55% of revenue
                    Decimal("3276000"),   # 50% of revenue
                    Decimal("3538032")    # 45% of revenue
                ],
                "capex": [
                    Decimal("175000"),    # 5% of revenue
                    Decimal("227500"),    # 5% of revenue
                    Decimal("273000"),    # 5% of revenue
                    Decimal("327600"),    # 5% of revenue
                    Decimal("393120")     # 5% of revenue
                ],
                "depreciation": [
                    Decimal("140000"),
                    Decimal("170000"),
                    Decimal("200000"),
                    Decimal("240000"),
                    Decimal("285000")
                ],
                "working_capital_changes": [
                    Decimal("70000"),
                    Decimal("91000"),
                    Decimal("109200"),
                    Decimal("131040"),
                    Decimal("157248")
                ]
            },
            "assumptions": {
                "discount_rate": Decimal("0.115"),     # 11.5% WACC
                "terminal_growth": Decimal("0.03"),    # 3% terminal growth
                "tax_rate": Decimal("0.25"),           # 25% corporate tax
                "years": 5
            },
            "market_data": {
                "risk_free_rate": Decimal("0.04"),
                "market_risk_premium": Decimal("0.06"),
                "beta": Decimal("1.25"),
                "debt_to_equity": Decimal("0.30")
            }
        }
    
    @pytest.fixture
    def comprehensive_saas_data(self):
        """Detailed SaaS test data for quality validation."""
        return {
            "company_name": "SaaS Quality Inc",
            "valuation_date": "2024-01-15",
            "currency": "USD",
            "metrics": {
                "arr": [
                    Decimal("2400000"),   # $2.4M ARR
                    Decimal("3600000"),   # $3.6M ARR (50% growth)
                    Decimal("5040000"),   # $5.04M ARR (40% growth)
                    Decimal("6552000"),   # $6.552M ARR (30% growth)
                    Decimal("8192000")    # $8.192M ARR (25% growth)
                ],
                "monthly_churn_rate": [0.045, 0.038, 0.032, 0.028, 0.025],
                "customer_acquisition_cost": Decimal("220"),
                "average_revenue_per_user": Decimal("580"),
                "gross_margin": Decimal("0.87"),
                "customer_lifetime_value": Decimal("2900"),
                "net_revenue_retention": Decimal("1.18"),
                "magic_number": Decimal("1.35")
            },
            "assumptions": {
                "discount_rate": Decimal("0.14"),      # 14% for SaaS
                "terminal_multiple": Decimal("9.5"),   # 9.5x ARR terminal
                "growth_efficiency": Decimal("0.75")
            },
            "benchmarks": {
                "revenue_multiple_range": (8, 15),
                "growth_rate_median": Decimal("0.40"),
                "churn_rate_benchmark": Decimal("0.035"),
                "cac_payback_benchmark": 10  # months
            }
        }

    @pytest.mark.skipif(PyPDF2 is None, reason="PyPDF2 not available")
    @pytest.mark.pdf
    def test_pdf_structure_and_content(self, report_generator, comprehensive_dcf_data, temp_output_dir):
        """Validate PDF structure, page count, and content extraction."""
        # Generate DCF PDF report
        output_path = Path(temp_output_dir) / "quality_dcf.pdf"
        
        dcf_valuation = DCFValuation(
            projections=comprehensive_dcf_data["projections"],
            assumptions=comprehensive_dcf_data["assumptions"]
        )
        dcf_result = dcf_valuation.calculate()
        
        result_path = report_generator.generate_dcf_valuation_pdf(
            dcf_result=dcf_result,
            company_data=comprehensive_dcf_data,
            output_path=str(output_path)
        )
        
        # Validate PDF structure using PyPDF2
        with open(result_path, 'rb') as pdf_file:
            pdf_reader = PdfReader(pdf_file)
            
            # Basic structure validation
            assert len(pdf_reader.pages) >= 3, f"PDF should have at least 3 pages, got {len(pdf_reader.pages)}"
            assert len(pdf_reader.pages) <= 10, f"PDF should not exceed 10 pages, got {len(pdf_reader.pages)}"
            
            # Extract text from all pages for content validation
            full_text = ""
            for page_num, page in enumerate(pdf_reader.pages):
                try:
                    page_text = page.extract_text()
                    full_text += page_text
                    
                    # Each page should have content
                    assert len(page_text.strip()) > 100, f"Page {page_num + 1} has insufficient content"
                    
                except Exception as e:
                    pytest.fail(f"Failed to extract text from page {page_num + 1}: {e}")
            
            # Content validation - check for key elements
            content_checks = {
                "Company name": comprehensive_dcf_data["company_name"] in full_text,
                "Valuation date": "2024" in full_text,
                "Revenue projections": any(str(rev)[:4] in full_text for rev in comprehensive_dcf_data["projections"]["revenue"]),
                "Discount rate": "11.5" in full_text or "0.115" in full_text,
                "Terminal growth": "3.0" in full_text or "0.03" in full_text,
                "DCF Analysis": "DCF" in full_text or "Discounted Cash Flow" in full_text,
                "Present Value": "Present Value" in full_text or "PV" in full_text,
                "Valuation Summary": "Valuation" in full_text and "Summary" in full_text
            }
            
            for check_name, check_result in content_checks.items():
                assert check_result, f"Missing expected content: {check_name}"
            
            # Validate financial formatting (currency symbols, proper number formatting)
            currency_patterns = [
                r'\$[\d,]+',          # $1,000
                r'[\d,]+\.\d{2}',     # 1,000.00
                r'\$[\d,]+\.?\d*[KMB]' # $1.2M, $500K, etc.
            ]
            
            found_currency_format = False
            for pattern in currency_patterns:
                if re.search(pattern, full_text):
                    found_currency_format = True
                    break
            
            assert found_currency_format, "No properly formatted currency values found in PDF"
            
            # Validate professional formatting indicators
            professional_indicators = [
                "Executive Summary",
                "Financial Projections",
                "Valuation Analysis",
                "Assumptions",
                "Methodology"
            ]
            
            found_indicators = [indicator for indicator in professional_indicators if indicator in full_text]
            assert len(found_indicators) >= 3, f"Insufficient professional formatting indicators: {found_indicators}"

    @pytest.mark.skipif(load_workbook is None, reason="openpyxl not available")
    @pytest.mark.excel
    def test_excel_workbook_validation(self, report_generator, comprehensive_dcf_data, temp_output_dir):
        """Validate Excel sheets, formulas, formatting, and data integrity."""
        # Generate DCF Excel report
        output_path = Path(temp_output_dir) / "quality_dcf.xlsx"
        
        dcf_valuation = DCFValuation(
            projections=comprehensive_dcf_data["projections"],
            assumptions=comprehensive_dcf_data["assumptions"]
        )
        dcf_result = dcf_valuation.calculate()
        
        result_path = report_generator.generate_dcf_valuation_excel(
            dcf_result=dcf_result,
            company_data=comprehensive_dcf_data,
            output_path=str(output_path)
        )
        
        # Load and validate workbook
        wb = load_workbook(result_path)
        
        # Validate required sheets
        expected_sheets = [
            "Executive Summary",
            "Financial Projections", 
            "DCF Calculation",
            "Sensitivity Analysis",
            "Assumptions"
        ]
        
        for sheet_name in expected_sheets:
            assert sheet_name in wb.sheetnames, f"Missing required sheet: {sheet_name}"
        
        # Detailed validation of Executive Summary sheet
        exec_sheet = wb["Executive Summary"]
        
        # Sheet should have substantial content
        assert exec_sheet.max_row >= 15, f"Executive Summary has too few rows: {exec_sheet.max_row}"
        assert exec_sheet.max_column >= 3, f"Executive Summary has too few columns: {exec_sheet.max_column}"
        
        # Check for company name in the sheet
        company_name_found = False
        for row in exec_sheet.iter_rows(max_row=10, max_col=5):
            for cell in row:
                if cell.value and comprehensive_dcf_data["company_name"] in str(cell.value):
                    company_name_found = True
                    break
            if company_name_found:
                break
        
        assert company_name_found, "Company name not found in Executive Summary"
        
        # Validate Financial Projections sheet
        proj_sheet = wb["Financial Projections"]
        
        # Should have revenue projections
        revenue_values_found = []
        for row in proj_sheet.iter_rows(min_row=1, max_row=50):
            for cell in row:
                if cell.value and isinstance(cell.value, (int, float)):
                    # Check if this looks like one of our revenue values
                    cell_value = float(cell.value)
                    for revenue in comprehensive_dcf_data["projections"]["revenue"]:
                        if abs(cell_value - float(revenue)) < 1000:  # Within $1K tolerance
                            revenue_values_found.append(cell_value)
        
        assert len(revenue_values_found) >= 3, f"Insufficient revenue values found in projections: {revenue_values_found}"
        
        # Validate DCF Calculation sheet has formulas
        dcf_sheet = wb["DCF Calculation"]
        
        formula_cells = []
        for row in dcf_sheet.iter_rows():
            for cell in row:
                if cell.value and isinstance(cell.value, str) and cell.value.startswith('='):
                    formula_cells.append(cell.coordinate)
        
        assert len(formula_cells) >= 5, f"DCF sheet should have multiple formulas, found {len(formula_cells)}"
        
        # Validate formatting - check for styled headers
        header_formatting_found = False
        for sheet_name in ["Executive Summary", "Financial Projections"]:
            sheet = wb[sheet_name]
            
            # Check first few rows for header formatting
            for row_num in range(1, 4):
                for col_num in range(1, 6):
                    cell = sheet.cell(row=row_num, column=col_num)
                    
                    if cell.value:
                        # Check for bold formatting or colored background
                        if (cell.font and cell.font.bold) or (cell.fill and cell.fill.patternType):
                            header_formatting_found = True
                            break
                
                if header_formatting_found:
                    break
            
            if header_formatting_found:
                break
        
        # Note: This assertion might be too strict depending on the implementation
        # assert header_formatting_found, "No header formatting found in Excel sheets"
        
        # Validate Assumptions sheet has key assumptions
        assumptions_sheet = wb["Assumptions"]
        
        assumption_values = []
        for row in assumptions_sheet.iter_rows():
            for cell in row:
                if cell.value and isinstance(cell.value, (int, float)):
                    assumption_values.append(float(cell.value))
        
        # Check that discount rate appears in assumptions
        discount_rate = float(comprehensive_dcf_data["assumptions"]["discount_rate"])
        discount_rate_found = any(abs(val - discount_rate) < 0.001 for val in assumption_values)
        
        assert discount_rate_found, f"Discount rate {discount_rate} not found in assumptions sheet"

    @pytest.mark.integration
    def test_chart_integration_in_reports(self, report_generator, comprehensive_saas_data, temp_output_dir):
        """
        Verify charts are embedded correctly in reports.
        This is a placeholder for when chart generation is implemented.
        """
        # Generate SaaS report which typically includes charts
        pdf_path = Path(temp_output_dir) / "saas_charts.pdf"
        excel_path = Path(temp_output_dir) / "saas_charts.xlsx"
        
        saas_valuation = SaaSValuation(
            metrics=comprehensive_saas_data["metrics"],
            assumptions=comprehensive_saas_data["assumptions"]
        )
        saas_result = saas_valuation.calculate()
        
        # Generate reports
        pdf_result = report_generator.generate_saas_valuation_pdf(
            saas_result=saas_result,
            company_data=comprehensive_saas_data,
            output_path=str(pdf_path)
        )
        
        excel_result = report_generator.generate_saas_valuation_excel(
            saas_result=saas_result,
            company_data=comprehensive_saas_data,
            output_path=str(excel_path)
        )
        
        # Basic validation that files were created
        assert os.path.exists(pdf_result), "SaaS PDF was not created"
        assert os.path.exists(excel_result), "SaaS Excel was not created"
        
        # TODO: Once chart generation is implemented, add:
        # 1. PDF chart validation using PyPDF2 or specialized libraries
        # 2. Excel chart validation using openpyxl chart detection
        # 3. Chart data accuracy validation
        # 4. Chart formatting and professional appearance validation
        
        # Placeholder assertions for chart integration
        pdf_size = os.path.getsize(pdf_result)
        excel_size = os.path.getsize(excel_result)
        
        # Files with charts should be larger (rough heuristic)
        assert pdf_size > 15000, f"PDF with charts should be larger than 15KB, got {pdf_size} bytes"
        assert excel_size > 8000, f"Excel with charts should be larger than 8KB, got {excel_size} bytes"
        
        # When charts are implemented, add specific chart validation:
        # - Revenue growth charts in SaaS reports
        # - Cash flow waterfall charts in DCF reports
        # - Sensitivity analysis heat maps
        # - Valuation multiple comparison charts

    @pytest.mark.integration
    def test_financial_data_accuracy(self, report_generator, comprehensive_dcf_data, temp_output_dir):
        """Cross-validate calculations between source data and generated reports."""
        # Generate both PDF and Excel reports for comparison
        pdf_path = Path(temp_output_dir) / "accuracy_dcf.pdf"
        excel_path = Path(temp_output_dir) / "accuracy_dcf.xlsx"
        
        dcf_valuation = DCFValuation(
            projections=comprehensive_dcf_data["projections"],
            assumptions=comprehensive_dcf_data["assumptions"]
        )
        dcf_result = dcf_valuation.calculate()
        
        # Generate reports
        pdf_result = report_generator.generate_dcf_valuation_pdf(
            dcf_result=dcf_result,
            company_data=comprehensive_dcf_data,
            output_path=str(pdf_path)
        )
        
        excel_result = report_generator.generate_dcf_valuation_excel(
            dcf_result=dcf_result,
            company_data=comprehensive_dcf_data,
            output_path=str(excel_path)
        )
        
        # Validate that reports were created
        assert os.path.exists(pdf_result), "DCF PDF was not created"
        assert os.path.exists(excel_result), "DCF Excel was not created"
        
        # Extract and validate key financial metrics from the calculation
        expected_metrics = {
            "total_revenue": sum(comprehensive_dcf_data["projections"]["revenue"]),
            "discount_rate": comprehensive_dcf_data["assumptions"]["discount_rate"],
            "terminal_growth": comprehensive_dcf_data["assumptions"]["terminal_growth"],
            "tax_rate": comprehensive_dcf_data["assumptions"]["tax_rate"],
            "enterprise_value": dcf_result.get("enterprise_value"),
            "present_value": dcf_result.get("present_value")
        }
        
        # Validate Excel contains accurate calculations
        if load_workbook:
            wb = load_workbook(excel_result)
            
            # Check Executive Summary for key metrics
            exec_sheet = wb["Executive Summary"]
            excel_values = []
            
            for row in exec_sheet.iter_rows():
                for cell in row:
                    if cell.value and isinstance(cell.value, (int, float)):
                        excel_values.append(float(cell.value))
            
            # Validate discount rate appears in Excel
            discount_rate_decimal = float(expected_metrics["discount_rate"])
            discount_rate_percentage = discount_rate_decimal * 100
            
            discount_rate_found = any(
                abs(val - discount_rate_decimal) < 0.001 or 
                abs(val - discount_rate_percentage) < 0.1 
                for val in excel_values
            )
            
            assert discount_rate_found, f"Discount rate not accurately reflected in Excel report"
            
            # Validate revenue projections accuracy
            revenue_matches = 0
            for expected_revenue in comprehensive_dcf_data["projections"]["revenue"]:
                expected_value = float(expected_revenue)
                for excel_value in excel_values:
                    if abs(excel_value - expected_value) < 1000:  # $1K tolerance
                        revenue_matches += 1
                        break
            
            assert revenue_matches >= 3, f"Insufficient revenue projection matches in Excel: {revenue_matches}"
        
        # PDF content accuracy validation (if PyPDF2 is available)
        if PyPDF2:
            with open(pdf_result, 'rb') as pdf_file:
                pdf_reader = PdfReader(pdf_file)
                full_text = ""
                
                for page in pdf_reader.pages:
                    full_text += page.extract_text()
                
                # Check for key financial metrics in PDF text
                discount_rate_percentage = float(expected_metrics["discount_rate"]) * 100
                
                # Format checks for discount rate (could appear as 11.5%, 0.115, etc.)
                discount_rate_formats = [
                    f"{discount_rate_percentage:.1f}%",
                    f"{discount_rate_percentage:.1f}",
                    f"{expected_metrics['discount_rate']:.3f}"
                ]
                
                discount_rate_in_pdf = any(format_str in full_text for format_str in discount_rate_formats)
                assert discount_rate_in_pdf, f"Discount rate not found in PDF content with formats: {discount_rate_formats}"
                
                # Check for terminal growth rate
                terminal_growth_percentage = float(expected_metrics["terminal_growth"]) * 100
                terminal_growth_formats = [
                    f"{terminal_growth_percentage:.1f}%",
                    f"{terminal_growth_percentage:.1f}",
                    f"{expected_metrics['terminal_growth']:.2f}"
                ]
                
                terminal_growth_in_pdf = any(format_str in full_text for format_str in terminal_growth_formats)
                assert terminal_growth_in_pdf, f"Terminal growth rate not found in PDF content"
        
        # Validate calculation consistency
        # The DCF valuation should produce consistent results
        assert dcf_result is not None, "DCF calculation returned no result"
        
        # Basic sanity checks on the results
        if "enterprise_value" in dcf_result:
            enterprise_value = dcf_result["enterprise_value"]
            assert enterprise_value > 0, f"Enterprise value should be positive: {enterprise_value}"
            
            # Enterprise value should be reasonable relative to revenue
            total_revenue = sum(float(rev) for rev in comprehensive_dcf_data["projections"]["revenue"])
            ev_revenue_multiple = float(enterprise_value) / total_revenue
            
            assert 0.1 < ev_revenue_multiple < 50, f"EV/Revenue multiple seems unreasonable: {ev_revenue_multiple:.2f}"
        
        # Validate that both reports contain consistent information
        assert os.path.getsize(pdf_result) > 12000, "PDF report seems too small for comprehensive content"
        assert os.path.getsize(excel_result) > 6000, "Excel report seems too small for comprehensive content"

    @pytest.mark.integration 
    def test_saas_metrics_accuracy(self, report_generator, comprehensive_saas_data, temp_output_dir):
        """Validate SaaS-specific metrics accuracy in reports."""
        # Generate SaaS reports
        pdf_path = Path(temp_output_dir) / "saas_accuracy.pdf"
        excel_path = Path(temp_output_dir) / "saas_accuracy.xlsx"
        
        saas_valuation = SaaSValuation(
            metrics=comprehensive_saas_data["metrics"],
            assumptions=comprehensive_saas_data["assumptions"]
        )
        saas_result = saas_valuation.calculate()
        
        # Generate reports
        pdf_result = report_generator.generate_saas_valuation_pdf(
            saas_result=saas_result,
            company_data=comprehensive_saas_data,
            output_path=str(pdf_path)
        )
        
        excel_result = report_generator.generate_saas_valuation_excel(
            saas_result=saas_result,
            company_data=comprehensive_saas_data,
            output_path=str(excel_path)
        )
        
        # Validate files were created
        assert os.path.exists(pdf_result), "SaaS PDF was not created"
        assert os.path.exists(excel_result), "SaaS Excel was not created"
        
        # Extract key SaaS metrics for validation
        expected_metrics = {
            "latest_arr": comprehensive_saas_data["metrics"]["arr"][-1],
            "cac": comprehensive_saas_data["metrics"]["customer_acquisition_cost"],
            "ltv": comprehensive_saas_data["metrics"]["customer_lifetime_value"],
            "gross_margin": comprehensive_saas_data["metrics"]["gross_margin"],
            "nrr": comprehensive_saas_data["metrics"]["net_revenue_retention"],
            "magic_number": comprehensive_saas_data["metrics"]["magic_number"]
        }
        
        # Validate Excel contains SaaS metrics
        if load_workbook:
            wb = load_workbook(excel_result)
            
            # Check for SaaS-specific sheets
            saas_sheets = ["SaaS Metrics", "Valuation Summary", "Benchmarks"]
            found_saas_sheets = [sheet for sheet in saas_sheets if sheet in wb.sheetnames]
            
            assert len(found_saas_sheets) >= 1, f"No SaaS-specific sheets found. Available: {wb.sheetnames}"
            
            # Extract all numeric values from sheets
            all_values = []
            for sheet_name in wb.sheetnames:
                sheet = wb[sheet_name]
                for row in sheet.iter_rows():
                    for cell in row:
                        if cell.value and isinstance(cell.value, (int, float)):
                            all_values.append(float(cell.value))
            
            # Check for key SaaS metrics in the data
            arr_found = any(abs(val - float(expected_metrics["latest_arr"])) < 10000 for val in all_values)
            assert arr_found, f"Latest ARR {expected_metrics['latest_arr']} not found in Excel"
            
            cac_found = any(abs(val - float(expected_metrics["cac"])) < 10 for val in all_values)
            assert cac_found, f"CAC {expected_metrics['cac']} not found in Excel"
        
        # Validate PDF contains SaaS terminology and metrics
        if PyPDF2:
            with open(pdf_result, 'rb') as pdf_file:
                pdf_reader = PdfReader(pdf_file)
                full_text = ""
                
                for page in pdf_reader.pages:
                    full_text += page.extract_text()
                
                # Check for SaaS-specific terminology
                saas_terms = [
                    "ARR", "Annual Recurring Revenue",
                    "CAC", "Customer Acquisition",
                    "LTV", "Lifetime Value",
                    "Churn", "Retention",
                    "Magic Number"
                ]
                
                found_terms = [term for term in saas_terms if term in full_text]
                assert len(found_terms) >= 3, f"Insufficient SaaS terminology in PDF: {found_terms}"
                
                # Check for key metric values (allowing for formatting variations)
                arr_value = float(expected_metrics["latest_arr"])
                arr_formatted = [
                    f"${arr_value:,.0f}",
                    f"${arr_value/1000000:.1f}M",
                    f"{arr_value:,.0f}"
                ]
                
                arr_in_pdf = any(fmt in full_text for fmt in arr_formatted)
                # Note: This might be too strict depending on formatting
                # assert arr_in_pdf, f"ARR value not found in PDF with formats: {arr_formatted}"
        
        # Validate that SaaS reports are appropriately sized (different from DCF)
        pdf_size = os.path.getsize(pdf_result)
        excel_size = os.path.getsize(excel_result)
        
        assert pdf_size > 10000, f"SaaS PDF report too small: {pdf_size} bytes"
        assert excel_size > 5000, f"SaaS Excel report too small: {excel_size} bytes"
