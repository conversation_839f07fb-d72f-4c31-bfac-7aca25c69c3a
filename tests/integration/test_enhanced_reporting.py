"""
Integration tests for the enhanced reporting system.

Tests comprehensive functionality including error handling, graceful degradation,
resource monitoring, and fallback mechanisms.
"""

import pytest
import tempfile
import shutil
import os
from pathlib import Path
from unittest.mock import patch, MagicMock
from datetime import datetime, timedelta

from mcx3d_finance.reporting.enhanced_generator import (
    create_enhanced_generator,
    EnhancedReportGenerator
)
from mcx3d_finance.exceptions import (
    ReportGenerationError,
    ReportOutputError,
    ReportMemoryError,
    ValidationError
)
from mcx3d_finance.utils.graceful_degradation import ReportComplexity
from mcx3d_finance.utils.resource_monitor import ResourceMonitor


class TestEnhancedReportingIntegration:
    """Integration tests for enhanced reporting system."""
    
    @pytest.fixture
    def temp_output_dir(self):
        """Create temporary output directory."""
        temp_dir = tempfile.mkdtemp()
        yield temp_dir
        shutil.rmtree(temp_dir, ignore_errors=True)
    
    @pytest.fixture
    def sample_income_statement_data(self):
        """Sample income statement data for testing."""
        return {
            "header": {
                "organization_id": 123,
                "company_name": "Test Company Inc.",
                "statement_title": "INCOME STATEMENT",
                "reporting_date": "2023-12-31",
                "period_start": "2023-10-01",
                "period_end": "2023-12-31",
                "generated_at": datetime.now().isoformat(),
                "currency": "USD"
            },
            "total_revenue": 150000.00,
            "cost_of_goods_sold": 60000.00,
            "gross_profit": 90000.00,
            "operating_expenses": 45000.00,
            "operating_income": 45000.00,
            "interest_expense": 2000.00,
            "income_before_taxes": 43000.00,
            "income_tax_expense": 10750.00,
            "net_income": 32250.00,
            "earnings_per_share": {
                "basic": 3.23,
                "diluted": 3.20
            }
        }
    
    @pytest.fixture
    def sample_balance_sheet_data(self):
        """Sample balance sheet data for testing."""
        return {
            "header": {
                "organization_id": 123,
                "company_name": "Test Company Inc.",
                "statement_title": "BALANCE SHEET",
                "reporting_date": "2023-12-31",
                "generated_at": datetime.now().isoformat(),
                "currency": "USD"
            },
            "assets": {
                "current_assets": {
                    "cash": 25000.00,
                    "accounts_receivable": 15000.00,
                    "inventory": 20000.00,
                    "total_current_assets": 60000.00
                },
                "non_current_assets": {
                    "property_plant_equipment": 100000.00,
                    "intangible_assets": 25000.00,
                    "total_non_current_assets": 125000.00
                },
                "total_assets": 185000.00
            },
            "liabilities": {
                "current_liabilities": {
                    "accounts_payable": 12000.00,
                    "short_term_debt": 8000.00,
                    "total_current_liabilities": 20000.00
                },
                "non_current_liabilities": {
                    "long_term_debt": 40000.00,
                    "total_non_current_liabilities": 40000.00
                },
                "total_liabilities": 60000.00
            },
            "equity": {
                "common_stock": 50000.00,
                "retained_earnings": 75000.00,
                "total_equity": 125000.00
            },
            "total_liabilities_equity": 185000.00
        }
    
    @pytest.fixture
    def sample_dcf_data(self):
        """Sample DCF valuation data for testing."""
        return {
            "header": {
                "company_name": "Test Company Inc.",
                "valuation_date": "2023-12-31",
                "analyst": "Test Analyst",
                "currency": "USD"
            },
            "assumptions": {
                "discount_rate": 0.10,
                "terminal_growth_rate": 0.03,
                "tax_rate": 0.25,
                "forecast_years": 5
            },
            "projections": [
                {"year": 2024, "revenue": 1000000, "ebitda": 250000, "fcf": 200000},
                {"year": 2025, "revenue": 1100000, "ebitda": 280000, "fcf": 225000},
                {"year": 2026, "revenue": 1210000, "ebitda": 315000, "fcf": 255000},
                {"year": 2027, "revenue": 1331000, "ebitda": 355000, "fcf": 290000},
                {"year": 2028, "revenue": 1464100, "ebitda": 400000, "fcf": 330000}
            ],
            "valuation": {
                "present_value_fcf": 1045678,
                "terminal_value": 4714286,
                "enterprise_value": 5759964,
                "net_debt": 50000,
                "equity_value": 5709964,
                "shares_outstanding": 1000000,
                "value_per_share": 5.71
            }
        }
    
    def test_create_enhanced_generator_basic(self):
        """Test basic creation of enhanced generator."""
        generator = create_enhanced_generator()
        assert isinstance(generator, EnhancedReportGenerator)
        assert generator.resource_monitor is not None
        assert generator.degradation_manager is not None
        assert generator.validation_engine is not None
    
    def test_create_enhanced_generator_with_params(self):
        """Test enhanced generator creation with custom parameters."""
        generator = create_enhanced_generator(
            memory_threshold_mb=1024.0,
            disk_threshold_mb=2048.0,
            enable_validation=True,
            enable_resource_monitoring=True
        )
        
        assert generator.memory_threshold_mb == 1024.0
        assert generator.disk_threshold_mb == 2048.0
    
    def test_income_statement_generation_pdf(self, sample_income_statement_data, temp_output_dir):
        """Test income statement generation in PDF format."""
        generator = create_enhanced_generator()
        output_path = os.path.join(temp_output_dir, "income_statement.pdf")
        
        result = generator.generate_income_statement(
            income_statement_data=sample_income_statement_data,
            output_path=output_path,
            output_format="pdf"
        )
        
        assert result["success"] is True
        assert "output_path" in result
        assert result["format"] == "pdf"
        assert Path(result["output_path"]).exists()
        assert Path(result["output_path"]).stat().st_size > 0
    
    def test_income_statement_generation_excel(self, sample_income_statement_data, temp_output_dir):
        """Test income statement generation in Excel format."""
        generator = create_enhanced_generator()
        output_path = os.path.join(temp_output_dir, "income_statement.xlsx")
        
        result = generator.generate_income_statement(
            income_statement_data=sample_income_statement_data,
            output_path=output_path,
            output_format="excel"
        )
        
        assert result["success"] is True
        assert result["format"] == "excel"
        assert Path(result["output_path"]).exists()
    
    def test_balance_sheet_generation_pdf(self, sample_balance_sheet_data, temp_output_dir):
        """Test balance sheet generation in PDF format."""
        generator = create_enhanced_generator()
        output_path = os.path.join(temp_output_dir, "balance_sheet.pdf")
        
        result = generator.generate_balance_sheet(
            balance_sheet_data=sample_balance_sheet_data,
            output_path=output_path,
            output_format="pdf"
        )
        
        assert result["success"] is True
        assert result["format"] == "pdf"
        assert Path(result["output_path"]).exists()
    
    def test_dcf_valuation_generation_pdf(self, sample_dcf_data, temp_output_dir):
        """Test DCF valuation generation in PDF format."""
        generator = create_enhanced_generator()
        output_path = os.path.join(temp_output_dir, "dcf_valuation.pdf")
        
        result = generator.generate_dcf_valuation(
            dcf_data=sample_dcf_data,
            output_path=output_path,
            output_format="pdf"
        )
        
        assert result["success"] is True
        assert result["format"] == "pdf"
        assert Path(result["output_path"]).exists()
    
    def test_graceful_degradation_format_fallback(self, sample_income_statement_data, temp_output_dir):
        """Test graceful degradation with format fallback."""
        generator = create_enhanced_generator()
        
        # Mock PDF generation to fail
        with patch.object(generator.original_generator, 'generate_income_statement_pdf') as mock_pdf:
            mock_pdf.side_effect = ReportGenerationError("PDF generation failed")
            
            output_path = os.path.join(temp_output_dir, "income_statement.pdf")
            
            result = generator.generate_income_statement(
                income_statement_data=sample_income_statement_data,
                output_path=output_path,
                output_format="pdf"
            )
            
            # Should fallback to Excel format
            assert result["success"] is True
            assert result["format"] == "excel"  # Fallback format
            assert "degradation_applied" in result
            assert any("format fallback" in str(d).lower() for d in result["degradation_applied"])
    
    def test_complexity_degradation(self, sample_income_statement_data, temp_output_dir):
        """Test complexity-based degradation."""
        generator = create_enhanced_generator()
        output_path = os.path.join(temp_output_dir, "income_statement.pdf")
        
        # Test with minimal complexity
        result = generator.generate_income_statement(
            income_statement_data=sample_income_statement_data,
            output_path=output_path,
            output_format="pdf",
            complexity=ReportComplexity.MINIMAL
        )
        
        assert result["success"] is True
        assert result["complexity"] == ReportComplexity.MINIMAL
    
    def test_resource_monitoring_integration(self, sample_income_statement_data, temp_output_dir):
        """Test resource monitoring during report generation."""
        generator = create_enhanced_generator(enable_resource_monitoring=True)
        output_path = os.path.join(temp_output_dir, "income_statement.pdf")
        
        result = generator.generate_income_statement(
            income_statement_data=sample_income_statement_data,
            output_path=output_path,
            output_format="pdf"
        )
        
        assert result["success"] is True
        assert "performance_metrics" in result
        assert "generation_time_seconds" in result["performance_metrics"]
        assert "memory_usage_mb" in result["performance_metrics"]
    
    def test_validation_error_handling(self, temp_output_dir):
        """Test validation error handling with invalid data."""
        generator = create_enhanced_generator(enable_validation=True)
        output_path = os.path.join(temp_output_dir, "invalid_report.pdf")
        
        # Invalid data - missing required fields
        invalid_data = {"invalid": "data"}
        
        result = generator.generate_income_statement(
            income_statement_data=invalid_data,
            output_path=output_path,
            output_format="pdf"
        )
        
        assert result["success"] is False
        assert "errors" in result
        assert len(result["errors"]) > 0
        assert any("validation" in str(error).lower() for error in result["errors"])
    
    def test_file_permissions_error_handling(self, sample_income_statement_data):
        """Test file permissions error handling."""
        generator = create_enhanced_generator()
        
        # Try to write to a read-only location
        if os.name == 'posix':  # Unix-like systems
            readonly_path = "/proc/income_statement.pdf"  # proc is typically read-only
        else:  # Windows
            readonly_path = "C:\\Windows\\System32\\income_statement.pdf"
        
        result = generator.generate_income_statement(
            income_statement_data=sample_income_statement_data,
            output_path=readonly_path,
            output_format="pdf"
        )
        
        # Should either fail gracefully or fallback to temp directory
        if not result["success"]:
            assert "errors" in result
            assert any("permission" in str(error).lower() or "access" in str(error).lower() 
                      for error in result["errors"])
        else:
            # If successful, it should have fallen back to a writable location
            assert result["success"] is True
            assert "degradation_applied" in result
    
    def test_disk_space_monitoring(self, sample_income_statement_data, temp_output_dir):
        """Test disk space monitoring and warnings."""
        generator = create_enhanced_generator(disk_threshold_mb=1.0)  # Very low threshold
        output_path = os.path.join(temp_output_dir, "income_statement.pdf")
        
        result = generator.generate_income_statement(
            income_statement_data=sample_income_statement_data,
            output_path=output_path,
            output_format="pdf"
        )
        
        # Should either succeed with warnings or apply degradation
        if result["success"]:
            # Check if warnings were generated due to low disk space
            assert "warnings" in result or "degradation_applied" in result
    
    @patch('mcx3d_finance.utils.resource_monitor.psutil.virtual_memory')
    def test_memory_pressure_handling(self, mock_memory, sample_income_statement_data, temp_output_dir):
        """Test handling of memory pressure situations."""
        # Mock low memory situation
        mock_memory.return_value.percent = 95.0  # 95% memory usage
        mock_memory.return_value.available = 100 * 1024 * 1024  # 100MB available
        
        generator = create_enhanced_generator(memory_threshold_mb=200.0)
        output_path = os.path.join(temp_output_dir, "income_statement.pdf")
        
        result = generator.generate_income_statement(
            income_statement_data=sample_income_statement_data,
            output_path=output_path,
            output_format="pdf"
        )
        
        # Should handle memory pressure gracefully
        if result["success"]:
            # Should have applied some form of degradation or warnings
            assert "warnings" in result or "degradation_applied" in result
        else:
            # If failed, should provide clear error messaging
            assert "errors" in result
            assert any("memory" in str(error).lower() for error in result["errors"])
    
    def test_concurrent_report_generation(self, sample_income_statement_data, temp_output_dir):
        """Test concurrent report generation capabilities."""
        import threading
        import time
        
        generator = create_enhanced_generator()
        results = []
        
        def generate_report(report_id):
            output_path = os.path.join(temp_output_dir, f"income_statement_{report_id}.pdf")
            result = generator.generate_income_statement(
                income_statement_data=sample_income_statement_data,
                output_path=output_path,
                output_format="pdf"
            )
            results.append((report_id, result))
        
        # Start multiple threads
        threads = []
        for i in range(3):
            thread = threading.Thread(target=generate_report, args=(i,))
            threads.append(thread)
            thread.start()
        
        # Wait for all threads to complete
        for thread in threads:
            thread.join(timeout=30)  # 30 second timeout
        
        # Check results
        assert len(results) == 3
        for report_id, result in results:
            assert result["success"] is True
            assert Path(result["output_path"]).exists()
    
    def test_error_recovery_and_logging(self, sample_income_statement_data, temp_output_dir):
        """Test comprehensive error recovery and logging."""
        generator = create_enhanced_generator()
        
        # Mock a transient error that should trigger retry
        with patch.object(generator.original_generator, 'generate_income_statement_pdf') as mock_pdf:
            # First call fails, second succeeds
            mock_pdf.side_effect = [
                ReportGenerationError("Transient error", error_code="TEMP_FAIL"),
                MagicMock(return_value=b"PDF content")
            ]
            
            output_path = os.path.join(temp_output_dir, "income_statement.pdf")
            
            result = generator.generate_income_statement(
                income_statement_data=sample_income_statement_data,
                output_path=output_path,
                output_format="pdf"
            )
            
            # Should succeed after retry or fallback
            assert result["success"] is True
            assert "warnings" in result or "degradation_applied" in result
    
    def test_performance_metrics_collection(self, sample_income_statement_data, temp_output_dir):
        """Test performance metrics collection."""
        generator = create_enhanced_generator(enable_resource_monitoring=True)
        output_path = os.path.join(temp_output_dir, "income_statement.pdf")
        
        start_time = datetime.now()
        
        result = generator.generate_income_statement(
            income_statement_data=sample_income_statement_data,
            output_path=output_path,
            output_format="pdf"
        )
        
        end_time = datetime.now()
        generation_time = (end_time - start_time).total_seconds()
        
        assert result["success"] is True
        assert "performance_metrics" in result
        
        metrics = result["performance_metrics"]
        assert "generation_time_seconds" in metrics
        assert "memory_usage_mb" in metrics
        assert "peak_memory_mb" in metrics
        
        # Verify timing is reasonable
        assert metrics["generation_time_seconds"] <= generation_time + 1.0  # Allow 1s buffer
        assert metrics["memory_usage_mb"] > 0
    
    def test_comprehensive_system_integration(self, sample_income_statement_data, 
                                           sample_balance_sheet_data, sample_dcf_data, 
                                           temp_output_dir):
        """Test comprehensive system integration with multiple report types."""
        generator = create_enhanced_generator(
            enable_validation=True,
            enable_resource_monitoring=True
        )
        
        reports = [
            ("income_statement", sample_income_statement_data, "generate_income_statement"),
            ("balance_sheet", sample_balance_sheet_data, "generate_balance_sheet"),
            ("dcf_valuation", sample_dcf_data, "generate_dcf_valuation")
        ]
        
        results = []
        
        for report_type, data, method_name in reports:
            for format_type in ["pdf", "excel"]:
                output_path = os.path.join(temp_output_dir, f"{report_type}.{format_type}")
                
                method = getattr(generator, method_name)
                
                if method_name == "generate_income_statement":
                    result = method(
                        income_statement_data=data,
                        output_path=output_path,
                        output_format=format_type
                    )
                elif method_name == "generate_balance_sheet":
                    result = method(
                        balance_sheet_data=data,
                        output_path=output_path,
                        output_format=format_type
                    )
                elif method_name == "generate_dcf_valuation":
                    result = method(
                        dcf_data=data,
                        output_path=output_path,
                        output_format=format_type
                    )
                
                results.append((report_type, format_type, result))
        
        # Verify all reports generated successfully
        success_count = sum(1 for _, _, result in results if result["success"])
        total_count = len(results)
        
        # At least 80% should succeed (allowing for some format/type combinations that might not be implemented)
        assert success_count >= total_count * 0.8
        
        # Check that files were actually created for successful reports
        for report_type, format_type, result in results:
            if result["success"]:
                assert Path(result["output_path"]).exists()
                assert Path(result["output_path"]).stat().st_size > 0