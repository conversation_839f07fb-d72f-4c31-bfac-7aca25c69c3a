"""
Comprehensive integration tests for the complete data processing pipeline.
Tests end-to-end workflows, performance with 10K+ transactions, and all integrated components.
"""

import pytest
import sys
import os
import time
from datetime import datetime
from unittest.mock import patch

# Add project root to path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '../..')))

from mcx3d_finance.core.data_processors import XeroDataProcessor
from mcx3d_finance.core.duplicate_detector import ConfidenceLevel
from mcx3d_finance.core.transformation_engine import DataQualityLevel
from mcx3d_finance.core.account_mapper import IndustryType


class TestCompleteDataProcessingPipeline:
    """Test the complete integrated data processing pipeline."""
    
    def setup_method(self):
        """Set up test fixtures."""
        self.processor = XeroDataProcessor(industry=IndustryType.TECHNOLOGY)
        self.organization_id = "test_org_123"
    
    def test_end_to_end_processing_small_dataset(self):
        """Test end-to-end processing with a small dataset."""
        # Sample financial data
        financial_data = {
            "transactions": [
                {
                    "id": "txn_1",
                    "amount": 1500.00,
                    "date": "2024-01-15",
                    "description": "Software License Payment",
                    "account_id": "acc_1",
                    "contact_id": "contact_1",
                    "type": "SPEND"
                },
                {
                    "id": "txn_2",
                    "amount": 2500.00,
                    "date": "2024-01-16",
                    "description": "Consulting Services Revenue",
                    "account_id": "acc_2",
                    "contact_id": "contact_2",
                    "type": "RECEIVE"
                }
            ],
            "contacts": [
                {
                    "id": "contact_1",
                    "name": "TechVendor Inc",
                    "email": "<EMAIL>",
                    "phone": "555-0123"
                },
                {
                    "id": "contact_2",
                    "name": "Client Corp",
                    "email": "<EMAIL>",
                    "phone": "555-0456"
                }
            ],
            "accounts": [
                {
                    "id": "acc_1",
                    "code": "6000",
                    "name": "Software Expenses",
                    "type": "EXPENSE"
                },
                {
                    "id": "acc_2",
                    "code": "4000",
                    "name": "Consulting Revenue",
                    "type": "REVENUE"
                }
            ]
        }
        
        # Process through enhanced pipeline
        result = self.processor.process_with_enhanced_pipeline(
            financial_data,
            enable_transformations=True,
            enable_quality_scoring=True,
            enable_duplicate_detection=True,
            batch_size=100,
            parallel_processing=False  # Use sequential for small dataset
        )
        
        # Verify processing completed successfully
        assert "error" not in result
        assert "processing_metadata" in result
        assert result["processing_metadata"]["pipeline_version"] == "2.0"
        
        # Verify transformation results
        if "transformation_results" in result:
            for data_type, transform_result in result["transformation_results"].items():
                assert "status" in transform_result
                assert "processed_records" in transform_result
        
        # Verify quality reports
        if "quality_reports" in result:
            for data_type, quality_report in result["quality_reports"].items():
                assert "overall_score" in quality_report
                assert "quality_level" in quality_report
                assert quality_report["overall_score"] >= 0.0
        
        # Verify duplicate detection
        if "duplicate_detection_results" in result:
            duplicate_results = result["duplicate_detection_results"]
            assert "duplicates_detected" in duplicate_results
            assert "merges_performed" in duplicate_results
    
    @pytest.mark.asyncio
    async def test_integrated_validation_pipeline(self):
        """Test the integrated validation pipeline."""
        financial_data = {
            "transactions": [
                {
                    "id": "txn_1",
                    "amount": 1000.00,
                    "date": "2024-01-15",
                    "description": "Test Transaction",
                    "account_id": "acc_1"
                }
            ],
            "contacts": [
                {
                    "id": "contact_1",
                    "name": "Test Contact",
                    "email": "<EMAIL>"
                }
            ]
        }
        
        # Process with integrated validation
        result = await self.processor.process_with_integrated_validation(
            financial_data,
            self.organization_id,
            enable_real_time_validation=False,
            enable_transformation_validation=True,
            enable_sync_validation=True
        )
        
        # Verify validation pipeline completed
        assert "error" not in result
        assert "processing_metadata" in result
        assert result["processing_metadata"]["pipeline_version"] == "3.0_integrated_validation"
        
        # Verify validation steps
        assert "ingestion_validation" in result
        assert "transformation_validation" in result
        assert "sync_validation" in result
        assert "final_routing" in result
        
        # Check validation results
        ingestion = result["ingestion_validation"]
        assert "action" in ingestion
        assert "route" in ingestion
        assert "validation_passed" in ingestion
        assert "validation_failed" in ingestion
    
    def test_real_time_validation(self):
        """Test real-time validation of individual records."""
        record = {
            "id": "txn_realtime",
            "amount": 500.00,
            "date": "2024-01-15",
            "description": "Real-time Transaction",
            "account_id": "acc_1"
        }
        
        result = self.processor.validate_real_time_data(
            record, "transaction", self.organization_id
        )
        
        # Verify real-time validation result
        assert "validation_passed" in result
        assert "action" in result
        assert "route" in result
        assert "processing_time" in result
        assert "validation_details" in result
        
        # Check validation details
        details = result["validation_details"]
        assert "total_checks" in details
        assert "passed_checks" in details
        assert "failed_checks" in details
    
    def test_performance_with_large_dataset(self):
        """Test performance with 10K+ transactions."""
        # Generate large dataset
        transactions = []
        contacts = []
        accounts = []
        
        # Create accounts
        for i in range(100):
            accounts.append({
                "id": f"acc_{i}",
                "code": f"{4000 + i}",
                "name": f"Account {i}",
                "type": "REVENUE" if i % 2 == 0 else "EXPENSE"
            })
        
        # Create contacts
        for i in range(500):
            contacts.append({
                "id": f"contact_{i}",
                "name": f"Contact {i}",
                "email": f"contact{i}@example.com",
                "phone": f"555-{i:04d}"
            })
        
        # Create 10K+ transactions
        for i in range(10000):
            transactions.append({
                "id": f"txn_{i}",
                "amount": 100.00 + (i % 1000),
                "date": f"2024-01-{(i % 28) + 1:02d}",
                "description": f"Transaction {i % 100}",  # Create some duplicates
                "account_id": f"acc_{i % 100}",
                "contact_id": f"contact_{i % 500}",
                "type": "RECEIVE" if i % 2 == 0 else "SPEND"
            })
        
        financial_data = {
            "transactions": transactions,
            "contacts": contacts,
            "accounts": accounts
        }
        
        # Measure processing time
        start_time = time.time()
        
        result = self.processor.process_with_enhanced_pipeline(
            financial_data,
            enable_transformations=True,
            enable_quality_scoring=True,
            enable_duplicate_detection=True,
            batch_size=1000,
            parallel_processing=True
        )
        
        end_time = time.time()
        processing_time = end_time - start_time
        
        # Performance assertions
        assert processing_time < 120, f"Processing took {processing_time:.2f} seconds, which is too long for 10K+ records"
        
        # Verify processing completed successfully
        assert "error" not in result
        assert "final_data_stats" in result
        
        # Check that duplicate detection found some duplicates
        if "duplicate_detection_results" in result:
            duplicate_results = result["duplicate_detection_results"]
            total_duplicates = sum(
                stats.get("total_found", 0) 
                for stats in duplicate_results.get("duplicates_detected", {}).values()
            )
            assert total_duplicates > 0, "Should have found some duplicates in the test data"
        
        print(f"Successfully processed {len(transactions)} transactions in {processing_time:.2f} seconds")
        print(f"Processing rate: {len(transactions) / processing_time:.0f} transactions/second")
    
    def test_data_quality_scoring_comprehensive(self):
        """Test comprehensive data quality scoring."""
        # High quality data
        high_quality_data = [
            {
                "id": "1",
                "name": "John Smith",
                "email": "<EMAIL>",
                "phone": "555-1234",
                "amount": 100.50,
                "date": "2024-01-01"
            },
            {
                "id": "2",
                "name": "Jane Doe",
                "email": "<EMAIL>",
                "phone": "555-5678",
                "amount": 200.75,
                "date": "2024-01-02"
            }
        ]
        
        quality_report = self.processor.quality_scorer.calculate_quality_score(high_quality_data)
        
        assert quality_report["overall_score"] > 0.8
        assert quality_report["quality_level"] in [DataQualityLevel.EXCELLENT, DataQualityLevel.GOOD]
        assert "dimension_scores" in quality_report
        assert "recommendations" in quality_report
        
        # Poor quality data
        poor_quality_data = [
            {
                "id": "1",
                "name": "",  # Missing
                "email": "invalid-email",  # Invalid
                "phone": "123",  # Invalid
                "amount": "not-a-number",  # Invalid
                "date": "invalid-date"  # Invalid
            },
            {
                "id": "1",  # Duplicate ID
                "name": "Jane Doe",
                "email": None,  # Missing
                "phone": None,  # Missing
                "amount": None,  # Missing
                "date": None  # Missing
            }
        ]
        
        poor_quality_report = self.processor.quality_scorer.calculate_quality_score(poor_quality_data)
        
        assert poor_quality_report["overall_score"] < 0.7
        assert poor_quality_report["quality_level"] in [DataQualityLevel.POOR, DataQualityLevel.FAIR]
        assert len(poor_quality_report["recommendations"]) > 0
    
    def test_duplicate_detection_accuracy(self):
        """Test duplicate detection accuracy with known duplicates."""
        # Create data with known duplicates
        transactions = [
            # Exact duplicates
            {
                "id": "txn_1",
                "amount": 100.00,
                "date": "2024-01-01",
                "description": "Payment to vendor",
                "account_id": "acc_1"
            },
            {
                "id": "txn_2",
                "amount": 100.00,
                "date": "2024-01-01",
                "description": "Payment to vendor",
                "account_id": "acc_1"
            },
            # Near duplicates (fuzzy match)
            {
                "id": "txn_3",
                "amount": 100.01,  # Slightly different amount
                "date": "2024-01-01",
                "description": "Payment to vendor",
                "account_id": "acc_1"
            },
            # Not a duplicate
            {
                "id": "txn_4",
                "amount": 200.00,
                "date": "2024-01-02",
                "description": "Different payment",
                "account_id": "acc_2"
            }
        ]
        
        duplicates = self.processor.duplicate_detector.detect_duplicates(
            transactions, "transaction"
        )
        
        # Should find at least 2 duplicate pairs (exact and near match)
        assert len(duplicates) >= 1
        
        # Check confidence levels
        high_confidence_duplicates = [
            d for d in duplicates if d.confidence_level == ConfidenceLevel.HIGH
        ]
        assert len(high_confidence_duplicates) >= 1, "Should find at least one high-confidence duplicate"
    
    def test_transformation_rules_application(self):
        """Test transformation rules application."""
        # Test data with various formats that need transformation
        test_data = [
            {
                "id": "1",
                "email": "  <EMAIL>  ",  # Needs normalization
                "phone": "**********",  # Needs formatting
                "amount": "$1,234.56",  # Needs currency standardization
                "description": "  Multiple   spaces   here  "  # Needs text cleaning
            }
        ]
        
        # Process through transformation engine
        # First, let's check what transformation rules are available
        available_fields = list(test_data[0].keys()) if test_data else []
        applicable_rules = self.processor.transformation_engine.rule_engine.get_applicable_rules(
            "contact", available_fields
        )

        # If no rules are applicable, add a simple test rule
        if not applicable_rules:
            from mcx3d_finance.core.transformation_engine import TransformationRule, TransformationRuleType
            test_rule = TransformationRule(
                rule_id="test_email_rule",
                rule_type=TransformationRuleType.NORMALIZATION,
                name="Test Email Rule",
                description="Test email normalization",
                source_fields=["email"],
                target_fields=["email"],
                transformation_function="normalize_email",
                conditions={"data_type": "contact"}
            )
            self.processor.transformation_engine.add_transformation_rule(test_rule)

        batch_result = self.processor.transformation_engine.process_batch(
            test_data, "contact", batch_size=100, parallel=False
        )
        
        assert batch_result.total_records == 1
        assert batch_result.processed_records >= 1
        assert batch_result.overall_quality_score > 0
        
        # Check that transformations were applied
        assert len(batch_result.batch_results) > 0
        
        # Verify transformation success
        successful_transformations = [
            r for r in batch_result.batch_results 
            if r.status.value in ["success", "partial_success"]
        ]
        assert len(successful_transformations) > 0


if __name__ == "__main__":
    pytest.main([__file__])
