"""
Tests for authentication middleware.
"""
import pytest
from datetime import datetime, timedelta, timezone
from unittest.mock import Mo<PERSON>, patch, MagicMock
from jose import jwt, JW<PERSON>rror

from fastapi import HTTPException, Request
from fastapi.security import HTTPAuthorizationCredentials

from mcx3d_finance.api.auth_middleware import (
    create_access_token,
    verify_token,
    get_current_user,
    verify_organization_access,
    require_auth,
    require_organization_access,
    RateLimiter,
    AuthenticationError,
    AuthorizationError,
    add_security_headers,
    pwd_context,
    SECRET_KEY,
    ALGORITHM
)


class TestJWTFunctions:
    """Test JWT token creation and verification."""

    def test_create_access_token_default_expiry(self):
        """Test creating access token with default expiration."""
        data = {"sub": "user123", "email": "<EMAIL>"}
        token = create_access_token(data)
        
        # Decode and verify
        payload = jwt.decode(token, SECRET_KEY, algorithms=[ALGORITHM])
        
        assert payload["sub"] == "user123"
        assert payload["email"] == "<EMAIL>"
        assert "exp" in payload
        
        # Check expiration is approximately 30 minutes from now
        exp_time = datetime.fromtimestamp(payload["exp"], tz=timezone.utc)
        expected_exp = datetime.now(timezone.utc) + timedelta(minutes=30)
        assert abs((exp_time - expected_exp).total_seconds()) < 60  # Within 1 minute

    def test_create_access_token_custom_expiry(self):
        """Test creating access token with custom expiration."""
        data = {"sub": "user123"}
        expires_delta = timedelta(hours=1)
        token = create_access_token(data, expires_delta)
        
        payload = jwt.decode(token, SECRET_KEY, algorithms=[ALGORITHM])
        
        exp_time = datetime.fromtimestamp(payload["exp"], tz=timezone.utc)
        expected_exp = datetime.now(timezone.utc) + timedelta(hours=1)
        assert abs((exp_time - expected_exp).total_seconds()) < 60

    def test_verify_token_valid(self):
        """Test verifying a valid token."""
        data = {"sub": "user123", "email": "<EMAIL>"}
        token = create_access_token(data)
        
        payload = verify_token(token)
        
        assert payload["sub"] == "user123"
        assert payload["email"] == "<EMAIL>"

    def test_verify_token_invalid(self):
        """Test verifying an invalid token."""
        with pytest.raises(AuthenticationError) as exc_info:
            verify_token("invalid.token.here")
        
        assert exc_info.value.status_code == 401
        assert "Invalid authentication token" in exc_info.value.detail

    def test_verify_token_expired(self):
        """Test verifying an expired token."""
        data = {"sub": "user123"}
        # Create token that expires immediately
        expires_delta = timedelta(seconds=-1)
        
        payload = data.copy()
        payload["exp"] = datetime.now(timezone.utc) + expires_delta
        token = jwt.encode(payload, SECRET_KEY, algorithm=ALGORITHM)
        
        with pytest.raises(AuthenticationError) as exc_info:
            verify_token(token)
        
        assert exc_info.value.status_code == 401


class TestGetCurrentUser:
    """Test get_current_user dependency."""

    @pytest.mark.asyncio
    async def test_get_current_user_valid_token(self):
        """Test getting current user with valid token."""
        # Create valid token
        token_data = {
            "sub": "123",
            "email": "<EMAIL>",
            "organizations": [1, 2, 3]
        }
        token = create_access_token(token_data)
        
        # Mock credentials
        credentials = HTTPAuthorizationCredentials(
            scheme="Bearer",
            credentials=token
        )
        
        user = await get_current_user(credentials)
        
        assert user["user_id"] == "123"
        assert user["email"] == "<EMAIL>"
        assert user["organizations"] == [1, 2, 3]

    @pytest.mark.asyncio
    async def test_get_current_user_invalid_token(self):
        """Test getting current user with invalid token."""
        credentials = HTTPAuthorizationCredentials(
            scheme="Bearer",
            credentials="invalid.token"
        )
        
        with pytest.raises(AuthenticationError) as exc_info:
            await get_current_user(credentials)
        
        assert exc_info.value.status_code == 401
        assert "Could not validate credentials" in exc_info.value.detail

    @pytest.mark.asyncio
    async def test_get_current_user_missing_sub(self):
        """Test getting current user with token missing 'sub' claim."""
        # Create token without 'sub'
        token_data = {"email": "<EMAIL>"}
        token = jwt.encode(
            token_data,
            SECRET_KEY,
            algorithm=ALGORITHM
        )
        
        credentials = HTTPAuthorizationCredentials(
            scheme="Bearer",
            credentials=token
        )
        
        with pytest.raises(AuthenticationError) as exc_info:
            await get_current_user(credentials)
        
        assert exc_info.value.status_code == 401
        assert "Invalid token payload" in exc_info.value.detail


class TestVerifyOrganizationAccess:
    """Test organization access verification."""

    @pytest.mark.asyncio
    async def test_verify_organization_access_allowed(self):
        """Test verifying access to allowed organization."""
        current_user = {
            "user_id": "123",
            "organizations": [1, 2, 3]
        }
        
        # Should return True without raising exception
        result = await verify_organization_access(2, current_user)
        assert result is True

    @pytest.mark.asyncio
    async def test_verify_organization_access_denied(self):
        """Test verifying access to unauthorized organization."""
        current_user = {
            "user_id": "123",
            "organizations": [1, 2, 3]
        }
        
        with pytest.raises(AuthorizationError) as exc_info:
            await verify_organization_access(5, current_user)
        
        assert exc_info.value.status_code == 403
        assert "You do not have access to organization 5" in exc_info.value.detail

    @pytest.mark.asyncio
    async def test_verify_organization_access_no_orgs(self):
        """Test verifying access when user has no organizations."""
        current_user = {
            "user_id": "123",
            "organizations": []
        }
        
        with pytest.raises(AuthorizationError) as exc_info:
            await verify_organization_access(1, current_user)
        
        assert exc_info.value.status_code == 403


class TestDecorators:
    """Test authentication decorators."""

    @pytest.mark.asyncio
    async def test_require_auth_decorator(self):
        """Test require_auth decorator."""
        @require_auth
        async def protected_endpoint():
            return {"message": "Protected"}
        
        # The decorator itself doesn't add authentication logic,
        # it's just a marker for endpoints that need auth
        result = await protected_endpoint()
        assert result == {"message": "Protected"}

    @pytest.mark.asyncio
    async def test_require_organization_access_decorator(self):
        """Test require_organization_access decorator."""
        @require_organization_access
        async def org_endpoint(organization_id: int):
            return {"org_id": organization_id}
        
        result = await org_endpoint(organization_id=1)
        assert result == {"org_id": 1}


class TestRateLimiter:
    """Test rate limiting functionality."""

    @pytest.fixture
    def rate_limiter(self):
        """Create rate limiter instance."""
        return RateLimiter(calls=5, period=60)

    @pytest.mark.asyncio
    async def test_rate_limit_within_limit(self, rate_limiter):
        """Test requests within rate limit."""
        mock_request = Mock(spec=Request)
        current_user = {"user_id": "user123"}
        
        # Make requests within limit
        for _ in range(5):
            await rate_limiter.check_rate_limit(mock_request, current_user)
        
        # Should not raise exception

    @pytest.mark.asyncio
    async def test_rate_limit_exceeded(self, rate_limiter):
        """Test exceeding rate limit."""
        mock_request = Mock(spec=Request)
        current_user = {"user_id": "user123"}
        
        # Make requests up to limit
        for _ in range(5):
            await rate_limiter.check_rate_limit(mock_request, current_user)
        
        # Next request should exceed limit
        with pytest.raises(HTTPException) as exc_info:
            await rate_limiter.check_rate_limit(mock_request, current_user)
        
        assert exc_info.value.status_code == 429
        assert "Rate limit exceeded" in exc_info.value.detail

    @pytest.mark.asyncio
    async def test_rate_limit_different_users(self, rate_limiter):
        """Test rate limiting tracks different users separately."""
        mock_request = Mock(spec=Request)
        user1 = {"user_id": "user1"}
        user2 = {"user_id": "user2"}
        
        # User 1 makes 5 requests
        for _ in range(5):
            await rate_limiter.check_rate_limit(mock_request, user1)
        
        # User 2 should still be able to make requests
        await rate_limiter.check_rate_limit(mock_request, user2)

    @pytest.mark.asyncio
    async def test_rate_limit_cleanup(self, rate_limiter):
        """Test rate limiter cleans up old requests."""
        mock_request = Mock(spec=Request)
        current_user = {"user_id": "user123"}
        
        # Make a request
        await rate_limiter.check_rate_limit(mock_request, current_user)
        
        # Simulate time passing by modifying the stored timestamp
        if "user123" in rate_limiter.requests:
            # Make the request appear old
            old_time = datetime.now(timezone.utc) - timedelta(seconds=61)
            rate_limiter.requests["user123"] = [old_time]
        
        # Next request should work as old one is cleaned up
        await rate_limiter.check_rate_limit(mock_request, current_user)


class TestSecurityHeaders:
    """Test security headers middleware."""

    @pytest.mark.asyncio
    async def test_add_security_headers(self):
        """Test adding security headers to response."""
        mock_request = Mock(spec=Request)
        mock_response = Mock()
        mock_response.headers = {}
        
        async def mock_call_next(request):
            return mock_response
        
        response = await add_security_headers(mock_request, mock_call_next)
        
        # Verify security headers are added
        assert response.headers["X-Content-Type-Options"] == "nosniff"
        assert response.headers["X-Frame-Options"] == "DENY"
        assert response.headers["X-XSS-Protection"] == "1; mode=block"
        assert "Strict-Transport-Security" in response.headers
        assert response.headers["Content-Security-Policy"] == "default-src 'self'"


class TestPasswordContext:
    """Test password hashing context."""

    def test_password_context_exists(self):
        """Test password context is properly configured."""
        assert pwd_context is not None
        assert "bcrypt" in pwd_context.schemes()


class TestExceptionClasses:
    """Test custom exception classes."""

    def test_authentication_error(self):
        """Test AuthenticationError exception."""
        error = AuthenticationError("Test authentication error")
        assert error.status_code == 401
        assert error.detail == "Test authentication error"

    def test_authorization_error(self):
        """Test AuthorizationError exception."""
        error = AuthorizationError("Test authorization error")
        assert error.status_code == 403
        assert error.detail == "Test authorization error"