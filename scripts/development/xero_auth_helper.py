#!/usr/bin/env python3
"""
Xero OAuth Authorization Helper with Enhanced Error Handling
"""

import webbrowser
import sys
import os
from dotenv import load_dotenv
from mcx3d_finance.auth.xero_oauth import XeroAuthManager
from mcx3d_finance.core.config import get_xero_config

def validate_configuration():
    """Validate Xero OAuth configuration before starting."""
    print("🔍 Validating Xero OAuth Configuration...")
    
    config = get_xero_config()
    issues = []
    
    if not config.get("client_id") or config.get("client_id") == "YOUR_CLIENT_ID":
        issues.append("❌ XERO_CLIENT_ID is missing or not set")
        
    if not config.get("client_secret") or config.get("client_secret") == "YOUR_CLIENT_SECRET":
        issues.append("❌ XERO_CLIENT_SECRET is missing or not set")
        
    if not config.get("redirect_uri"):
        issues.append("❌ XERO_REDIRECT_URI is missing")
    elif not config.get("redirect_uri").startswith("http"):
        issues.append("❌ XERO_REDIRECT_URI must be a valid HTTP URL")
        
    if not config.get("scopes"):
        issues.append("❌ XERO_SCOPES is missing")
    elif "offline_access" not in config.get("scopes", ""):
        issues.append("⚠️  XERO_SCOPES should include 'offline_access' for token refresh")
        
    if issues:
        print("\n🚨 Configuration Issues Found:")
        for issue in issues:
            print(f"   {issue}")
        print("\n📝 To fix these issues:")
        print("   1. Check your .env file in the project root")
        print("   2. Ensure your Xero app is properly configured at https://developer.xero.com/app/manage")
        print("   3. Make sure the redirect URI matches exactly")
        print()
        return False
        
    print("✅ Configuration looks good!")
    return True

def print_troubleshooting_guide():
    """Print comprehensive troubleshooting guide."""
    print("\n🛠️  Troubleshooting Guide:")
    print("=" * 50)
    print("If authorization fails, check these common issues:")
    print()
    print("1. 🔗 Redirect URI Mismatch:")
    print("   - Xero app redirect URI: http://localhost:8000/auth/xero/callback")
    print("   - Environment variable: XERO_REDIRECT_URI")
    print("   - Both must match exactly")
    print()
    print("2. 🔑 Invalid Credentials:")
    print("   - Verify XERO_CLIENT_ID and XERO_CLIENT_SECRET in .env")
    print("   - Check your Xero app status at https://developer.xero.com/app/manage")
    print()
    print("3. 📋 Scope Issues:")
    print("   - Current scopes: accounting.transactions, accounting.contacts,")
    print("                     accounting.reports.read, accounting.settings, offline_access")
    print("   - Make sure your Xero app has these scopes enabled")
    print()
    print("4. 🌐 Network/Firewall:")
    print("   - Ensure localhost:8000 is accessible")
    print("   - Check if any firewall is blocking the callback")
    print()
    print("5. 🕐 State Token Issues:")
    print("   - State tokens expire after 10 minutes")
    print("   - If you see 'Invalid state parameter', try again")

def main():
    """Open Xero authorization in browser with enhanced error handling and validation."""
    
    print("🚀 MCX3D Financials - Xero OAuth Authorization")
    print("=" * 50)
    
    # Load environment
    if not load_dotenv():
        print("⚠️  No .env file found, using system environment variables")
    
    # Validate configuration first
    if not validate_configuration():
        print_troubleshooting_guide()
        sys.exit(1)
    
    try:
        # Generate auth URL
        auth_manager = XeroAuthManager()
        auth_url, state = auth_manager.generate_auth_url()
        
        if not auth_url:
            print("❌ Failed to generate authorization URL")
            print("   This usually indicates a configuration issue.")
            print_troubleshooting_guide()
            sys.exit(1)
            
        print(f"✅ Authorization URL Generated Successfully!")
        print(f"📋 State Token: {state}")
        print()
        print("🌐 Opening authorization page in your browser...")
        print(f"   URL: {auth_url}")
        print()
        
        # Open in browser
        try:
            webbrowser.open(auth_url)
        except Exception as browser_error:
            print(f"⚠️  Could not open browser automatically: {browser_error}")
            print("   Please copy and paste the URL above manually.")
        
        print("📖 Authorization Steps:")
        print("1. ✅ Log in to your Xero account")
        print("2. 🏢 Select the organization you want to connect")
        print("3. ✋ Click 'Authorize' to grant MCX3D Financials access")
        print("4. 🔄 You'll be redirected to the application")
        print()
        print("🎯 After successful authorization:")
        print("   - Organization data will be saved to the database")
        print("   - You can run: python test_data_import.py")
        print("   - Or start the API server: docker-compose up")
        print()
        print("🔗 Callback URL should be:")
        print("   http://localhost:8000/auth/xero/callback")
        print()
        print("🔒 PKCE Security Enhancement:")
        print("   - Code challenge generated with SHA256")
        print("   - Enhanced security for OAuth2 flow")
        print("   - Compliant with Xero's latest requirements")
        print()
        
    except KeyboardInterrupt:
        print("\n👋 Authorization cancelled by user")
        sys.exit(0)
    except Exception as e:
        print(f"❌ Error: {e}")
        print()
        print("🔍 Common solutions:")
        print("   1. Check your internet connection")
        print("   2. Verify your Xero app configuration")
        print("   3. Ensure Redis is running (for state management)")
        print("   4. Check the logs for more details")
        print_troubleshooting_guide()
        sys.exit(1)

if __name__ == "__main__":
    main()