#!/usr/bin/env python3
"""
Test script to verify all imports are working correctly.
"""

def test_imports():
    """Test all critical imports for the MCX3D Financial system."""
    
    try:
        # Test core financial modules
        print("✅ IncomeStatementGenerator import successful")
        
        print("✅ BalanceSheetGenerator import successful")
        
        print("✅ CashFlowGenerator import successful")
        
        # Test valuation modules
        print("✅ DCFValuation import successful")
        
        print("✅ MultiplesValuation import successful")
        
        # Test SaaS KPIs
        print("✅ SaaSKPICalculator import successful")
        
        # Test Xero integration
        print("✅ XeroClient import successful")
        
        print("✅ XeroAuthManager import successful")
        
        # Test database models
        print("✅ Database models import successful")
        
        # Test data processors
        print("✅ XeroDataProcessor import successful")
        
        print("\n🎉 All imports successful! The system is ready.")
        return True
        
    except ImportError as e:
        print(f"❌ Import error: {e}")
        return False
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        return False

if __name__ == "__main__":
    test_imports()
