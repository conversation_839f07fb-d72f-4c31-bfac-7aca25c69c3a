#!/usr/bin/env python3
"""
Fix critical errors that prevent Xero integration functionality
"""

import os
import re
from pathlib import Path

def fix_missing_methods():
    """Add missing methods to XeroDataProcessor"""
    
    processor_file = Path("mcx3d_finance/core/data_processors.py")
    
    # Check if missing methods exist
    with open(processor_file, 'r') as f:
        content = f.read()
    
    methods_to_add = []
    
    if 'def process_profit_loss_for_gaap' not in content:
        methods_to_add.append('''
    def process_profit_loss_for_gaap(self, profit_loss_data: Dict[str, Any]) -> Dict[str, Any]:
        """Process profit & loss data for GAAP compliance"""
        try:
            # Basic processing for now - can be enhanced later
            processed_data = {
                "report_date": profit_loss_data.get("report_date"),
                "sections": [],
                "gaap_compliant": True
            }
            
            for section in profit_loss_data.get("sections", []):
                processed_section = {
                    "section_name": section.get("section_name", ""),
                    "accounts": [],
                    "total": 0.0
                }
                
                for account in section.get("accounts", []):
                    processed_account = {
                        "account_name": account.get("account_name", ""),
                        "amount": float(account.get("amount", 0)),
                        "gaap_classification": self._classify_for_gaap(account.get("account_name", ""))
                    }
                    processed_section["accounts"].append(processed_account)
                    processed_section["total"] += processed_account["amount"]
                
                processed_data["sections"].append(processed_section)
            
            return processed_data
            
        except Exception as e:
            logger.error(f"Error processing profit & loss for GAAP: {e}")
            return profit_loss_data
            
    def _classify_for_gaap(self, account_name: str) -> str:
        """Basic GAAP classification helper"""
        account_lower = account_name.lower()
        
        if any(term in account_lower for term in ['revenue', 'sales', 'income']):
            return 'revenue'
        elif any(term in account_lower for term in ['expense', 'cost', 'depreciation']):
            return 'expense'
        else:
            return 'other'
''')
    
    if methods_to_add:
        # Add methods before the last class or at the end
        content = content.rstrip() + ''.join(methods_to_add) + '\n'
        
        with open(processor_file, 'w') as f:
            f.write(content)
        
        print(f"✅ Added {len(methods_to_add)} missing methods to XeroDataProcessor")
        return True
    
    print("✅ No missing methods found in XeroDataProcessor")
    return False

def fix_sync_tasks_validation():
    """Fix the validation call in sync_tasks.py"""
    
    sync_file = Path("mcx3d_finance/tasks/sync_tasks.py")
    
    with open(sync_file, 'r') as f:
        content = f.read()
    
    # Fix the validation call
    old_validation = """validation_results = data_processor.validation_engine.validate_financial_data(
                sync_results,
                enforce_gaap=True,
                custom_rules=[]
            )"""
    
    new_validation = """validation_results = data_processor.validation_engine.validate_data(
                organization.id,
                sync_results
            )"""
    
    if old_validation in content:
        content = content.replace(old_validation, new_validation)
        
        with open(sync_file, 'w') as f:
            f.write(content)
        
        print("✅ Fixed validation call in sync_tasks.py")
        return True
    
    print("✅ Validation call already correct in sync_tasks.py")
    return False

def main():
    print("🔧 Fixing Critical Xero Integration Issues...")
    
    changes_made = 0
    
    if fix_missing_methods():
        changes_made += 1
    
    if fix_sync_tasks_validation():
        changes_made += 1
    
    if changes_made > 0:
        print(f"✅ Fixed {changes_made} critical issues!")
        print("🔄 Restart the application to apply changes")
    else:
        print("✅ No critical issues found - system ready!")

if __name__ == "__main__":
    main()