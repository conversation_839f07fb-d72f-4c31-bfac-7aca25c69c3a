# Test Data Management System

This directory contains comprehensive test data management scripts for the MCX3D Financial System. These tools provide consistent, realistic test data generation and management capabilities for all testing scenarios.

## 🚀 Quick Start

### Basic Test Data Generation
```bash
# Generate basic test data with 3 organizations
python scripts/seed_test_data.py

# Generate data for specific scenario
python scripts/seed_test_data.py --scenario enterprise --organizations 5

# Save to custom file
python scripts/seed_test_data.py --output custom_test_data.json
```

### Test Data Management
```bash
# Create anonymized data for production testing
python scripts/manage_test_data.py anonymize --input test_seed_data.json --level high

# Generate performance dataset for load testing
python scripts/manage_test_data.py performance --size 5000 --scaling 20

# Validate data integrity
python scripts/manage_test_data.py validate --file test_seed_data.json
```

## 📁 Scripts Overview

### 1. `seed_test_data.py` - Test Data Generation

**Purpose**: Generates comprehensive, realistic test data for all testing scenarios.

**Key Features**:
- Multiple organization types and industries
- Complete chart of accounts with GAAP classification
- Realistic transaction patterns and amounts
- Valuation projects with proper assumptions
- Report generation history

**Usage Examples**:
```bash
# Basic usage - 3 organizations with standard data
python seed_test_data.py

# Large dataset for integration testing
python seed_test_data.py --organizations 10

# Specific scenarios
python seed_test_data.py --scenario minimal      # Single small company
python seed_test_data.py --scenario growth       # Growing SaaS company
python seed_test_data.py --scenario enterprise   # Multiple large companies
python seed_test_data.py --scenario valuation    # Valuation-heavy testing

# Dry run (generate without saving)
python seed_test_data.py --dry-run --organizations 5
```

**Generated Data Structure**:
```json
{
  "timestamp": "2024-01-20T10:30:00",
  "summary": {
    "organizations": 3,
    "accounts": 75,
    "transactions": 450,
    "valuation_projects": 15,
    "report_generations": 45
  },
  "data": {
    "organizations": [...],
    "accounts": [...],
    "transactions": [...],
    "valuation_projects": [...],
    "report_generations": [...]
  }
}
```

### 2. `manage_test_data.py` - Data Lifecycle Management

**Purpose**: Comprehensive test data lifecycle management including anonymization, cleanup, and validation.

**Key Features**:
- Multi-level data anonymization
- Performance dataset creation
- Data backup and restore
- Integrity validation
- Test scenario export

**Available Commands**:

#### Anonymize Data
```bash
# Basic anonymization
python manage_test_data.py anonymize --input test_data.json

# High-level anonymization for production
python manage_test_data.py anonymize --input test_data.json --level high --output prod_safe_data.json

# Anonymization levels:
# - low: Basic identifier replacement
# - medium: Identifier + financial amount obfuscation
# - high: Comprehensive data protection
```

#### Performance Datasets
```bash
# Create large dataset for performance testing
python manage_test_data.py performance --size 1000 --scaling 50

# Generate massive dataset for stress testing
python manage_test_data.py performance --size 10000 --scaling 100 --output stress_test_data.json
```

#### Data Validation
```bash
# Validate data integrity and structure
python manage_test_data.py validate --file test_data.json

# Example validation output:
# ✅ Validation completed:
#    Total records: 1,245
#    Data sections: 5/5
#    Integrity issues: 0
```

#### Backup & Restore
```bash
# Create backup of current test data
python manage_test_data.py backup --name "before_major_test"

# Restore from backup
python manage_test_data.py restore --backup backups/before_major_test.json
```

#### Test Scenarios Export
```bash
# Export predefined test scenarios
python manage_test_data.py scenarios --output test_scenarios.json
```

## 🎯 Test Scenarios

### Predefined Scenarios

1. **Minimal Company** (`--scenario minimal`)
   - Single small organization
   - Basic financial structure
   - 25 transactions
   - Perfect for unit testing

2. **Growth SaaS** (`--scenario growth`)
   - Technology company
   - Subscription revenue patterns
   - 200 transactions
   - SaaS-specific metrics

3. **Enterprise Multi** (`--scenario enterprise`)
   - 5 organizations
   - Multiple industries
   - Complex structures
   - 500+ transactions

4. **Valuation Heavy** (`--scenario valuation`)
   - Extensive valuation history
   - 10+ projects per organization
   - Multiple report formats
   - Complex financial projections

### Custom Scenario Creation

You can create custom scenarios by modifying the scenario definitions in `seed_test_data.py`:

```python
custom_scenarios = {
    "healthcare_focus": {
        "description": "Healthcare industry specific testing",
        "organization_count": 2,
        "industry": "Healthcare",
        "transaction_count": 300,
        "complexity": "medium"
    }
}
```

## 📊 Data Types and Structure

### Organizations
- Company information and metadata
- Industry classification
- Currency and regional settings
- Xero integration identifiers

### Chart of Accounts
- GAAP-compliant account structure
- Standard account codes and classifications
- Balance information
- Account type categorization

### Transactions
- Realistic transaction patterns
- Multiple transaction types (Invoice, Payment, Expense, etc.)
- Contact information
- Reconciliation status

### Valuation Projects
- DCF and SaaS valuation models
- Financial assumptions and projections
- Sensitivity analysis data
- Monte Carlo simulation results

### Report Generations
- Report generation history
- Multiple output formats (PDF, Excel, HTML)
- Performance metrics
- Error tracking

## 🔒 Data Anonymization

### Anonymization Levels

#### Low Level
- Replace obvious identifiers (company names, contact names)
- Maintain realistic data patterns
- Suitable for internal testing

#### Medium Level
- Identifier replacement + financial obfuscation
- 80-120% variation in amounts
- Suitable for shared development environments

#### High Level
- Comprehensive data protection
- 50-150% variation in amounts
- Replace all user references
- Suitable for production-like testing

### Example Anonymization
```bash
# Original
"company_name": "Acme Technologies Inc"
"contact_name": "John Smith"
"amount": "15000.00"

# Low level anonymization
"company_name": "Test Company 1"
"contact_name": "Contact abc123"
"amount": "15000.00"

# High level anonymization
"company_name": "Test Company 1"
"contact_name": "Contact def456"
"amount": "12750.00"  # 85% of original
```

## ⚡ Performance Testing

### Large Dataset Generation

The performance dataset generator creates high-volume data for load testing:

```bash
# Generate 50,000 transaction records
python manage_test_data.py performance --size 5000 --scaling 10

# Results in:
# - 5-10 organizations
# - 250+ accounts
# - 50,000+ transactions
# - 50+ valuation projects
# - 100+ reports
```

### Performance Benchmarks

Based on our testing, the system should handle:
- **Small datasets** (< 1,000 records): < 1 second
- **Medium datasets** (1,000-10,000 records): < 5 seconds  
- **Large datasets** (10,000-100,000 records): < 30 seconds
- **Performance datasets** (100,000+ records): < 2 minutes

## 🧪 Integration with Testing Framework

### pytest Integration

The test data scripts integrate seamlessly with our pytest-based testing framework:

```python
# In test files
import json
from pathlib import Path

@pytest.fixture
def sample_test_data():
    """Load generated test data for testing."""
    data_file = Path("test_seed_data.json")
    with open(data_file, 'r') as f:
        return json.load(f)

def test_with_realistic_data(sample_test_data):
    organizations = sample_test_data["data"]["organizations"]
    # Use realistic test data in tests
    assert len(organizations) > 0
```

### CI/CD Integration

Test data generation can be integrated into CI/CD pipelines:

```yaml
# In GitHub Actions workflow
- name: Generate test data
  run: |
    python scripts/seed_test_data.py --scenario enterprise
    python scripts/manage_test_data.py validate --file test_seed_data.json

- name: Run tests with generated data
  run: pytest tests/ -m integration
```

## 🛠 Advanced Usage

### Custom Data Generation

Extend the `TestDataSeeder` class for custom data generation:

```python
from scripts.seed_test_data import TestDataSeeder

class CustomSeeder(TestDataSeeder):
    def generate_industry_specific_data(self, industry):
        # Custom logic for industry-specific data
        pass

# Use custom seeder
seeder = CustomSeeder()
data = seeder.generate_organization_data(count=5)
```

### Data Validation Rules

Add custom validation rules to `TestDataManager`:

```python
def validate_saas_metrics(self, data):
    """Custom validation for SaaS-specific data."""
    for org in data.get("organizations", []):
        if org["industry"] == "Technology":
            # Validate SaaS-specific metrics
            pass
```

## 📈 Best Practices

### Test Data Management
1. **Use appropriate anonymization** for different environments
2. **Generate fresh data** for each major test cycle
3. **Validate data integrity** before running tests
4. **Clean up old data** regularly to maintain performance
5. **Document custom scenarios** for team consistency

### Performance Considerations
1. **Start with small datasets** for development
2. **Use performance datasets** only for load testing
3. **Monitor generation time** and optimize if needed
4. **Consider data archival** for long-term storage

### Security Guidelines
1. **Never use production data** for testing
2. **Always anonymize** data shared across teams
3. **Use high-level anonymization** for external environments
4. **Regularly audit** test data for sensitive information

## 🔧 Troubleshooting

### Common Issues

#### "No module named" Error
```bash
# Ensure you're in the project root
cd /path/to/mcx3d_financials/v2
python scripts/seed_test_data.py
```

#### Memory Issues with Large Datasets
```bash
# Reduce dataset size
python manage_test_data.py performance --size 1000 --scaling 5

# Or generate in chunks
python seed_test_data.py --organizations 2
```

#### Validation Failures
```bash
# Check data structure
python manage_test_data.py validate --file test_data.json

# Regenerate if needed
python seed_test_data.py --scenario minimal
```

### Performance Issues

If data generation is slow:
1. Reduce the number of organizations
2. Decrease transaction count per organization
3. Use simpler scenarios for development
4. Consider using pre-generated datasets

## 📚 Additional Resources

- [Testing Documentation](../tests/README.md)
- [Performance Baseline Guide](../docs/PERFORMANCE_TESTING.md)
- [CI/CD Pipeline Documentation](../.github/workflows/README.md)
- [Database Schema Documentation](../mcx3d_finance/db/README.md)

## 🤝 Contributing

When adding new test data types or scenarios:

1. Update the `TestDataSeeder` class in `seed_test_data.py`
2. Add corresponding anonymization logic in `manage_test_data.py`
3. Include validation rules for data integrity
4. Update this documentation
5. Add test scenarios to the predefined scenarios list

Example contribution:
```python
def generate_audit_trail_data(self, organization_id, audit_count=50):
    """Generate audit trail data for compliance testing."""
    # Implementation here
    pass
```