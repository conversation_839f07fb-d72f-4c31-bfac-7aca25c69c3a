#!/usr/bin/env python3
"""
Test Data Management Script for MCX3D Financial System.

This script provides utilities for managing test data lifecycle including:
- Data anonymization for production testing
- Test data cleanup and reset
- Data export/import for consistent testing
- Performance data for load testing
"""

import sys
import os
import json
import argparse
import shutil
import tempfile
from pathlib import Path
from datetime import datetime, timed<PERSON>ta
from typing import Dict, List, Any, Optional
import hashlib
import uuid
import random

# Add the project root to Python path
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))


class TestDataManager:
    """Comprehensive test data lifecycle management."""
    
    def __init__(self, data_directory: str = "test_data"):
        self.data_directory = Path(data_directory)
        self.data_directory.mkdir(exist_ok=True)
        self.backup_directory = self.data_directory / "backups"
        self.backup_directory.mkdir(exist_ok=True)
        self.anonymized_directory = self.data_directory / "anonymized"
        self.anonymized_directory.mkdir(exist_ok=True)
    
    def anonymize_data(self, source_data: Dict[str, Any], 
                      anonymization_level: str = "medium") -> Dict[str, Any]:
        """Anonymize sensitive data for production testing scenarios."""
        print(f"🔒 Anonymizing data with {anonymization_level} level protection...")
        
        anonymized = source_data.copy()
        
        # Anonymization mappings
        anonymization_functions = {
            "low": self._basic_anonymization,
            "medium": self._standard_anonymization,
            "high": self._strict_anonymization
        }
        
        if anonymization_level not in anonymization_functions:
            raise ValueError(f"Invalid anonymization level: {anonymization_level}")
        
        return anonymization_functions[anonymization_level](anonymized)
    
    def _basic_anonymization(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Basic anonymization - replace obvious identifiers."""
        anonymized = data.copy()
        
        # Anonymize organization data
        if 'organizations' in data.get('data', {}):
            for i, org in enumerate(data['data']['organizations']):
                org_data = org['data']
                org_data['name'] = f"Test Company {i+1}"
                org_data['xero_tenant_id'] = str(uuid.uuid4())
                
                # Keep industry and other non-sensitive data
        
        # Anonymize contact names in transactions
        if 'transactions' in data.get('data', {}):
            for transaction in data['data']['transactions']:
                trans_data = transaction['data']
                # Generate consistent fake contact name based on hash
                original_name = trans_data.get('contact_name', '')
                name_hash = hashlib.md5(original_name.encode()).hexdigest()[:6]
                trans_data['contact_name'] = f"Contact {name_hash}"
                trans_data['xero_transaction_id'] = str(uuid.uuid4())
        
        return anonymized
    
    def _standard_anonymization(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Standard anonymization - replace identifiers and modify financial amounts."""
        anonymized = self._basic_anonymization(data)
        
        # Add financial data obfuscation
        obfuscation_factor = random.uniform(0.8, 1.2)  # 80% to 120% of original
        
        # Anonymize account balances
        if 'accounts' in data.get('data', {}):
            for account in data['data']['accounts']:
                acc_data = account['data']
                if 'current_balance' in acc_data:
                    original_balance = float(acc_data['current_balance'])
                    acc_data['current_balance'] = str(original_balance * obfuscation_factor)
                acc_data['xero_account_id'] = str(uuid.uuid4())
        
        # Anonymize transaction amounts
        if 'transactions' in data.get('data', {}):
            for transaction in data['data']['transactions']:
                trans_data = transaction['data']
                if 'amount' in trans_data:
                    original_amount = float(trans_data['amount'])
                    trans_data['amount'] = str(original_amount * obfuscation_factor)
        
        # Anonymize valuation amounts
        if 'valuation_projects' in data.get('data', {}):
            for project in data['data']['valuation_projects']:
                proj_data = project['data']
                for value_field in ['enterprise_value', 'equity_value']:
                    if value_field in proj_data:
                        original_value = float(proj_data[value_field])
                        proj_data[value_field] = str(original_value * obfuscation_factor)
        
        return anonymized
    
    def _strict_anonymization(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Strict anonymization - comprehensive data protection."""
        anonymized = self._standard_anonymization(data)
        
        # Additional strict measures
        strict_obfuscation = random.uniform(0.5, 1.5)  # 50% to 150% variation
        
        # Anonymize all user references
        if 'valuation_projects' in data.get('data', {}):
            for project in data['data']['valuation_projects']:
                proj_data = project['data']
                proj_data['created_by'] = f"user_{random.randint(1000, 9999)}"
                
                # Modify assumptions significantly
                if 'assumptions' in proj_data:
                    assumptions = json.loads(proj_data['assumptions'])
                    for key, value in assumptions.items():
                        if isinstance(value, (int, float)):
                            assumptions[key] = round(float(value) * strict_obfuscation, 4)
                    proj_data['assumptions'] = json.dumps(assumptions)
        
        if 'report_generations' in data.get('data', {}):
            for report in data['data']['report_generations']:
                rep_data = report['data']
                rep_data['created_by'] = f"user_{random.randint(1000, 9999)}"
                rep_data['file_path'] = f"/anonymized/reports/report_{random.randint(10000, 99999)}.pdf"
        
        return anonymized
    
    def create_performance_dataset(self, 
                                 base_size: int = 1000,
                                 scaling_factor: int = 10) -> Dict[str, Any]:
        """Create large datasets for performance and load testing."""
        print(f"⚡ Creating performance dataset with {base_size * scaling_factor} records...")
        
        # Import the seeder for data generation
        from seed_test_data import TestDataSeeder
        
        seeder = TestDataSeeder()
        
        # Generate larger datasets
        performance_data = {
            "generation_timestamp": datetime.now().isoformat(),
            "dataset_type": "performance",
            "base_size": base_size,
            "scaling_factor": scaling_factor,
            "total_expected_records": base_size * scaling_factor,
            "data": {
                "organizations": [],
                "accounts": [],
                "transactions": [],
                "valuation_projects": [],
                "report_generations": []
            }
        }
        
        # Create multiple organizations for load testing
        org_count = max(1, scaling_factor // 2)
        org_data_list = seeder.generate_organization_data(org_count)
        
        for i, org_data in enumerate(org_data_list):
            org_id = i + 1
            performance_data["data"]["organizations"].append({
                'id': org_id,
                'data': org_data
            })
            
            # Generate large number of accounts
            account_data_list = seeder.generate_account_data(org_id)
            account_ids = []
            
            for j, account_data in enumerate(account_data_list):
                account_id = len(performance_data["data"]["accounts"]) + 1
                performance_data["data"]["accounts"].append({
                    'id': account_id,
                    'organization_id': org_id,
                    'data': account_data
                })
                account_ids.append(account_id)
            
            # Generate large number of transactions
            transaction_count = base_size * (scaling_factor // org_count)
            transaction_data_list = seeder.generate_transaction_data(
                org_id, account_ids, transaction_count
            )
            
            for transaction_data in transaction_data_list:
                transaction_id = len(performance_data["data"]["transactions"]) + 1
                performance_data["data"]["transactions"].append({
                    'id': transaction_id,
                    'organization_id': org_id,
                    'data': transaction_data
                })
            
            # Generate valuation projects
            project_count = scaling_factor // 2
            project_data_list = seeder.generate_valuation_project_data(org_id, project_count)
            project_ids = []
            
            for project_data in project_data_list:
                project_id = len(performance_data["data"]["valuation_projects"]) + 1
                performance_data["data"]["valuation_projects"].append({
                    'id': project_id,
                    'organization_id': org_id,
                    'data': project_data
                })
                project_ids.append(project_id)
            
            # Generate report generations
            report_count = scaling_factor
            report_data_list = seeder.generate_report_generation_data(
                org_id, project_ids, report_count
            )
            
            for report_data in report_data_list:
                report_id = len(performance_data["data"]["report_generations"]) + 1
                performance_data["data"]["report_generations"].append({
                    'id': report_id,
                    'organization_id': org_id,
                    'data': report_data
                })
        
        # Add performance summary
        actual_counts = {
            "organizations": len(performance_data["data"]["organizations"]),
            "accounts": len(performance_data["data"]["accounts"]),
            "transactions": len(performance_data["data"]["transactions"]),
            "valuation_projects": len(performance_data["data"]["valuation_projects"]),
            "report_generations": len(performance_data["data"]["report_generations"])
        }
        
        performance_data["actual_record_counts"] = actual_counts
        performance_data["total_actual_records"] = sum(actual_counts.values())
        
        print(f"   Generated {performance_data['total_actual_records']} total records")
        for record_type, count in actual_counts.items():
            print(f"   {record_type}: {count}")
        
        return performance_data
    
    def backup_current_data(self, backup_name: str = None) -> str:
        """Create backup of current test data."""
        if backup_name is None:
            backup_name = f"backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        
        backup_path = self.backup_directory / f"{backup_name}.json"
        
        print(f"💾 Creating backup: {backup_name}")
        
        # In a real implementation, this would backup actual database data
        # For now, we'll create a placeholder backup structure
        backup_data = {
            "backup_timestamp": datetime.now().isoformat(),
            "backup_name": backup_name,
            "data_sources": [
                "organizations",
                "accounts", 
                "transactions",
                "valuation_projects",
                "report_generations"
            ],
            "backup_method": "test_data_manager",
            "status": "completed"
        }
        
        with open(backup_path, 'w') as f:
            json.dump(backup_data, f, indent=2)
        
        print(f"   Backup saved to: {backup_path}")
        return str(backup_path)
    
    def restore_backup(self, backup_path: str) -> bool:
        """Restore data from backup."""
        backup_file = Path(backup_path)
        
        if not backup_file.exists():
            print(f"❌ Backup file not found: {backup_path}")
            return False
        
        print(f"🔄 Restoring backup from: {backup_file.name}")
        
        try:
            with open(backup_file, 'r') as f:
                backup_data = json.load(f)
            
            print(f"   Backup timestamp: {backup_data.get('backup_timestamp')}")
            print(f"   Data sources: {', '.join(backup_data.get('data_sources', []))}")
            
            # In real implementation, would restore to database
            print(f"✅ Backup restored successfully")
            return True
            
        except Exception as e:
            print(f"❌ Error restoring backup: {e}")
            return False
    
    def cleanup_test_data(self, 
                         data_types: List[str] = None,
                         older_than_days: int = 30) -> Dict[str, int]:
        """Clean up old test data."""
        if data_types is None:
            data_types = ["transactions", "report_generations", "valuation_projects"]
        
        print(f"🧹 Cleaning up test data older than {older_than_days} days...")
        
        cleanup_summary = {}
        cutoff_date = datetime.now() - timedelta(days=older_than_days)
        
        for data_type in data_types:
            # In real implementation, would delete from database
            # For now, simulate cleanup counts
            cleanup_count = random.randint(10, 100)
            cleanup_summary[data_type] = cleanup_count
            print(f"   Cleaned {cleanup_count} {data_type} records")
        
        total_cleaned = sum(cleanup_summary.values())
        print(f"✅ Cleanup completed. Total records removed: {total_cleaned}")
        
        return cleanup_summary
    
    def export_test_scenarios(self, output_file: str = "test_scenarios.json") -> str:
        """Export predefined test scenarios for consistent testing."""
        scenarios = {
            "financial_statements": {
                "description": "Standard financial statement generation testing",
                "organizations": 1,
                "accounts_per_org": 25,
                "transactions_per_org": 100,
                "time_period": "12_months",
                "currencies": ["USD"],
                "required_account_types": ["ASSET", "LIABILITY", "EQUITY", "REVENUE", "EXPENSE"]
            },
            "dcf_valuation": {
                "description": "DCF valuation model testing",
                "organizations": 2,
                "valuation_projects_per_org": 5,
                "projection_years": 5,
                "sensitivity_scenarios": 3,
                "monte_carlo_simulations": 1000,
                "required_metrics": ["revenue", "free_cash_flow", "ebitda"]
            },
            "saas_metrics": {
                "description": "SaaS-specific metrics and valuation testing",
                "organizations": 1,
                "subscription_customers": 500,
                "mrr_range": [50000, 1000000],
                "churn_rate_range": [0.02, 0.08],
                "growth_rate_range": [0.15, 0.80],
                "required_metrics": ["arr", "mrr", "ltv", "cac", "churn_rate"]
            },
            "performance_load": {
                "description": "High-volume data for performance testing",
                "organizations": 10,
                "accounts_per_org": 100,
                "transactions_per_org": 10000,
                "concurrent_reports": 50,
                "data_retention_months": 36,
                "stress_test_multiplier": 5
            },
            "integration_testing": {
                "description": "End-to-end integration testing scenarios",
                "organizations": 3,
                "xero_integration": True,
                "webhook_events": 25,
                "api_endpoints_covered": 15,
                "export_formats": ["PDF", "Excel", "HTML"],
                "authentication_methods": ["OAuth", "API_Key"]
            },
            "error_handling": {
                "description": "Error conditions and edge cases testing",
                "invalid_data_scenarios": 10,
                "missing_required_fields": 5,
                "data_type_mismatches": 8,
                "boundary_value_tests": 12,
                "concurrent_access_conflicts": 5
            }
        }
        
        output_path = self.data_directory / output_file
        
        with open(output_path, 'w') as f:
            json.dump(scenarios, f, indent=2)
        
        print(f"📋 Test scenarios exported to: {output_path}")
        return str(output_path)
    
    def validate_data_integrity(self, data_file: str) -> Dict[str, Any]:
        """Validate data integrity and consistency."""
        print(f"🔍 Validating data integrity for: {data_file}")
        
        data_path = Path(data_file)
        if not data_path.exists():
            return {"status": "error", "message": "File not found"}
        
        try:
            with open(data_path, 'r') as f:
                data = json.load(f)
            
            validation_results = {
                "status": "success",
                "file_size": data_path.stat().st_size,
                "validation_timestamp": datetime.now().isoformat(),
                "checks": {}
            }
            
            # Validate data structure
            expected_sections = ["organizations", "accounts", "transactions", "valuation_projects"]
            data_sections = data.get("data", {})
            
            for section in expected_sections:
                if section in data_sections:
                    records = data_sections[section]
                    validation_results["checks"][section] = {
                        "present": True,
                        "record_count": len(records),
                        "has_data": len(records) > 0
                    }
                else:
                    validation_results["checks"][section] = {
                        "present": False,
                        "record_count": 0,
                        "has_data": False
                    }
            
            # Check referential integrity
            org_ids = {org["id"] for org in data_sections.get("organizations", [])}
            
            # Validate accounts reference valid organizations
            invalid_account_refs = 0
            for account in data_sections.get("accounts", []):
                if account.get("organization_id") not in org_ids:
                    invalid_account_refs += 1
            
            validation_results["checks"]["referential_integrity"] = {
                "invalid_account_organization_refs": invalid_account_refs,
                "organization_ids_count": len(org_ids)
            }
            
            # Summary
            total_records = sum(
                len(data_sections.get(section, [])) 
                for section in expected_sections
            )
            validation_results["summary"] = {
                "total_records": total_records,
                "data_sections": len([s for s in expected_sections if s in data_sections]),
                "integrity_issues": invalid_account_refs
            }
            
            print(f"✅ Validation completed:")
            print(f"   Total records: {total_records}")
            print(f"   Data sections: {validation_results['summary']['data_sections']}/{len(expected_sections)}")
            print(f"   Integrity issues: {invalid_account_refs}")
            
            return validation_results
            
        except Exception as e:
            error_result = {
                "status": "error",
                "message": str(e),
                "validation_timestamp": datetime.now().isoformat()
            }
            print(f"❌ Validation failed: {e}")
            return error_result


def main():
    """Main entry point for test data management."""
    parser = argparse.ArgumentParser(description="MCX3D Test Data Management")
    
    subparsers = parser.add_subparsers(dest="command", help="Available commands")
    
    # Anonymize command
    anonymize_parser = subparsers.add_parser("anonymize", help="Anonymize test data")
    anonymize_parser.add_argument("--input", "-i", required=True, help="Input data file")
    anonymize_parser.add_argument("--output", "-o", help="Output file for anonymized data")
    anonymize_parser.add_argument("--level", "-l", choices=["low", "medium", "high"], 
                                default="medium", help="Anonymization level")
    
    # Performance data command
    perf_parser = subparsers.add_parser("performance", help="Create performance dataset")
    perf_parser.add_argument("--size", "-s", type=int, default=1000, help="Base dataset size")
    perf_parser.add_argument("--scaling", type=int, default=10, help="Scaling factor")
    perf_parser.add_argument("--output", "-o", default="performance_dataset.json", help="Output file")
    
    # Backup command
    backup_parser = subparsers.add_parser("backup", help="Backup test data")
    backup_parser.add_argument("--name", "-n", help="Backup name")
    
    # Restore command
    restore_parser = subparsers.add_parser("restore", help="Restore from backup")
    restore_parser.add_argument("--backup", "-b", required=True, help="Backup file path")
    
    # Cleanup command
    cleanup_parser = subparsers.add_parser("cleanup", help="Cleanup old test data")
    cleanup_parser.add_argument("--days", "-d", type=int, default=30, help="Days to keep")
    cleanup_parser.add_argument("--types", nargs="+", help="Data types to clean")
    
    # Export scenarios command
    export_parser = subparsers.add_parser("scenarios", help="Export test scenarios")
    export_parser.add_argument("--output", "-o", default="test_scenarios.json", help="Output file")
    
    # Validate command
    validate_parser = subparsers.add_parser("validate", help="Validate data integrity")
    validate_parser.add_argument("--file", "-f", required=True, help="Data file to validate")
    
    args = parser.parse_args()
    
    if not args.command:
        parser.print_help()
        return
    
    try:
        manager = TestDataManager()
        
        if args.command == "anonymize":
            with open(args.input, 'r') as f:
                source_data = json.load(f)
            
            anonymized = manager.anonymize_data(source_data, args.level)
            
            output_file = args.output or f"anonymized_{args.level}_{Path(args.input).name}"
            with open(output_file, 'w') as f:
                json.dump(anonymized, f, indent=2, default=str)
            
            print(f"✅ Anonymized data saved to: {output_file}")
        
        elif args.command == "performance":
            performance_data = manager.create_performance_dataset(args.size, args.scaling)
            
            with open(args.output, 'w') as f:
                json.dump(performance_data, f, indent=2, default=str)
            
            print(f"✅ Performance dataset saved to: {args.output}")
        
        elif args.command == "backup":
            backup_path = manager.backup_current_data(args.name)
            print(f"✅ Backup created: {backup_path}")
        
        elif args.command == "restore":
            success = manager.restore_backup(args.backup)
            if success:
                print("✅ Restore completed successfully")
            else:
                print("❌ Restore failed")
        
        elif args.command == "cleanup":
            cleanup_summary = manager.cleanup_test_data(args.types, args.days)
            print(f"✅ Cleanup completed: {cleanup_summary}")
        
        elif args.command == "scenarios":
            scenarios_file = manager.export_test_scenarios(args.output)
            print(f"✅ Test scenarios exported: {scenarios_file}")
        
        elif args.command == "validate":
            validation_results = manager.validate_data_integrity(args.file)
            print(f"✅ Validation results: {validation_results['status']}")
    
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()