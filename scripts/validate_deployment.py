#!/usr/bin/env python3
"""
Production Deployment Validation Script for MCX3D Financial System.

This script validates deployments by running comprehensive checks
to ensure the system is ready for production use.
"""

import sys
import os
import json
import time
import argparse
import subprocess
from pathlib import Path
from datetime import datetime
from typing import Dict, List, Any, Optional
import logging

# Add the project root to Python path
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))


class DeploymentValidator:
    """Comprehensive deployment validation system."""
    
    def __init__(self, environment: str = "production"):
        self.environment = environment
        self.results = {
            "timestamp": datetime.now().isoformat(),
            "environment": environment,
            "validation_steps": {},
            "summary": {
                "total_steps": 0,
                "passed_steps": 0,
                "failed_steps": 0,
                "warning_steps": 0
            },
            "deployment_ready": False
        }
        self._setup_logging()
    
    def _setup_logging(self):
        """Setup logging for deployment validation."""
        log_level = logging.DEBUG if os.getenv('DEBUG') else logging.INFO
        logging.basicConfig(
            level=log_level,
            format='%(asctime)s - %(levelname)s - %(message)s'
        )
        self.logger = logging.getLogger(__name__)
    
    def _record_step_result(self, step_name: str, status: str, 
                          message: str, details: Dict[str, Any] = None):
        """Record the result of a validation step."""
        self.results["validation_steps"][step_name] = {
            "status": status,
            "message": message,
            "details": details or {},
            "timestamp": datetime.now().isoformat()
        }
        
        self.results["summary"]["total_steps"] += 1
        
        if status == "PASS":
            self.results["summary"]["passed_steps"] += 1
            self.logger.info(f"✅ {step_name}: {message}")
        elif status == "FAIL":
            self.results["summary"]["failed_steps"] += 1
            self.logger.error(f"❌ {step_name}: {message}")
        elif status == "WARNING":
            self.results["summary"]["warning_steps"] += 1
            self.logger.warning(f"⚠️  {step_name}: {message}")
    
    def validate_environment_setup(self):
        """Validate environment setup and configuration."""
        try:
            # Check critical environment variables for production
            if self.environment == "production":
                required_vars = [
                    "DATABASE_URL",
                    "REDIS_URL",
                    "SECRET_KEY",
                    "ENVIRONMENT"
                ]
                
                missing_vars = [var for var in required_vars if not os.getenv(var)]
                
                if missing_vars:
                    self._record_step_result(
                        "environment_setup",
                        "FAIL",
                        f"Missing required environment variables: {', '.join(missing_vars)}",
                        {"missing_variables": missing_vars}
                    )
                    return
            
            # Validate environment value
            env_value = os.getenv('ENVIRONMENT', '').lower()
            if self.environment == "production" and env_value != "production":
                self._record_step_result(
                    "environment_setup",
                    "WARNING",
                    f"ENVIRONMENT variable is '{env_value}', expected 'production'"
                )
            else:
                self._record_step_result(
                    "environment_setup",
                    "PASS",
                    f"Environment configuration validated for {self.environment}"
                )
        
        except Exception as e:
            self._record_step_result(
                "environment_setup",
                "FAIL",
                f"Error validating environment setup: {e}"
            )
    
    def validate_dependencies(self):
        """Validate that all required dependencies are available."""
        try:
            # Test critical imports
            critical_modules = [
                "mcx3d_finance.reporting.generator",
                "mcx3d_finance.core.financials.balance_sheet",
                "mcx3d_finance.core.financials.income_statement",
                "mcx3d_finance.db.session",
                "mcx3d_finance.api.main"
            ]
            
            missing_modules = []
            for module in critical_modules:
                try:
                    __import__(module)
                except ImportError as e:
                    missing_modules.append(f"{module}: {e}")
            
            if missing_modules:
                self._record_step_result(
                    "dependencies",
                    "FAIL",
                    f"Missing critical dependencies: {len(missing_modules)} modules",
                    {"missing_modules": missing_modules}
                )
            else:
                self._record_step_result(
                    "dependencies",
                    "PASS",
                    f"All {len(critical_modules)} critical dependencies available"
                )
        
        except Exception as e:
            self._record_step_result(
                "dependencies",
                "FAIL",
                f"Error validating dependencies: {e}"
            )
    
    def validate_database_connectivity(self):
        """Validate database connectivity and basic operations."""
        try:
            database_url = os.getenv('DATABASE_URL')
            if not database_url:
                self._record_step_result(
                    "database_connectivity",
                    "FAIL",
                    "DATABASE_URL not configured"
                )
                return
            
            # Try to import and test database connection
            try:
                from mcx3d_finance.db.session import get_session
                
                # Test database connection
                with get_session() as session:
                    # Execute a simple query to test connectivity
                    result = session.execute("SELECT 1 as test").fetchone()
                    if result and result.test == 1:
                        self._record_step_result(
                            "database_connectivity",
                            "PASS",
                            "Database connectivity verified"
                        )
                    else:
                        self._record_step_result(
                            "database_connectivity",
                            "FAIL",
                            "Database query returned unexpected result"
                        )
            
            except Exception as db_error:
                self._record_step_result(
                    "database_connectivity",
                    "FAIL",
                    f"Database connection failed: {db_error}"
                )
        
        except Exception as e:
            self._record_step_result(
                "database_connectivity",
                "FAIL",
                f"Error testing database connectivity: {e}"
            )
    
    def validate_redis_connectivity(self):
        """Validate Redis connectivity for caching and sessions."""
        try:
            redis_url = os.getenv('REDIS_URL')
            if not redis_url:
                self._record_step_result(
                    "redis_connectivity",
                    "WARNING",
                    "REDIS_URL not configured (optional for some deployments)"
                )
                return
            
            # Test Redis connection if available
            try:
                import redis
                
                # Parse Redis URL and connect
                redis_client = redis.from_url(redis_url)
                
                # Test Redis with a simple operation
                test_key = f"mcx3d_deployment_test_{int(time.time())}"
                redis_client.set(test_key, "test_value", ex=10)  # Expire in 10 seconds
                
                retrieved_value = redis_client.get(test_key)
                if retrieved_value and retrieved_value.decode() == "test_value":
                    redis_client.delete(test_key)  # Cleanup
                    self._record_step_result(
                        "redis_connectivity",
                        "PASS",
                        "Redis connectivity verified"
                    )
                else:
                    self._record_step_result(
                        "redis_connectivity",
                        "FAIL",
                        "Redis connection test failed"
                    )
            
            except ImportError:
                self._record_step_result(
                    "redis_connectivity",
                    "WARNING",
                    "Redis package not available"
                )
            except Exception as redis_error:
                self._record_step_result(
                    "redis_connectivity",
                    "FAIL",
                    f"Redis connection failed: {redis_error}"
                )
        
        except Exception as e:
            self._record_step_result(
                "redis_connectivity",
                "FAIL",
                f"Error testing Redis connectivity: {e}"
            )
    
    def validate_core_functionality(self):
        """Validate core application functionality."""
        try:
            from mcx3d_finance.reporting.generator import ReportGenerator
            
            # Test data for validation
            test_data = {
                "organization_id": 1,
                "company_name": "Deployment Validation Corp",
                "valuation_date": "2024-01-01",
                "enterprise_value": 1000000,
                "financial_projections": [
                    {
                        "year": 1,
                        "revenue": 500000,
                        "free_cash_flow": 100000,
                        "ebitda": 150000,
                        "revenue_growth_rate": 0.10
                    }
                ],
                "assumptions": {
                    "discount_rate": 0.12,
                    "terminal_growth": 0.025,
                    "tax_rate": 0.21
                }
            }
            
            report_generator = ReportGenerator()
            
            # Test report generation with temporary file
            import tempfile
            with tempfile.NamedTemporaryFile(suffix=".pdf", delete=False) as temp_file:
                temp_path = temp_file.name
            
            try:
                start_time = time.time()
                
                report_generator.generate_dcf_valuation_pdf(
                    dcf_data=test_data,
                    output_path=temp_path
                )
                
                end_time = time.time()
                generation_time = end_time - start_time
                
                # Validate output
                if Path(temp_path).exists():
                    file_size = Path(temp_path).stat().st_size
                    Path(temp_path).unlink()  # Cleanup
                    
                    if generation_time > 30.0:  # Longer threshold for deployment validation
                        self._record_step_result(
                            "core_functionality",
                            "WARNING",
                            f"Core functionality works but slow: {generation_time:.2f}s",
                            {"generation_time": generation_time, "file_size": file_size}
                        )
                    else:
                        self._record_step_result(
                            "core_functionality",
                            "PASS",
                            f"Core functionality validated in {generation_time:.2f}s",
                            {"generation_time": generation_time, "file_size": file_size}
                        )
                else:
                    self._record_step_result(
                        "core_functionality",
                        "FAIL",
                        "Core functionality test did not produce output"
                    )
            
            finally:
                # Ensure cleanup
                if Path(temp_path).exists():
                    Path(temp_path).unlink()
        
        except Exception as e:
            self._record_step_result(
                "core_functionality",
                "FAIL",
                f"Core functionality validation failed: {e}"
            )
    
    def validate_security_configuration(self):
        """Validate security configuration and settings."""
        try:
            security_issues = []
            
            # Check SECRET_KEY
            secret_key = os.getenv('SECRET_KEY', '')
            if not secret_key:
                security_issues.append("SECRET_KEY not set")
            elif len(secret_key) < 32:
                security_issues.append("SECRET_KEY too short (< 32 characters)")
            elif secret_key.lower() in ['secret', 'password', 'key', 'test', 'development']:
                security_issues.append("SECRET_KEY appears to be a default/weak value")
            
            # Check for debug mode in production
            if self.environment == "production":
                debug_indicators = [
                    ('DEBUG', 'True'),
                    ('FLASK_DEBUG', '1'),
                    ('DJANGO_DEBUG', 'True')
                ]
                
                for env_var, debug_value in debug_indicators:
                    if os.getenv(env_var, '').lower() == debug_value.lower():
                        security_issues.append(f"{env_var} enabled in production")
            
            # Check file permissions on sensitive files
            sensitive_files = [
                '.env',
                'config.yml',
                'secrets.json'
            ]
            
            project_root = Path(__file__).parent.parent
            for filename in sensitive_files:
                file_path = project_root / filename
                if file_path.exists():
                    import stat
                    file_stat = file_path.stat()
                    # Check if file is readable by others
                    if file_stat.st_mode & stat.S_IROTH:
                        security_issues.append(f"{filename} is readable by others")
            
            if security_issues:
                self._record_step_result(
                    "security_configuration",
                    "FAIL" if len(security_issues) > 2 else "WARNING",
                    f"Security issues found: {len(security_issues)}",
                    {"security_issues": security_issues}
                )
            else:
                self._record_step_result(
                    "security_configuration",
                    "PASS",
                    "Security configuration validated"
                )
        
        except Exception as e:
            self._record_step_result(
                "security_configuration",
                "FAIL",
                f"Error validating security configuration: {e}"
            )
    
    def validate_performance_benchmarks(self):
        """Validate that performance meets minimum benchmarks."""
        try:
            # Run the performance baseline script
            scripts_dir = Path(__file__).parent
            baseline_script = scripts_dir / "establish_baselines.py"
            
            if not baseline_script.exists():
                self._record_step_result(
                    "performance_benchmarks",
                    "WARNING",
                    "Performance baseline script not found"
                )
                return
            
            # Run baseline establishment
            import subprocess
            import tempfile
            
            with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as temp_file:
                temp_baseline_file = temp_file.name
            
            try:
                # Change to project root and run baseline script
                project_root = scripts_dir.parent
                result = subprocess.run(
                    [sys.executable, str(baseline_script)],
                    cwd=project_root,
                    capture_output=True,
                    text=True,
                    timeout=120  # 2 minute timeout
                )
                
                if result.returncode == 0:
                    # Check if baseline file was created
                    baseline_file = project_root / "performance_baseline.json"
                    if baseline_file.exists():
                        # Load and analyze results
                        with open(baseline_file, 'r') as f:
                            baseline_data = json.load(f)
                        
                        # Check performance against thresholds
                        performance_issues = []
                        for test_name, test_data in baseline_data.get("tests", {}).items():
                            avg_time = test_data.get("times", {}).get("average", 0)
                            success_rate = (
                                test_data.get("successful_iterations", 0) / 
                                test_data.get("iterations", 1) * 100
                            )
                            
                            if avg_time > 5.0:  # 5 second threshold for deployment
                                performance_issues.append(f"{test_name}: {avg_time:.2f}s (slow)")
                            
                            if success_rate < 95:
                                performance_issues.append(f"{test_name}: {success_rate:.1f}% success (low)")
                        
                        if performance_issues:
                            self._record_step_result(
                                "performance_benchmarks",
                                "WARNING",
                                f"Performance issues: {len(performance_issues)}",
                                {"performance_issues": performance_issues}
                            )
                        else:
                            self._record_step_result(
                                "performance_benchmarks",
                                "PASS",
                                "Performance benchmarks meet requirements"
                            )
                    else:
                        self._record_step_result(
                            "performance_benchmarks",
                            "WARNING",
                            "Baseline script ran but no output file created"
                        )
                else:
                    self._record_step_result(
                        "performance_benchmarks",
                        "FAIL",
                        f"Performance baseline script failed: {result.stderr}"
                    )
            
            finally:
                # Cleanup
                if Path(temp_baseline_file).exists():
                    Path(temp_baseline_file).unlink()
        
        except subprocess.TimeoutExpired:
            self._record_step_result(
                "performance_benchmarks",
                "FAIL",
                "Performance benchmark validation timed out"
            )
        except Exception as e:
            self._record_step_result(
                "performance_benchmarks",
                "FAIL",
                f"Error validating performance benchmarks: {e}"
            )
    
    def validate_logging_and_monitoring(self):
        """Validate logging and monitoring configuration."""
        try:
            import logging
            
            # Test that logging works
            test_logger = logging.getLogger('mcx3d_finance.deployment_test')
            
            # Test different log levels
            log_levels_working = []
            for level_name, level_value in [
                ('DEBUG', logging.DEBUG),
                ('INFO', logging.INFO),
                ('WARNING', logging.WARNING),
                ('ERROR', logging.ERROR)
            ]:
                try:
                    test_logger.log(level_value, f"Deployment validation {level_name} test")
                    log_levels_working.append(level_name)
                except Exception:
                    pass
            
            # Check log configuration
            log_issues = []
            
            # Check if logs are going to appropriate handlers
            root_logger = logging.getLogger()
            if not root_logger.handlers:
                log_issues.append("No logging handlers configured")
            
            # Check log level configuration
            if root_logger.level > logging.WARNING and self.environment == "production":
                log_issues.append("Log level too high for production (missing important logs)")
            
            if log_issues:
                self._record_step_result(
                    "logging_and_monitoring",
                    "WARNING",
                    f"Logging issues found: {len(log_issues)}",
                    {"log_issues": log_issues, "working_levels": log_levels_working}
                )
            else:
                self._record_step_result(
                    "logging_and_monitoring",
                    "PASS",
                    f"Logging configuration validated ({len(log_levels_working)} levels working)"
                )
        
        except Exception as e:
            self._record_step_result(
                "logging_and_monitoring",
                "FAIL",
                f"Error validating logging and monitoring: {e}"
            )
    
    def run_all_validations(self) -> Dict[str, Any]:
        """Run all deployment validations."""
        self.logger.info(f"🚀 Starting MCX3D Deployment Validation ({self.environment})")
        start_time = time.time()
        
        # Run all validation steps
        self.validate_environment_setup()
        self.validate_dependencies()
        self.validate_database_connectivity()
        self.validate_redis_connectivity()
        self.validate_core_functionality()
        self.validate_security_configuration()
        self.validate_performance_benchmarks()
        self.validate_logging_and_monitoring()
        
        # Calculate overall deployment readiness
        total_time = time.time() - start_time
        
        failed_steps = self.results["summary"]["failed_steps"]
        warning_steps = self.results["summary"]["warning_steps"]
        
        # Deployment is ready if no critical failures
        # Warnings are acceptable but should be addressed
        self.results["deployment_ready"] = failed_steps == 0
        self.results["summary"]["total_duration"] = total_time
        
        status_msg = "READY" if self.results["deployment_ready"] else "NOT READY"
        self.logger.info(f"✅ Deployment validation completed in {total_time:.2f}s - {status_msg}")
        
        return self.results
    
    def save_results(self, output_file: str = None) -> str:
        """Save deployment validation results."""
        if output_file is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            output_file = f"/tmp/mcx3d_deployment_validation_{self.environment}_{timestamp}.json"
        
        output_path = Path(output_file)
        
        with open(output_path, 'w') as f:
            json.dump(self.results, f, indent=2, default=str)
        
        self.logger.info(f"💾 Deployment validation results saved to: {output_path}")
        return str(output_path)


def main():
    """Main entry point for deployment validation."""
    parser = argparse.ArgumentParser(description="MCX3D Deployment Validation")
    parser.add_argument("--environment", "-e", default="production",
                       choices=["production", "staging", "development"],
                       help="Environment to validate")
    parser.add_argument("--output", "-o", help="Output file for results")
    parser.add_argument("--verbose", "-v", action="store_true", help="Verbose logging")
    parser.add_argument("--json-output", action="store_true", help="Output results as JSON")
    parser.add_argument("--exit-code", action="store_true",
                       help="Exit with non-zero code if validation fails")
    
    args = parser.parse_args()
    
    if args.verbose:
        logging.getLogger().setLevel(logging.DEBUG)
        os.environ['DEBUG'] = '1'
    
    try:
        validator = DeploymentValidator(args.environment)
        results = validator.run_all_validations()
        
        # Save results
        output_file = validator.save_results(args.output)
        
        # Print summary
        if args.json_output:
            print(json.dumps(results, indent=2, default=str))
        else:
            print(f"\n🚀 MCX3D Deployment Validation Summary ({args.environment})")
            print("=" * 60)
            print(f"Deployment Ready: {'✅ YES' if results['deployment_ready'] else '❌ NO'}")
            print(f"Total Steps: {results['summary']['total_steps']}")
            print(f"Passed: {results['summary']['passed_steps']}")
            print(f"Warnings: {results['summary']['warning_steps']}")
            print(f"Failed: {results['summary']['failed_steps']}")
            print(f"Duration: {results['summary']['total_duration']:.2f}s")
            print(f"Results saved to: {output_file}")
            
            # Show failed steps
            if results['summary']['failed_steps'] > 0:
                print(f"\n❌ Failed Steps:")
                for step_name, step_result in results['validation_steps'].items():
                    if step_result['status'] == 'FAIL':
                        print(f"  • {step_name}: {step_result['message']}")
            
            # Show warning steps
            if results['summary']['warning_steps'] > 0:
                print(f"\n⚠️  Warning Steps:")
                for step_name, step_result in results['validation_steps'].items():
                    if step_result['status'] == 'WARNING':
                        print(f"  • {step_name}: {step_result['message']}")
            
            if results['deployment_ready']:
                print(f"\n🎉 Deployment validation passed! System is ready for {args.environment}.")
            else:
                print(f"\n🛑 Deployment validation failed. Please address failed steps before deploying to {args.environment}.")
        
        # Exit with appropriate code
        if args.exit_code:
            sys.exit(0 if results['deployment_ready'] else 1)
    
    except KeyboardInterrupt:
        print("\n⚠️  Deployment validation interrupted by user")
        sys.exit(130)
    except Exception as e:
        print(f"❌ Deployment validation failed: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)


if __name__ == "__main__":
    main()