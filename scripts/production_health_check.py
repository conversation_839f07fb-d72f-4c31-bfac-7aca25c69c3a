#!/usr/bin/env python3
"""
Production Health Check Script for MCX3D Financial System.

This script performs comprehensive health checks in production environments
to ensure system reliability and performance.
"""

import sys
import os
import json
import time
import tempfile
import logging
from pathlib import Path
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional
import argparse

# Add the project root to Python path
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from mcx3d_finance.reporting.generator import ReportGenerator


class ProductionHealthChecker:
    """Comprehensive production health checking system."""
    
    def __init__(self, config_file: Optional[str] = None):
        self.config = self._load_config(config_file)
        self.results = {
            "timestamp": datetime.now().isoformat(),
            "checks": {},
            "summary": {
                "total_checks": 0,
                "passed_checks": 0,
                "failed_checks": 0,
                "warning_checks": 0
            },
            "overall_status": "UNKNOWN"
        }
        self._setup_logging()
    
    def _load_config(self, config_file: Optional[str]) -> Dict[str, Any]:
        """Load health check configuration."""
        default_config = {
            "timeouts": {
                "report_generation": 30.0,
                "health_check_total": 300.0
            },
            "thresholds": {
                "memory_usage_mb": 500,
                "report_generation_time": 10.0,
                "concurrent_reports": 3
            },
            "required_env_vars": [
                "DATABASE_URL",
                "REDIS_URL"
            ],
            "optional_env_vars": [
                "XERO_CLIENT_ID",
                "XERO_CLIENT_SECRET",
                "XERO_WEBHOOK_KEY"
            ],
            "output_directory": "/tmp/mcx3d_health_checks"
        }
        
        if config_file and Path(config_file).exists():
            try:
                with open(config_file, 'r') as f:
                    user_config = json.load(f)
                    default_config.update(user_config)
            except Exception as e:
                logging.warning(f"Failed to load config file {config_file}: {e}")
        
        return default_config
    
    def _setup_logging(self):
        """Setup logging for health checks."""
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.StreamHandler(),
                logging.FileHandler('/tmp/mcx3d_health_check.log', mode='a')
            ]
        )
        self.logger = logging.getLogger(__name__)
    
    def _record_check_result(self, check_name: str, status: str, 
                           message: str, details: Dict[str, Any] = None):
        """Record the result of a health check."""
        self.results["checks"][check_name] = {
            "status": status,
            "message": message,
            "details": details or {},
            "timestamp": datetime.now().isoformat()
        }
        
        self.results["summary"]["total_checks"] += 1
        
        if status == "PASS":
            self.results["summary"]["passed_checks"] += 1
            self.logger.info(f"✅ {check_name}: {message}")
        elif status == "FAIL":
            self.results["summary"]["failed_checks"] += 1
            self.logger.error(f"❌ {check_name}: {message}")
        elif status == "WARNING":
            self.results["summary"]["warning_checks"] += 1
            self.logger.warning(f"⚠️  {check_name}: {message}")
    
    def check_environment_configuration(self):
        """Check environment configuration and variables."""
        try:
            # Check required environment variables
            missing_required = []
            for var in self.config["required_env_vars"]:
                if not os.getenv(var):
                    missing_required.append(var)
            
            # Check optional environment variables
            missing_optional = []
            for var in self.config["optional_env_vars"]:
                if not os.getenv(var):
                    missing_optional.append(var)
            
            if missing_required:
                self._record_check_result(
                    "environment_configuration",
                    "FAIL",
                    f"Missing required environment variables: {', '.join(missing_required)}",
                    {"missing_required": missing_required, "missing_optional": missing_optional}
                )
            elif missing_optional:
                self._record_check_result(
                    "environment_configuration",
                    "WARNING",
                    f"Missing optional environment variables: {', '.join(missing_optional)}",
                    {"missing_optional": missing_optional}
                )
            else:
                self._record_check_result(
                    "environment_configuration",
                    "PASS",
                    "All environment variables are properly configured"
                )
        
        except Exception as e:
            self._record_check_result(
                "environment_configuration",
                "FAIL",
                f"Error checking environment configuration: {e}"
            )
    
    def check_file_system_permissions(self):
        """Check file system permissions and disk space."""
        try:
            output_dir = Path(self.config["output_directory"])
            output_dir.mkdir(parents=True, exist_ok=True)
            
            # Test file creation
            test_file = output_dir / f"health_check_{int(time.time())}.txt"
            test_content = f"Health check test file created at {datetime.now()}"
            
            test_file.write_text(test_content)
            
            # Verify file was created and readable
            if test_file.exists() and test_file.read_text() == test_content:
                file_size = test_file.stat().st_size
                test_file.unlink()  # Cleanup
                
                # Check available disk space
                import shutil
                total, used, free = shutil.disk_usage(output_dir)
                free_gb = free / (1024**3)
                
                if free_gb < 1.0:  # Less than 1GB free
                    self._record_check_result(
                        "file_system_permissions",
                        "WARNING",
                        f"Low disk space: {free_gb:.2f}GB free",
                        {"free_space_gb": free_gb, "total_space_gb": total / (1024**3)}
                    )
                else:
                    self._record_check_result(
                        "file_system_permissions",
                        "PASS",
                        f"File system accessible, {free_gb:.2f}GB free space",
                        {"free_space_gb": free_gb}
                    )
            else:
                self._record_check_result(
                    "file_system_permissions",
                    "FAIL",
                    "Unable to create or read test file"
                )
        
        except Exception as e:
            self._record_check_result(
                "file_system_permissions",
                "FAIL",
                f"File system permission error: {e}"
            )
    
    def check_core_functionality(self):
        """Check core report generation functionality."""
        try:
            # Test data for health check
            test_data = {
                "organization_id": 999999,
                "company_name": "Health Check Test Corp",
                "valuation_date": datetime.now().strftime("%Y-%m-%d"),
                "enterprise_value": 1000000,
                "financial_projections": [
                    {
                        "year": 1,
                        "revenue": 500000,
                        "free_cash_flow": 100000,
                        "ebitda": 150000,
                        "revenue_growth_rate": 0.10
                    }
                ],
                "assumptions": {
                    "discount_rate": 0.12,
                    "terminal_growth": 0.025,
                    "tax_rate": 0.21
                }
            }
            
            report_generator = ReportGenerator()
            output_dir = Path(self.config["output_directory"])
            output_file = output_dir / f"health_check_{int(time.time())}.pdf"
            
            start_time = time.time()
            
            report_generator.generate_dcf_valuation_pdf(
                dcf_data=test_data,
                output_path=str(output_file)
            )
            
            end_time = time.time()
            generation_time = end_time - start_time
            
            # Validate output
            if output_file.exists():
                file_size = output_file.stat().st_size
                output_file.unlink()  # Cleanup
                
                # Check performance
                max_time = self.config["thresholds"]["report_generation_time"]
                if generation_time > max_time:
                    self._record_check_result(
                        "core_functionality",
                        "WARNING",
                        f"Report generation slow: {generation_time:.2f}s (threshold: {max_time}s)",
                        {"generation_time": generation_time, "file_size": file_size}
                    )
                else:
                    self._record_check_result(
                        "core_functionality",
                        "PASS",
                        f"Report generation successful in {generation_time:.2f}s",
                        {"generation_time": generation_time, "file_size": file_size}
                    )
            else:
                self._record_check_result(
                    "core_functionality",
                    "FAIL",
                    "Report file was not created"
                )
        
        except Exception as e:
            self._record_check_result(
                "core_functionality",
                "FAIL",
                f"Core functionality error: {e}"
            )
    
    def check_memory_usage(self):
        """Check memory usage patterns."""
        try:
            import psutil
            process = psutil.Process()
            
            initial_memory = process.memory_info().rss
            initial_memory_mb = initial_memory / (1024 * 1024)
            
            # Generate a few reports to test memory usage
            report_generator = ReportGenerator()
            test_data = {
                "organization_id": 999998,
                "company_name": "Memory Test Corp",
                "valuation_date": "2024-01-01",
                "enterprise_value": 1000000,
                "financial_projections": [{
                    "year": 1,
                    "revenue": 500000,
                    "free_cash_flow": 100000,
                    "ebitda": 150000
                }]
            }
            
            # Generate 3 reports
            for i in range(3):
                with tempfile.NamedTemporaryFile(suffix=f"_memory_test_{i}.pdf") as temp_file:
                    report_generator.generate_dcf_valuation_pdf(
                        dcf_data=test_data,
                        output_path=temp_file.name
                    )
            
            final_memory = process.memory_info().rss
            final_memory_mb = final_memory / (1024 * 1024)
            memory_increase_mb = final_memory_mb - initial_memory_mb
            
            max_memory_mb = self.config["thresholds"]["memory_usage_mb"]
            
            if final_memory_mb > max_memory_mb:
                self._record_check_result(
                    "memory_usage",
                    "WARNING",
                    f"High memory usage: {final_memory_mb:.1f}MB (threshold: {max_memory_mb}MB)",
                    {
                        "initial_memory_mb": initial_memory_mb,
                        "final_memory_mb": final_memory_mb,
                        "memory_increase_mb": memory_increase_mb
                    }
                )
            else:
                self._record_check_result(
                    "memory_usage",
                    "PASS",
                    f"Memory usage within limits: {final_memory_mb:.1f}MB",
                    {
                        "initial_memory_mb": initial_memory_mb,
                        "final_memory_mb": final_memory_mb,
                        "memory_increase_mb": memory_increase_mb
                    }
                )
        
        except ImportError:
            self._record_check_result(
                "memory_usage",
                "WARNING",
                "psutil not available for memory monitoring"
            )
        except Exception as e:
            self._record_check_result(
                "memory_usage",
                "FAIL",
                f"Memory usage check error: {e}"
            )
    
    def check_concurrent_performance(self):
        """Check concurrent request handling."""
        try:
            import concurrent.futures
            
            def generate_concurrent_report(index):
                try:
                    test_data = {
                        "organization_id": 999990 + index,
                        "company_name": f"Concurrent Test Corp {index}",
                        "valuation_date": "2024-01-01",
                        "enterprise_value": 1000000 + (index * 100000),
                        "financial_projections": [{
                            "year": 1,
                            "revenue": 500000 + (index * 50000),
                            "free_cash_flow": 100000 + (index * 10000),
                            "ebitda": 150000 + (index * 15000)
                        }]
                    }
                    
                    report_generator = ReportGenerator()
                    
                    with tempfile.NamedTemporaryFile(suffix=f"_concurrent_{index}.pdf") as temp_file:
                        start_time = time.time()
                        
                        report_generator.generate_dcf_valuation_pdf(
                            dcf_data=test_data,
                            output_path=temp_file.name
                        )
                        
                        end_time = time.time()
                        
                        file_size = Path(temp_file.name).stat().st_size if Path(temp_file.name).exists() else 0
                        
                        return {
                            "index": index,
                            "success": True,
                            "time": end_time - start_time,
                            "file_size": file_size
                        }
                
                except Exception as e:
                    return {
                        "index": index,
                        "success": False,
                        "error": str(e),
                        "time": 0,
                        "file_size": 0
                    }
            
            concurrent_count = self.config["thresholds"]["concurrent_reports"]
            
            with concurrent.futures.ThreadPoolExecutor(max_workers=concurrent_count) as executor:
                start_time = time.time()
                
                futures = [executor.submit(generate_concurrent_report, i) for i in range(concurrent_count)]
                results = [future.result() for future in concurrent.futures.as_completed(futures)]
                
                total_time = time.time() - start_time
            
            # Analyze results
            successful_results = [r for r in results if r["success"]]
            success_rate = len(successful_results) / len(results) * 100
            
            if success_rate < 80:  # Less than 80% success rate
                self._record_check_result(
                    "concurrent_performance",
                    "FAIL",
                    f"Low concurrent success rate: {success_rate:.1f}%",
                    {
                        "success_rate": success_rate,
                        "total_time": total_time,
                        "concurrent_count": concurrent_count
                    }
                )
            elif success_rate < 95:  # Less than 95% success rate
                self._record_check_result(
                    "concurrent_performance",
                    "WARNING",
                    f"Moderate concurrent success rate: {success_rate:.1f}%",
                    {
                        "success_rate": success_rate,
                        "total_time": total_time,
                        "concurrent_count": concurrent_count
                    }
                )
            else:
                avg_time = sum(r["time"] for r in successful_results) / len(successful_results)
                self._record_check_result(
                    "concurrent_performance",
                    "PASS",
                    f"Concurrent performance good: {success_rate:.1f}% success, {avg_time:.2f}s avg",
                    {
                        "success_rate": success_rate,
                        "total_time": total_time,
                        "average_time": avg_time,
                        "concurrent_count": concurrent_count
                    }
                )
        
        except Exception as e:
            self._record_check_result(
                "concurrent_performance",
                "FAIL",
                f"Concurrent performance check error: {e}"
            )
    
    def check_system_dependencies(self):
        """Check system dependencies and versions."""
        try:
            import sys
            import platform
            
            # Check Python version
            python_version = sys.version_info
            if python_version.major != 3 or python_version.minor < 8:
                self._record_check_result(
                    "system_dependencies",
                    "FAIL",
                    f"Unsupported Python version: {python_version.major}.{python_version.minor}",
                    {"python_version": f"{python_version.major}.{python_version.minor}.{python_version.micro}"}
                )
                return
            
            # Check critical imports
            critical_modules = [
                "mcx3d_finance.reporting.generator",
                "mcx3d_finance.core.financials.balance_sheet",
                "mcx3d_finance.core.financials.income_statement"
            ]
            
            missing_modules = []
            for module in critical_modules:
                try:
                    __import__(module)
                except ImportError:
                    missing_modules.append(module)
            
            if missing_modules:
                self._record_check_result(
                    "system_dependencies",
                    "FAIL",
                    f"Missing critical modules: {', '.join(missing_modules)}",
                    {"missing_modules": missing_modules}
                )
            else:
                self._record_check_result(
                    "system_dependencies",
                    "PASS",
                    f"All dependencies available (Python {python_version.major}.{python_version.minor})",
                    {
                        "python_version": f"{python_version.major}.{python_version.minor}.{python_version.micro}",
                        "platform": platform.platform()
                    }
                )
        
        except Exception as e:
            self._record_check_result(
                "system_dependencies",
                "FAIL",
                f"System dependencies check error: {e}"
            )
    
    def run_all_checks(self) -> Dict[str, Any]:
        """Run all health checks and return results."""
        self.logger.info("🚀 Starting MCX3D Production Health Checks")
        start_time = time.time()
        
        # Run all health checks
        self.check_environment_configuration()
        self.check_file_system_permissions()
        self.check_system_dependencies()
        self.check_core_functionality()
        self.check_memory_usage()
        self.check_concurrent_performance()
        
        # Calculate overall status
        total_time = time.time() - start_time
        
        failed_checks = self.results["summary"]["failed_checks"]
        warning_checks = self.results["summary"]["warning_checks"]
        
        if failed_checks > 0:
            self.results["overall_status"] = "CRITICAL"
        elif warning_checks > 0:
            self.results["overall_status"] = "WARNING"
        else:
            self.results["overall_status"] = "HEALTHY"
        
        self.results["summary"]["total_duration"] = total_time
        self.results["summary"]["checks_per_second"] = self.results["summary"]["total_checks"] / total_time
        
        self.logger.info(f"✅ Health checks completed in {total_time:.2f}s")
        self.logger.info(f"📊 Overall Status: {self.results['overall_status']}")
        
        return self.results
    
    def save_results(self, output_file: str = None):
        """Save health check results to file."""
        if output_file is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            output_file = f"/tmp/mcx3d_health_check_{timestamp}.json"
        
        output_path = Path(output_file)
        
        with open(output_path, 'w') as f:
            json.dump(self.results, f, indent=2, default=str)
        
        self.logger.info(f"💾 Health check results saved to: {output_path}")
        return str(output_path)


def main():
    """Main entry point for production health checks."""
    parser = argparse.ArgumentParser(description="MCX3D Production Health Check")
    parser.add_argument("--config", "-c", help="Configuration file path")
    parser.add_argument("--output", "-o", help="Output file for results")
    parser.add_argument("--verbose", "-v", action="store_true", help="Verbose logging")
    parser.add_argument("--json-output", action="store_true", help="Output results as JSON")
    parser.add_argument("--exit-code", action="store_true", 
                       help="Exit with non-zero code if checks fail")
    
    args = parser.parse_args()
    
    if args.verbose:
        logging.getLogger().setLevel(logging.DEBUG)
    
    try:
        health_checker = ProductionHealthChecker(args.config)
        results = health_checker.run_all_checks()
        
        # Save results
        output_file = health_checker.save_results(args.output)
        
        # Print summary
        if args.json_output:
            print(json.dumps(results, indent=2, default=str))
        else:
            print(f"\n🏥 MCX3D Production Health Check Summary")
            print("=" * 50)
            print(f"Overall Status: {results['overall_status']}")
            print(f"Total Checks: {results['summary']['total_checks']}")
            print(f"Passed: {results['summary']['passed_checks']}")
            print(f"Warnings: {results['summary']['warning_checks']}")
            print(f"Failed: {results['summary']['failed_checks']}")
            print(f"Duration: {results['summary']['total_duration']:.2f}s")
            print(f"Results saved to: {output_file}")
            
            # Show failed checks
            if results['summary']['failed_checks'] > 0:
                print(f"\n❌ Failed Checks:")
                for check_name, check_result in results['checks'].items():
                    if check_result['status'] == 'FAIL':
                        print(f"  • {check_name}: {check_result['message']}")
            
            # Show warning checks
            if results['summary']['warning_checks'] > 0:
                print(f"\n⚠️  Warning Checks:")
                for check_name, check_result in results['checks'].items():
                    if check_result['status'] == 'WARNING':
                        print(f"  • {check_name}: {check_result['message']}")
        
        # Exit with appropriate code
        if args.exit_code:
            if results['overall_status'] == 'CRITICAL':
                sys.exit(2)
            elif results['overall_status'] == 'WARNING':
                sys.exit(1)
            else:
                sys.exit(0)
    
    except KeyboardInterrupt:
        print("\n⚠️  Health check interrupted by user")
        sys.exit(130)
    except Exception as e:
        print(f"❌ Health check failed: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)


if __name__ == "__main__":
    main()