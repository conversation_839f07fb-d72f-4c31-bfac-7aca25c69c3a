# MCX3D Financials v2 - Quick Reference Guide

## 🚀 Instant Commands

### Start Development Environment
```bash
docker-compose up --build  # Start all services
```

### Most Used CLI Commands
```bash
# Generate Income Statement (PDF)
docker-compose exec web python -m mcx3d_finance.cli.main generate income-statement --organization-id 1 --period "2023-01-01,2023-12-31" --format pdf

# Sync Xero Data
docker-compose exec web python -m mcx3d_finance.cli.main sync xero --org-id 1

# Run All Tests
docker-compose exec web pytest

# Check API Health
curl http://localhost:8000/health
```

---

## 📡 Key API Endpoints

| Endpoint | Method | Purpose |
|----------|--------|---------|
| `/docs` | GET | Interactive API documentation |
| `/health` | GET | System health check |
| `/xero/authorize` | GET | Start Xero OAuth flow |
| `/reports/income-statement` | GET | Generate income statement |
| `/reports/balance-sheet` | GET | Generate balance sheet |
| `/api/metrics/saas-kpis` | GET | Calculate SaaS KPIs |

---

## 🏗️ Project Structure Essentials

```
mcx3d_finance/
├── api/           # REST endpoints
├── cli/           # Command-line tools
├── core/          # Business logic
│   ├── financials/    # Report generators
│   ├── metrics/       # SaaS KPIs
│   └── valuation/     # DCF & multiples
├── db/            # Database models
├── integrations/  # Xero API client
└── validation/    # Data validation
```

---

## ⚙️ Configuration Quick Setup

### 1. Copy Environment Template
```bash
cp .env.example .env
```

### 2. Essential Variables
```bash
DATABASE_URL=postgresql://user:password@localhost:5432/mcx3d_db
REDIS_URL=redis://localhost:6379/0
XERO_CLIENT_ID=your_xero_client_id
XERO_CLIENT_SECRET=your_xero_client_secret
SECRET_KEY=your-secret-key-32-chars-minimum
```

### 3. Initialize Database
```bash
docker-compose exec web alembic upgrade head
```

---

## 🧪 Testing Quick Reference

### Run Specific Test Types
```bash
pytest -m unit                    # Unit tests only
pytest -m integration            # Integration tests
pytest -m e2e                    # End-to-end tests
pytest -m performance            # Performance tests
pytest --cov=mcx3d_finance       # With coverage
```

### Test Categories
- `unit` - Isolated component tests
- `integration` - Database & API tests
- `e2e` - Full workflow tests
- `performance` - Benchmark tests
- `smoke` - Critical system validation

---

## 💻 CLI Command Categories

### Data Management
```bash
# Sync commands
sync xero --org-id 1                    # Full sync
sync xero --org-id 1 --incremental      # Incremental sync
```

### Report Generation
```bash
# Financial statements
generate income-statement --help         # See all options
generate balance-sheet --help           # Balance sheet options
generate cash-flow --help               # Cash flow options
```

### Valuation & Analytics
```bash
# Valuation models
valuate dcf --config config.json        # DCF model
valuate multiples --config config.json  # Multiples model

# SaaS analytics
analytics saas-metrics --org-id 1       # SaaS KPIs
analytics growth-analysis --org-id 1    # Growth metrics
```

---

## 🔧 Development Shortcuts

### Code Quality
```bash
black mcx3d_finance/              # Format code
flake8 mcx3d_finance/            # Lint code
mypy mcx3d_finance/              # Type checking
```

### Database Operations
```bash
# Migrations
alembic revision --autogenerate -m "description"  # Create migration
alembic upgrade head                               # Apply migrations
alembic history                                   # View history
```

### Docker Operations
```bash
docker-compose logs web          # View web service logs
docker-compose logs worker       # View Celery worker logs
docker-compose exec web bash     # Access web container
docker-compose down              # Stop all services
```

---

## 📊 Financial Report Formats

### Supported Output Formats
- `json` - Structured data
- `pdf` - Professional reports
- `excel` - Spreadsheet format
- `html` - Web display

### Date Format Examples
```bash
--period "2023-01-01,2023-12-31"    # Full year
--period "2023-Q4"                   # Quarter
--date "2023-12-31"                  # Specific date
```

---

## 🏥 Health Check Quick Status

### System Health Endpoints
```bash
curl http://localhost:8000/         # Basic health
curl http://localhost:8000/detailed # Detailed status
curl http://localhost:8000/readiness # Kubernetes readiness
curl http://localhost:8000/liveness  # Kubernetes liveness
```

### Service Status Indicators
- ✅ **Healthy** - All systems operational
- ⚠️ **Warning** - Some issues detected
- ❌ **Unhealthy** - Service problems
- 🔄 **Starting** - Service initializing

---

## 🔐 Authentication Flow

### Xero OAuth Setup
1. **Register App** - Get client ID/secret from Xero
2. **Configure Environment** - Add credentials to `.env`
3. **Authorize** - Visit `/xero/authorize` endpoint
4. **Handle Callback** - System processes OAuth response
5. **Use APIs** - Access Xero data through system

### API Authentication
```bash
# Login (returns JWT token)
curl -X POST http://localhost:8000/login \
  -H "Content-Type: application/json" \
  -d '{"username": "user", "password": "pass"}'

# Use token in subsequent requests
curl -H "Authorization: Bearer <token>" \
  http://localhost:8000/reports/income-statement
```

---

## ⚠️ Common Issues & Solutions

### Database Connection Issues
```bash
# Check if database is running
docker-compose ps db

# Reset database
docker-compose down
docker volume rm mcx3d_financials_postgres_data
docker-compose up -d db
```

### Xero Authentication Problems
```bash
# Check Xero auth status
curl http://localhost:8000/xero/status/1

# Refresh tokens if expired
curl -X POST http://localhost:8000/xero/refresh/1
```

### Port Conflicts
```bash
# Check what's using port 8000
lsof -i :8000

# Use different port
docker-compose -f docker-compose.yml -p mcx3d up
```

---

## 📚 Documentation Quick Links

| Resource | Location |
|----------|----------|
| **Complete Guide** | [PROJECT_INDEX.md](./PROJECT_INDEX.md) |
| **Navigation** | [DOCUMENTATION_INDEX.md](./DOCUMENTATION_INDEX.md) |
| **API Docs** | `http://localhost:8000/docs` |
| **Deployment** | [DOCKER_DEPLOYMENT_GUIDE.md](./DOCKER_DEPLOYMENT_GUIDE.md) |
| **Performance** | [PERFORMANCE_BENCHMARK_REPORT.md](./PERFORMANCE_BENCHMARK_REPORT.md) |

---

## 🆘 Emergency Commands

### System Recovery
```bash
# Complete system restart
docker-compose down && docker-compose up --build

# Clear all data and restart fresh
docker-compose down -v && docker-compose up --build

# Access logs for debugging
docker-compose logs --tail=100 web
```

### Data Recovery
```bash
# Database backup
docker-compose exec db pg_dump -U user mcx3d_db > backup.sql

# Database restore
docker-compose exec -T db psql -U user mcx3d_db < backup.sql
```

---

*Quick Reference Last Updated: January 2025*