#!/usr/bin/env python3
"""
Startup script for MCX3D Financials application
"""
import uvicorn
from dotenv import load_dotenv
import os
import logging
from mcx3d_finance.main import app

# Load environment variables
load_dotenv()

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

logger = logging.getLogger(__name__)

def main():
    """Start the FastAPI application"""
    logger.info("Starting MCX3D Financials API...")
    logger.info(f"Environment loaded - XERO_CLIENT_ID: {os.getenv('XERO_CLIENT_ID', 'Not set')[:10]}...")
    
    # Start the server
    uvicorn.run(
        app,
        host="0.0.0.0",
        port=8000,
        reload=True,
        log_level="info"
    )

if __name__ == "__main__":
    main()