# MCX3D Financials Environment Configuration
# Copy this file to .env and update with your actual values

# Database Configuration
DATABASE_URL=postgresql://user:password@localhost:5432/mcx3d_finance

# Redis Configuration
REDIS_URL=redis://localhost:6379/0

# Xero OAuth Configuration
XERO_CLIENT_ID=your_xero_client_id
XERO_CLIENT_SECRET=your_xero_client_secret
XERO_REDIRECT_URI=http://localhost:8000/api/auth/xero/callback
XERO_SCOPES=accounting.transactions accounting.contacts accounting.reports.read accounting.settings
XERO_WEBHOOK_KEY=your_xero_webhook_key

# Security Configuration
SECRET_KEY=your-secret-key-here-minimum-32-characters-long
ENCRYPTION_KEY=generate-with-fernet-generate-key-base64-encoded
JWT_ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=30

# Application Configuration
DEBUG=False
LOG_LEVEL=INFO
APP_NAME=MCX3D Finance
APP_VERSION=2.0.0

# API Configuration
API_HOST=0.0.0.0
API_PORT=8000
API_WORKERS=4

# Celery Configuration
CELERY_BROKER_URL=redis://localhost:6379/0
CELERY_RESULT_BACKEND=redis://localhost:6379/0

# CORS Configuration (comma-separated origins)
CORS_ORIGINS=http://localhost:3000,http://localhost:8080

# Rate Limiting
RATE_LIMIT_PER_MINUTE=100
RATE_LIMIT_PER_HOUR=1000

# Email Configuration (for notifications)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USERNAME=<EMAIL>
SMTP_PASSWORD=your_app_password
EMAIL_FROM=<EMAIL>

# Monitoring (optional)
SENTRY_DSN=
DATADOG_API_KEY=

# Testing (only used in test environment)
TEST_DATABASE_URL=postgresql://test_user:test_password@localhost:5432/mcx3d_test
TEST_REDIS_URL=redis://localhost:6379/1