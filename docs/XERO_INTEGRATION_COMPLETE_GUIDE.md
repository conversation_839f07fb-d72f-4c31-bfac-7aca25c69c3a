# MCX3D Finance - Complete Xero Integration Guide

## Overview

This guide consolidates all information about the Xero integration in MCX3D Finance, including OAuth setup, MCP (Model Context Protocol) integration, data import processes, and troubleshooting.

## Table of Contents

1. [Architecture Overview](#architecture-overview)
2. [OAuth2 Setup](#oauth2-setup)
3. [MCP Integration](#mcp-integration)
4. [Data Import Process](#data-import-process)
5. [CLI Commands](#cli-commands)
6. [API Endpoints](#api-endpoints)
7. [Troubleshooting](#troubleshooting)
8. [Development Tools](#development-tools)

## Architecture Overview

The Xero integration consists of several layers:

```
┌─────────────────────────────────────────────────────┐
│                   CLI Interface                      │
│         mcx3d-finance sync xero --org-id 2          │
└─────────────────────┬───────────────────────────────┘
                      │
┌─────────────────────▼───────────────────────────────┐
│                 Celery Task Layer                    │
│              sync_xero_data (async)                  │
└─────────────────────┬───────────────────────────────┘
                      │
┌─────────────────────▼───────────────────────────────┐
│            XeroDataImportService                     │
│     (Orchestrates the import process)                │
└─────────────────────┬───────────────────────────────┘
                      │
┌─────────────────────▼───────────────────────────────┐
│              MCPXeroClient                           │
│    (MCP integration with fallback)                   │
└─────────────────────┬───────────────────────────────┘
                      │
┌─────────────────────▼───────────────────────────────┐
│           XeroDataProcessor                          │
│    (GAAP classification, enrichment)                 │
└─────────────────────┬───────────────────────────────┘
                      │
┌─────────────────────▼───────────────────────────────┐
│          XeroDataStorageService                      │
│        (Database persistence)                        │
└─────────────────────────────────────────────────────┘
```

## OAuth2 Setup

### Configuration

1. **Environment Variables**:
```bash
XERO_CLIENT_ID=your_client_id_here
XERO_CLIENT_SECRET=your_client_secret_here
XERO_REDIRECT_URI=http://localhost:8000/auth/xero/callback
XERO_SCOPES=offline_access accounting.transactions accounting.contacts accounting.settings
```

2. **OAuth Flow**:
- User initiates: `GET /auth/xero/login`
- Redirected to Xero for authorization
- Callback: `GET /auth/xero/callback?code=xxx&state=xxx`
- Token stored encrypted in database

### Token Management

The `XeroAuthManager` handles:
- Token encryption/decryption
- Automatic token refresh
- Multi-tenant support
- Secure storage in PostgreSQL

## MCP Integration

### Overview

MCP (Model Context Protocol) provides an intelligent interface to Xero through Claude Desktop integration.

### Key Components

1. **MCPXeroClient** (`mcp_xero_client.py`):
   - Primary interface for MCP tools
   - Automatic fallback to direct API
   - Error handling and retry logic

2. **MCPXeroBridge** (`mcp_bridge.py`):
   - Bridges MCP tools with OAuth tokens
   - Handles authentication for MCP requests

3. **MCPDataValidator** (`mcp_data_validator.py`):
   - Validates data from MCP responses
   - Ensures data integrity

### MCP Tools Available

- `list-accounts`: Get chart of accounts
- `list-contacts`: Get all contacts
- `list-invoices`: Get invoices with filters
- `list-bank-transactions`: Get bank transactions
- `list-trial-balance`: Get trial balance report
- `list-profit-and-loss`: Get P&L report
- `list-organisation-details`: Get org details

### Usage Example

```python
from mcx3d_finance.integrations.mcp_xero_client import MCPXeroClient

client = MCPXeroClient(organization_id=2)
accounts = client.get_accounts()  # Uses MCP with automatic fallback
```

## Data Import Process

### Complete Import Flow

1. **Import Service** (`XeroDataImportService`):
   - Imports organization details
   - Chart of accounts with GAAP classification
   - Contacts with business enrichment
   - Invoices (sales and purchase)
   - Bank transactions
   - Financial reports

2. **Data Processing** (`XeroDataProcessor`):
   - GAAP account classification
   - Contact enrichment
   - Currency conversion
   - Data validation
   - Duplicate detection

3. **Storage Service** (`XeroDataStorageService`):
   - Upsert logic for all entities
   - Transaction safety
   - Progress tracking
   - Error handling

### Import Statistics

The import process tracks:
- Total records per entity type
- Successfully imported count
- Error count
- Processing time

## CLI Commands

### Basic Sync

```bash
# Full sync with progress
mcx3d-finance sync xero --org-id 2

# Incremental sync (recent changes only)
mcx3d-finance sync xero --org-id 2 --incremental

# Async mode (background processing)
mcx3d-finance sync xero --org-id 2 --async-mode

# Check async task status
mcx3d-finance sync status <task-id>
```

### Advanced Options

```bash
# Without progress bar
mcx3d-finance sync xero --org-id 2 --no-show-progress

# Debug mode
mcx3d-finance --debug sync xero --org-id 2
```

## API Endpoints

### Authentication
- `GET /auth/xero/login` - Initiate OAuth flow
- `GET /auth/xero/callback` - OAuth callback
- `POST /auth/xero/refresh` - Refresh tokens
- `DELETE /auth/xero/disconnect` - Disconnect integration

### Data Import
- `POST /api/sync/xero` - Trigger sync via API
- `GET /api/sync/status/{task_id}` - Check sync status
- `GET /api/sync/history` - Sync history

### Reports
- `GET /api/reports/balance-sheet` - Generate balance sheet
- `GET /api/reports/income-statement` - Generate income statement
- `GET /api/reports/cash-flow` - Generate cash flow

## Troubleshooting

### Common Issues

1. **401 Unauthorized**:
   - Check token expiration
   - Verify OAuth scopes
   - Ensure tenant ID is correct

2. **MCP Connection Failed**:
   - Verify Claude Desktop is running
   - Check MCP server configuration
   - System will fallback to direct API

3. **Import Failures**:
   - Check Xero API limits
   - Verify data permissions
   - Review error logs

### Debug Tools

Located in `scripts/development/`:
- `xero_auth_helper.py` - Test OAuth flow
- `explore_xero.py` - Explore API endpoints
- `test_data_import.py` - Test import process

### Logging

Enable debug logging:
```python
import logging
logging.basicConfig(level=logging.DEBUG)
```

## Development Tools

### Testing

```bash
# Run integration tests
docker-compose exec web pytest tests/integration/test_xero_integration.py

# Test specific component
docker-compose exec web pytest tests/integration/test_mcp_integration.py
```

### Direct Testing

```python
# Test import service
from mcx3d_finance.integrations.xero_data_import import test_import_service
test_import_service()

# Test storage service  
from mcx3d_finance.integrations.xero_data_storage import test_storage_service
test_storage_service()
```

## Database Schema

### Key Tables

1. **organizations**:
   - `xero_tenant_id`: Xero tenant identifier
   - `xero_token`: Encrypted OAuth token
   - `last_sync_at`: Last successful sync

2. **accounts**:
   - `xero_account_id`: Xero's account ID
   - `gaap_classification`: GAAP mapping
   - Full chart of accounts data

3. **contacts**:
   - `xero_contact_id`: Xero's contact ID
   - `business_profile`: Enrichment data
   - Complete contact information

4. **invoices**:
   - `xero_invoice_id`: Xero's invoice ID
   - `line_items`: JSON array
   - Full invoice details

5. **bank_transactions**:
   - `xero_transaction_id`: Xero's transaction ID
   - Complete transaction data

## Best Practices

1. **Always use MCP client first** - It provides intelligent fallback
2. **Monitor sync status** - Check for errors and partial failures
3. **Use incremental sync** - For regular updates after initial import
4. **Handle rate limits** - Xero has API rate limits
5. **Validate data** - Use validation pipeline for data integrity

## Future Enhancements

1. Real-time webhooks for instant updates
2. Advanced reconciliation features
3. Multi-currency support improvements
4. Enhanced error recovery
5. Batch processing optimizations

---

For questions or issues, check the logs in `/var/log/mcx3d-finance/` or run with `--debug` flag.