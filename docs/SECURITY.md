# Security Documentation - MCX3D Finance

## Overview

This document outlines the security architecture, best practices, and implementation guidelines for the MCX3D Finance application. Security is implemented in multiple layers to protect sensitive financial data and ensure compliance with industry standards.

## Security Architecture

### Authentication & Authorization

#### JWT-Based Authentication
- **Algorithm**: HS256 (HMAC with SHA-256)
- **Token Expiration**: 30 minutes (configurable)
- **Token Storage**: Client-side only (localStorage/sessionStorage)
- **Refresh Strategy**: Automatic refresh via Xero OAuth2 refresh tokens

#### Organization-Level Access Control
- Users can belong to multiple organizations
- Role-based permissions: `admin`, `user`, `viewer`
- Organization access verified on every API request
- Admin role required for sensitive operations (token revocation, etc.)

### OAuth2 Integration (Xero)

#### PKCE Support
- State parameter for CSRF protection
- State stored in Redis with 10-minute expiration
- Cryptographically secure random state generation

#### Token Management
- Access tokens encrypted using AES-256 (Fernet)
- Refresh tokens stored encrypted in database
- Automatic token refresh 5 minutes before expiration
- Token revocation tracking

### Data Encryption

#### At Rest
- OAuth tokens: AES-256 encryption using Fernet
- Sensitive PII fields: Field-level encryption
- Database: Use PostgreSQL encryption features in production
- Backups: Encrypted using cloud provider encryption

#### In Transit
- HTTPS required for all API endpoints
- TLS 1.2+ minimum
- Certificate pinning for mobile applications
- Secure WebSocket connections for real-time features

### API Security

#### Rate Limiting
- Default: 100 requests per minute per user
- Report generation: 10 requests per minute
- Configurable per endpoint
- Redis-based distributed rate limiting

#### Security Headers
```
X-Content-Type-Options: nosniff
X-Frame-Options: DENY
X-XSS-Protection: 1; mode=block
Strict-Transport-Security: max-age=31536000; includeSubDomains
Content-Security-Policy: default-src 'self'
```

#### Input Validation
- Pydantic models for request/response validation
- SQL injection prevention via SQLAlchemy ORM
- XSS prevention through proper encoding
- File upload restrictions and scanning

## Security Best Practices

### Development

1. **Environment Variables**
   - Never commit secrets to version control
   - Use `.env` files for local development
   - Use secret management services in production
   - Rotate secrets regularly

2. **Dependencies**
   - Regular security audits with `pip-audit`
   - Pin dependency versions
   - Monitor for CVEs
   - Update dependencies monthly

3. **Code Review**
   - Security-focused code reviews
   - Automated security scanning (SAST)
   - Dependency vulnerability scanning
   - Regular penetration testing

### Deployment

1. **Infrastructure**
   - Use VPC with private subnets
   - Implement WAF rules
   - Enable cloud provider security features
   - Regular security patches

2. **Monitoring**
   - Security event logging
   - Anomaly detection
   - Failed authentication tracking
   - Audit trail for sensitive operations

3. **Backup & Recovery**
   - Encrypted backups
   - Regular backup testing
   - Disaster recovery plan
   - Data retention policies

## Implementation Guidelines

### Generating Secure Keys

```bash
# Generate JWT secret key (minimum 32 characters)
python -c "import secrets; print(secrets.token_urlsafe(32))"

# Generate Fernet encryption key
python -c "from cryptography.fernet import Fernet; print(Fernet.generate_key().decode())"
```

### Password Requirements
- Minimum 12 characters
- Must include uppercase, lowercase, numbers, and symbols
- No common patterns or dictionary words
- Password history enforcement (no reuse of last 5)
- Account lockout after 5 failed attempts

### Session Management
- Session timeout after 30 minutes of inactivity
- Secure session cookies (HttpOnly, Secure, SameSite)
- Session invalidation on logout
- Concurrent session limits

### API Security Checklist

- [ ] All endpoints require authentication (except login/public)
- [ ] Organization access verified for data endpoints
- [ ] Rate limiting applied
- [ ] Input validation on all parameters
- [ ] Error messages don't leak sensitive information
- [ ] Audit logging for sensitive operations
- [ ] CORS properly configured
- [ ] Security headers present

## Incident Response

### Security Incident Procedure

1. **Detection**
   - Monitor security alerts
   - User reports
   - Automated detection

2. **Containment**
   - Isolate affected systems
   - Revoke compromised credentials
   - Block malicious IPs

3. **Investigation**
   - Analyze logs
   - Determine scope
   - Identify root cause

4. **Recovery**
   - Patch vulnerabilities
   - Restore from backups if needed
   - Reset affected credentials

5. **Post-Incident**
   - Document lessons learned
   - Update security procedures
   - Notify affected users if required

### Contact Information

- Security Team: <EMAIL>
- Emergency: +1-XXX-XXX-XXXX
- Bug Bounty: <EMAIL>

## Compliance

### Standards
- OWASP Top 10 compliance
- GDPR data protection
- SOC 2 Type II (planned)
- ISO 27001 (planned)

### Data Privacy
- Minimal data collection
- User consent for data processing
- Right to deletion (GDPR Article 17)
- Data portability
- Privacy by design

## Security Tools

### Recommended Tools
- **SAST**: Bandit for Python
- **Dependency Scanning**: pip-audit, safety
- **Secrets Scanning**: truffleHog, git-secrets
- **WAF**: CloudFlare, AWS WAF
- **Monitoring**: Datadog, Sentry

### Security Testing

```bash
# Run security tests
pytest tests/security/ -v

# Check for vulnerabilities
pip-audit

# Scan for secrets
truffleHog filesystem ./

# Python security linting
bandit -r mcx3d_finance/
```

## Regular Security Tasks

### Daily
- Monitor security alerts
- Review authentication logs
- Check for failed login attempts

### Weekly
- Review access logs
- Update security patches
- Verify backup integrity

### Monthly
- Rotate API keys
- Security training
- Dependency updates
- Access review

### Quarterly
- Penetration testing
- Security audit
- Disaster recovery drill
- Policy review

## Security Configuration

### Production Settings

```python
# Never use these in production - generate your own!
SECRET_KEY = os.environ.get('SECRET_KEY')  # Min 32 chars
ENCRYPTION_KEY = os.environ.get('ENCRYPTION_KEY')  # Fernet key

# Security settings
SECURE_SSL_REDIRECT = True
SESSION_COOKIE_SECURE = True
CSRF_COOKIE_SECURE = True
SECURE_HSTS_SECONDS = 31536000
SECURE_HSTS_INCLUDE_SUBDOMAINS = True
SECURE_HSTS_PRELOAD = True
```

## Reporting Security Issues

If you discover a security vulnerability, please:

1. **Do NOT** create a public GitHub issue
2. Email <EMAIL> with details
3. Include steps to reproduce
4. Allow 90 days for patching before disclosure

We appreciate responsible disclosure and may offer rewards for significant findings.

## Version History

- v2.0.0 - Initial security implementation
- v2.1.0 - Added OAuth2 with PKCE support
- v2.2.0 - Enhanced encryption and rate limiting

Last Updated: 2024-01-21