# MCX3D Developer Testing Guide

Welcome to the MCX3D Financial System testing infrastructure! This guide will help you get up to speed quickly with our testing practices and tools.

## 🚀 Quick Setup (5 minutes)

### 1. Install Dependencies
```bash
# Clone the repository
git clone <repository-url>
cd mcx3d_financials/v2

# Install dependencies
pip install -r requirements.txt
pip install -r requirements-test.txt

# Verify installation
pytest --version
```

### 2. Generate Test Data
```bash
# Generate minimal test data for development
./scripts/setup_test_data.sh quick

# Verify test data was created
ls -la test_data/
```

### 3. Run Your First Tests
```bash
# Run basic validation tests
pytest tests/test_basic_validation.py -v

# Run smoke tests
pytest -m smoke -v

# Expected output: All tests should pass
```

**🎉 Success!** You're ready to start testing. If any step fails, see the [Troubleshooting](#troubleshooting) section.

## 🧭 Navigation Guide

### What Should I Test First?

**If you're new to the codebase:**
1. Start with `tests/test_basic_validation.py` to understand the system
2. Look at `tests/fixtures/valuation_data.py` for test data patterns
3. Run `pytest -m unit` to see unit test examples

**If you're working on a specific feature:**
1. Find related tests in the appropriate directory:
   - Core logic: `tests/core/`
   - API endpoints: `tests/api/`
   - Report generation: `tests/integration/test_report_generation.py`
   - CLI: `tests/e2e/test_cli_exports.py`

**If you're debugging an issue:**
1. Run `./scripts/production_health_check.py` for system health
2. Check `pytest -m smoke` for critical functionality
3. Use `pytest --lf` to run last failed tests

### Test Categories Quick Reference

| Category | When to Use | Example Command |
|----------|-------------|-----------------|
| **Unit** | Testing individual functions | `pytest -m unit` |
| **Integration** | Testing component interactions | `pytest -m integration` |
| **E2E** | Testing complete workflows | `pytest -m e2e` |
| **Smoke** | Validating critical functionality | `pytest -m smoke` |
| **Performance** | Checking speed/memory | `pytest -m performance` |

## 💡 Common Development Scenarios

### Adding a New Feature

#### 1. Write Tests First (TDD Approach)
```python
# tests/core/test_new_feature.py
import pytest
from mcx3d_finance.core.new_feature import NewFeatureCalculator

@pytest.mark.unit
def test_new_calculation():
    """Test the new calculation logic."""
    calculator = NewFeatureCalculator()
    result = calculator.calculate(input_value=100)
    
    # This test will fail initially - that's expected!
    assert result == 150  # Expected output
```

#### 2. Run the Failing Test
```bash
# Run your new test - it should fail
pytest tests/core/test_new_feature.py::test_new_calculation -v

# Expected: FAILED (because feature doesn't exist yet)
```

#### 3. Implement the Feature
```python
# mcx3d_finance/core/new_feature.py
class NewFeatureCalculator:
    def calculate(self, input_value):
        return input_value * 1.5  # Simple implementation
```

#### 4. Verify Tests Pass
```bash
# Run the test again - it should pass now
pytest tests/core/test_new_feature.py::test_new_calculation -v

# Expected: PASSED
```

### Debugging a Failing Test

#### 1. Get Detailed Information
```bash
# Run with maximum verbosity
pytest tests/failing_test.py -vvv --tb=long

# Use debugger if needed
pytest tests/failing_test.py --pdb
```

#### 2. Check System Health
```bash
# Run health checks to rule out system issues
./scripts/production_health_check.py

# Check if test data is corrupted
python scripts/manage_test_data.py validate --file test_data/quick_test_data.json
```

#### 3. Isolate the Problem
```bash
# Run just the failing test
pytest tests/specific_test.py::test_failing_function -s

# Run tests that were previously passing
pytest --lf  # Last failed tests only
```

### Working with Test Data

#### Using Existing Test Data
```python
import pytest
from tests.fixtures.valuation_data import create_dcf_data

def test_with_realistic_data():
    # Use pre-built test data
    dcf_data = create_dcf_data(complexity="simple")
    
    # Your test logic here
    assert dcf_data["enterprise_value"] > 0
```

#### Creating Custom Test Data
```python
@pytest.fixture
def custom_test_data():
    """Create test data specific to your test."""
    return {
        "company_name": "My Test Company",
        "enterprise_value": 5000000,
        "financial_projections": [
            {
                "year": 1,
                "revenue": 1000000,
                "free_cash_flow": 200000
            }
        ]
    }

def test_with_custom_data(custom_test_data):
    # Use your custom data
    assert custom_test_data["company_name"] == "My Test Company"
```

#### Generating Large Test Datasets
```bash
# For performance testing
./scripts/setup_test_data.sh performance

# For integration testing
./scripts/setup_test_data.sh enterprise
```

## 🎯 Testing Patterns and Best Practices

### Test Structure (Arrange-Act-Assert)
```python
def test_dcf_calculation():
    # Arrange: Set up test data
    cash_flows = [-1000, 300, 400, 500]
    discount_rate = 0.12
    
    # Act: Execute the function
    present_value = calculate_present_value(cash_flows, discount_rate)
    
    # Assert: Verify the result
    assert present_value == pytest.approx(856.07, rel=1e-2)
```

### Testing Error Conditions
```python
def test_invalid_input_handling():
    """Test that invalid input is handled gracefully."""
    with pytest.raises(ValueError, match="Discount rate must be positive"):
        calculate_present_value([1000], discount_rate=-0.1)
```

### Parameterized Tests for Multiple Scenarios
```python
@pytest.mark.parametrize("input_amount,expected_format", [
    (1000, "$1.00K"),
    (1500000, "$1.50M"),
    (2000000000, "$2.00B"),
])
def test_currency_formatting(input_amount, expected_format):
    result = format_currency(input_amount)
    assert result == expected_format
```

### Testing with Temporary Files
```python
import tempfile
from pathlib import Path

def test_report_generation():
    with tempfile.NamedTemporaryFile(suffix=".pdf", delete=False) as temp_file:
        temp_path = temp_file.name
    
    try:
        # Generate report to temporary file
        generate_report(output_path=temp_path)
        
        # Verify file was created and has content
        assert Path(temp_path).exists()
        assert Path(temp_path).stat().st_size > 1000
        
    finally:
        # Clean up
        if Path(temp_path).exists():
            Path(temp_path).unlink()
```

## 🔧 Development Tools

### Running Tests During Development

#### Watch Mode (Auto-run tests on file changes)
```bash
# Install pytest-watch
pip install pytest-watch

# Auto-run tests when files change
ptw -- -m unit  # Watch unit tests only
ptw -- tests/core/  # Watch specific directory
```

#### Quick Feedback Loop
```bash
# Run only fast tests during development
pytest -m "unit and not slow" -x  # Stop on first failure

# Run tests related to your changes
pytest --lf  # Last failed tests
pytest --ff  # Failed first, then passing tests
```

#### Debugging with Print Statements
```python
def test_debug_example():
    result = complex_calculation()
    
    # Use print for debugging (will show with -s flag)
    print(f"Debug: result = {result}")
    
    assert result > 0

# Run with print output visible
pytest tests/test_file.py -s
```

### Code Coverage During Development

```bash
# Check coverage for your changes
pytest --cov=mcx3d_finance.core.new_module --cov-report=term-missing

# Visual coverage report
pytest --cov=mcx3d_finance --cov-report=html
open htmlcov/index.html
```

### Performance Testing During Development

```bash
# Quick performance check
pytest -m performance --benchmark-skip  # Skip slow benchmarks

# Baseline comparison
pytest tests/performance/test_report_performance.py --benchmark-compare

# Memory usage monitoring
pytest tests/performance/ --memray
```

## 🚀 Advanced Development Patterns

### Mocking External Dependencies

```python
from unittest.mock import patch, MagicMock

@patch('mcx3d_finance.integrations.xero.XeroClient')
def test_xero_integration(mock_xero):
    # Setup mock behavior
    mock_xero.return_value.get_organizations.return_value = [
        {"id": "123", "name": "Test Org"}
    ]
    
    # Test your code
    result = sync_organizations()
    
    # Verify mock was called correctly
    mock_xero.return_value.get_organizations.assert_called_once()
    assert len(result) == 1
```

### Testing Async Code

```python
import pytest
import asyncio

@pytest.mark.asyncio
async def test_async_function():
    result = await async_calculation()
    assert result > 0

# Or using the event loop directly
def test_async_with_loop():
    loop = asyncio.get_event_loop()
    result = loop.run_until_complete(async_calculation())
    assert result > 0
```

### Property-Based Testing

```python
from hypothesis import given, strategies as st

@given(st.floats(min_value=0.01, max_value=1.0))
def test_discount_rate_properties(discount_rate):
    """Test properties that should always be true."""
    present_value = calculate_present_value(1000, discount_rate, 5)
    
    # These should always be true regardless of input
    assert present_value > 0
    assert present_value < 1000  # Should be discounted
```

## 📊 Monitoring Your Code Quality

### Pre-commit Hooks

```bash
# Install pre-commit hooks
pip install pre-commit
pre-commit install

# Run hooks manually
pre-commit run --all-files
```

### Quality Checks

```bash
# Run all quality checks
pytest --cov=mcx3d_finance --cov-fail-under=85
flake8 mcx3d_finance/
black --check mcx3d_finance/

# Fix formatting issues
black mcx3d_finance/
```

### CI/CD Integration

When you push code, GitHub Actions will automatically:
1. Run all unit tests
2. Check code coverage (must be ≥85%)
3. Run integration tests
4. Perform security scans
5. Validate performance benchmarks

## 🎓 Learning Path

### Week 1: Getting Comfortable
- [ ] Set up development environment
- [ ] Run existing tests successfully
- [ ] Write your first unit test
- [ ] Use test fixtures for data
- [ ] Debug a failing test

### Week 2: Building Confidence
- [ ] Write integration tests
- [ ] Use parameterized tests
- [ ] Mock external dependencies
- [ ] Generate and validate test data
- [ ] Run performance tests

### Week 3: Advanced Techniques
- [ ] Write end-to-end tests
- [ ] Create custom test fixtures
- [ ] Implement property-based tests
- [ ] Set up continuous testing
- [ ] Contribute to testing infrastructure

## 🚨 Troubleshooting

### Common Issues and Solutions

#### "ImportError: No module named 'mcx3d_finance'"
```bash
# Solution: Install in development mode
pip install -e .

# Or add to Python path
export PYTHONPATH="${PYTHONPATH}:$(pwd)"
```

#### "Database connection failed"
```bash
# Solution: Set up test database
export DATABASE_URL=postgresql://test:test@localhost:5432/mcx3d_test

# Or use SQLite for local testing
export DATABASE_URL=sqlite:///test.db
```

#### "Test data files not found"
```bash
# Solution: Generate test data
./scripts/setup_test_data.sh quick

# Verify files exist
ls -la test_data/
```

#### "pytest: command not found"
```bash
# Solution: Install pytest
pip install pytest

# Or use python -m pytest
python -m pytest tests/
```

#### Tests are very slow
```bash
# Solution: Run faster subset
pytest -m "unit and not slow"

# Use parallel execution
pip install pytest-xdist
pytest -n auto  # Use all CPU cores
```

#### Permission denied errors
```bash
# Solution: Fix file permissions
chmod +x scripts/*.py
chmod +x scripts/*.sh

# Or run with python directly
python scripts/setup_test_data.py
```

### Getting Help

#### Debug Information
```bash
# Get system information
./scripts/production_health_check.py --verbose

# Check test environment
pytest --collect-only  # List all tests
pytest --markers  # List all markers
```

#### Log Files
- Test execution logs: `/tmp/mcx3d_test.log`
- Health check logs: `/tmp/mcx3d_health_check.log`
- Performance results: `performance_baseline.json`

#### Team Resources
- Ask in #testing Slack channel
- Review team testing standards
- Check existing test examples
- Pair programming sessions available

## 📋 Testing Checklist

### Before Submitting Code
- [ ] All existing tests pass (`pytest`)
- [ ] New functionality has tests
- [ ] Code coverage ≥85% for new code
- [ ] Integration tests pass
- [ ] Smoke tests pass
- [ ] No security issues detected
- [ ] Performance within thresholds

### Before Deployment
- [ ] All tests pass in CI/CD
- [ ] Production health checks pass
- [ ] Deployment validation successful
- [ ] Performance benchmarks met
- [ ] Security scans clean

## 🏆 Best Practices Summary

### Test Writing
1. **Write tests first** (TDD approach)
2. **Use descriptive names** that explain behavior
3. **Keep tests focused** on single functionality
4. **Make tests independent** of each other
5. **Use realistic test data**

### Performance
1. **Run fast tests frequently**
2. **Use appropriate test markers**
3. **Parallel execution** for large test suites
4. **Mock expensive operations**
5. **Monitor performance trends**

### Maintenance
1. **Clean up test data**
2. **Update tests when behavior changes**
3. **Remove obsolete tests**
4. **Keep test code quality high**
5. **Document complex test scenarios**

---

## 🎯 Next Steps

Now that you're familiar with the testing infrastructure:

1. **Start Testing**: Pick a feature and write your first test
2. **Explore Examples**: Look at existing tests for patterns
3. **Join the Community**: Participate in testing discussions
4. **Improve the System**: Suggest enhancements to testing tools
5. **Share Knowledge**: Help other developers get started

Welcome to the MCX3D testing community! 🚀

For more detailed information, see the [Complete Testing Guide](./TESTING_GUIDE.md).