# MCX3D Financial System - Testing Guide

This comprehensive guide covers all aspects of testing in the MCX3D Financial System, from development practices to production validation.

## 🚀 Quick Start

### Running Tests

```bash
# Run all tests
pytest

# Run specific test categories
pytest -m unit                    # Unit tests only
pytest -m integration            # Integration tests
pytest -m e2e                   # End-to-end tests
pytest -m smoke                 # Critical smoke tests
pytest -m production            # Production validation tests

# Run tests with coverage
pytest --cov=mcx3d_finance --cov-report=html

# Run performance tests
pytest -m performance --benchmark-save=baseline
```

### Test Data Setup

```bash
# Generate test data
./scripts/setup_test_data.sh quick       # Minimal dataset
./scripts/setup_test_data.sh standard    # Standard dataset
./scripts/setup_test_data.sh enterprise  # Large dataset

# Run health checks
./scripts/production_health_check.py

# Validate deployment
./scripts/validate_deployment.py --environment production
```

## 📁 Test Structure

### Directory Layout

```
tests/
├── core/                          # Core business logic tests
│   ├── test_data_validation.py    # Data validation tests
│   ├── test_financial_calculations.py
│   └── test_business_rules.py
├── integration/                   # Integration tests
│   ├── test_report_generation.py  # Report generation integration
│   ├── test_output_quality.py     # PDF/Excel validation
│   └── test_xero_integration.py   # External API integration
├── e2e/                          # End-to-end tests
│   ├── test_cli_exports.py       # CLI workflow tests
│   └── test_api_workflows.py     # API workflow tests
├── performance/                   # Performance tests
│   ├── test_report_performance.py # Load and stress tests
│   └── test_memory_usage.py      # Memory profiling
├── production/                    # Production validation
│   ├── test_smoke_tests.py       # Critical functionality
│   └── test_production_validation.py # Environment validation
├── fixtures/                     # Test data and utilities
│   ├── valuation_data.py         # Test data fixtures
│   └── test_utilities.py         # Helper functions
└── conftest.py                   # Global pytest configuration
```

## 🎯 Test Categories

### 1. Unit Tests (`@pytest.mark.unit`)

**Purpose**: Test individual components in isolation.

**Characteristics**:
- Fast execution (< 1 second each)
- No external dependencies
- Focused on single functions/classes
- High code coverage target (>90%)

**Example**:
```python
@pytest.mark.unit
def test_dcf_calculation():
    calculator = DCFCalculator()
    result = calculator.calculate_present_value(
        future_value=1000,
        discount_rate=0.10,
        years=5
    )
    assert result == pytest.approx(620.92, rel=1e-2)
```

### 2. Integration Tests (`@pytest.mark.integration`)

**Purpose**: Test component interactions and data flow.

**Characteristics**:
- Medium execution time (1-10 seconds)
- May use databases/external services
- Test realistic scenarios
- Focus on interface contracts

**Example**:
```python
@pytest.mark.integration
def test_report_generation_with_database():
    # Test complete flow from database to report
    org_data = create_test_organization()
    report = generate_balance_sheet(org_data.id)
    assert report.total_assets > 0
    assert report.assets_equal_liabilities_plus_equity()
```

### 3. End-to-End Tests (`@pytest.mark.e2e`)

**Purpose**: Test complete user workflows.

**Characteristics**:
- Longer execution time (10-60 seconds)
- Full system integration
- Realistic user scenarios
- Business value validation

**Example**:
```python
@pytest.mark.e2e
def test_complete_valuation_workflow():
    # Complete workflow from CLI to final report
    result = runner.invoke(cli, [
        'valuate', 'dcf',
        '--config', 'test_config.json',
        '--export', 'pdf'
    ])
    assert result.exit_code == 0
    assert "valuation completed" in result.output.lower()
```

### 4. Performance Tests (`@pytest.mark.performance`)

**Purpose**: Validate performance characteristics.

**Characteristics**:
- Execution time varies
- Memory and CPU monitoring
- Baseline comparison
- Regression detection

**Example**:
```python
@pytest.mark.performance 
def test_large_dataset_performance(benchmark):
    large_dataset = generate_large_dataset(10000)
    result = benchmark(process_large_dataset, large_dataset)
    assert result.processing_time < 30.0
```

### 5. Smoke Tests (`@pytest.mark.smoke`)

**Purpose**: Critical functionality validation for production.

**Characteristics**:
- Fast execution (< 30 seconds total)
- Critical path focus
- Production-safe data
- High reliability requirement

**Example**:
```python
@pytest.mark.smoke
@pytest.mark.production
def test_core_report_generation():
    """Must pass for production deployment."""
    report = generate_minimal_report()
    assert report.is_valid()
    assert report.file_size > 1000
```

## 🔧 Test Configuration

### pytest.ini Configuration

```ini
[tool:pytest]
testpaths = tests
addopts = 
    --cov=mcx3d_finance
    --cov-report=html
    --cov-fail-under=85
    --strict-markers
markers =
    unit: Unit tests for isolated components
    integration: Integration tests with dependencies
    e2e: End-to-end workflow tests
    performance: Performance and benchmark tests
    smoke: Critical smoke tests
    production: Production validation tests
```

### Environment Variables

```bash
# Test configuration
export TESTING=true
export DATABASE_URL=postgresql://test:test@localhost:5432/mcx3d_test
export REDIS_URL=redis://localhost:6379/1

# Production validation
export ENVIRONMENT=production
export SECRET_KEY=your-production-secret-key
```

## 📊 Test Data Management

### Test Data Categories

1. **Minimal Data**: Basic functionality testing
2. **Standard Data**: Realistic business scenarios
3. **Enterprise Data**: Large-scale, complex scenarios
4. **Performance Data**: High-volume datasets
5. **Edge Cases**: Boundary conditions and error cases

### Data Generation

```python
# Using test fixtures
@pytest.fixture
def sample_dcf_data():
    return {
        "company_name": "Test Corp",
        "enterprise_value": 10000000,
        "financial_projections": [...]
    }

# Using data factories
from tests.fixtures.valuation_data import create_dcf_data
dcf_data = create_dcf_data(complexity="medium")
```

### Data Management Scripts

```bash
# Generate test data
python scripts/seed_test_data.py --scenario enterprise

# Anonymize for production testing
python scripts/manage_test_data.py anonymize \
    --input test_data.json \
    --level high \
    --output production_safe_data.json

# Validate data integrity
python scripts/manage_test_data.py validate --file test_data.json
```

## 🏭 Production Testing

### Pre-Deployment Validation

```bash
# Complete deployment validation
./scripts/validate_deployment.py --environment production --exit-code

# Health checks
./scripts/production_health_check.py --json-output

# Smoke tests
pytest -m smoke --tb=short -v
```

### Production Monitoring

```bash
# Continuous health monitoring
./scripts/production_health_check.py --config production_config.json

# Performance monitoring
pytest -m performance --benchmark-compare=baseline

# System validation
pytest -m production -v
```

### Validation Checklist

- [ ] All smoke tests pass
- [ ] Performance within thresholds
- [ ] Security configuration validated
- [ ] Database connectivity confirmed
- [ ] External service integration working
- [ ] Logging and monitoring operational

## 🚀 CI/CD Integration

### GitHub Actions Workflow

```yaml
name: MCX3D Test Suite

on: [push, pull_request]

jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      
      - name: Run unit tests
        run: pytest -m "unit and not slow" --cov-report=xml
      
      - name: Run integration tests
        run: pytest -m "integration and not slow"
      
      - name: Run smoke tests
        run: pytest -m smoke
      
      - name: Upload coverage
        uses: codecov/codecov-action@v3
```

### Quality Gates

1. **Unit Tests**: 90%+ coverage, all passing
2. **Integration Tests**: All critical paths working
3. **Smoke Tests**: 100% pass rate required
4. **Performance Tests**: Within baseline thresholds
5. **Security Scans**: No high-severity issues

## 📈 Performance Testing

### Baseline Establishment

```bash
# Establish performance baselines
python scripts/establish_baselines.py

# Run performance tests
pytest -m performance --benchmark-save=current

# Compare with baseline
pytest -m performance --benchmark-compare=baseline
```

### Performance Thresholds

| Test Category | Threshold | Measurement |
|--------------|-----------|------------|
| DCF PDF Generation | < 5 seconds | Single report |
| SaaS PDF Generation | < 5 seconds | Single report |
| Concurrent Reports (3) | < 15 seconds | Total time |
| Memory Usage | < 500MB | Peak usage |
| Database Queries | < 100ms | Average response |

### Load Testing

```python
@pytest.mark.performance
def test_concurrent_load():
    """Test system under concurrent load."""
    with ThreadPoolExecutor(max_workers=10) as executor:
        futures = [
            executor.submit(generate_report, test_data) 
            for _ in range(50)
        ]
        results = [f.result() for f in futures]
    
    success_rate = sum(1 for r in results if r.success) / len(results)
    assert success_rate > 0.95  # 95% success rate
```

## 🔒 Security Testing

### Security Test Categories

1. **Input Validation**: SQL injection, XSS prevention
2. **Authentication**: OAuth flows, token validation
3. **Authorization**: Role-based access control
4. **Data Protection**: Encryption, PII handling
5. **Infrastructure**: Security headers, HTTPS

### Security Testing Example

```python
@pytest.mark.security
def test_sql_injection_prevention():
    """Test that SQL injection attempts are blocked."""
    malicious_input = "'; DROP TABLE organizations; --"
    
    with pytest.raises(ValidationError):
        search_organizations(name=malicious_input)
    
    # Verify database integrity
    assert Organization.query.count() > 0
```

## 🛠 Development Workflow

### Test-Driven Development (TDD)

1. **Red**: Write failing test
2. **Green**: Write minimal code to pass
3. **Refactor**: Improve code while keeping tests green

```python
# Step 1: Write failing test
def test_calculate_irr():
    cash_flows = [-1000, 300, 400, 500, 600]
    irr = calculate_irr(cash_flows)
    assert irr == pytest.approx(0.28, rel=1e-2)

# Step 2: Implement function
def calculate_irr(cash_flows):
    # Implementation here
    pass

# Step 3: Refactor and optimize
```

### Testing Best Practices

1. **Test Naming**: Use descriptive, behavior-focused names
2. **Test Structure**: Arrange-Act-Assert pattern
3. **Test Isolation**: Each test should be independent
4. **Data Management**: Use fixtures and factories
5. **Error Testing**: Test both success and failure cases

### Code Review Guidelines

- [ ] Tests cover new functionality
- [ ] Tests are maintainable and readable
- [ ] Performance impact considered
- [ ] Security implications reviewed
- [ ] Documentation updated

## 📚 Testing Tools and Libraries

### Core Testing Stack

- **pytest**: Test framework
- **pytest-cov**: Coverage reporting
- **pytest-benchmark**: Performance testing
- **pytest-xdist**: Parallel execution
- **pytest-mock**: Mocking utilities

### Specialized Libraries

- **PyPDF2**: PDF validation
- **openpyxl**: Excel file testing
- **psutil**: System resource monitoring
- **faker**: Test data generation
- **factory_boy**: Object factory patterns

### Installation

```bash
pip install -r requirements-test.txt

# Or install specific testing dependencies
pip install pytest pytest-cov pytest-benchmark PyPDF2 openpyxl psutil
```

## 🔍 Debugging Tests

### Common Issues and Solutions

#### Test Failures

```bash
# Run with detailed output
pytest -vvv --tb=long

# Run specific failing test
pytest tests/core/test_calculations.py::test_dcf_calculation -vvv

# Debug with pdb
pytest --pdb tests/core/test_calculations.py
```

#### Performance Issues

```bash
# Profile test execution
pytest --profile-svg

# Memory profiling
pytest --memray tests/performance/

# Identify slow tests
pytest --durations=10
```

#### Flaky Tests

```bash
# Run tests multiple times
pytest --count=10 tests/integration/

# Identify timing issues
pytest -x --lf  # Stop on first failure, run last failed
```

## 📖 Advanced Testing Patterns

### Parameterized Tests

```python
@pytest.mark.parametrize("input_value,expected", [
    (1000000, "1.00M"),
    (1500000, "1.50M"),
    (2000000, "2.00M"),
])
def test_format_currency(input_value, expected):
    assert format_currency(input_value) == expected
```

### Property-Based Testing

```python
from hypothesis import given, strategies as st

@given(st.floats(min_value=0.01, max_value=1.0))
def test_discount_rate_always_positive(discount_rate):
    result = calculate_present_value(1000, discount_rate, 5)
    assert result > 0
    assert result < 1000  # Should be less than future value
```

### Mock and Patch Patterns

```python
@patch('mcx3d_finance.integrations.xero.XeroClient')
def test_xero_integration(mock_xero):
    mock_xero.return_value.get_organizations.return_value = [
        {"id": "123", "name": "Test Org"}
    ]
    
    result = sync_organizations()
    assert len(result) == 1
    assert result[0]["name"] == "Test Org"
```

## 🎯 Coverage and Quality Metrics

### Coverage Targets

- **Overall Coverage**: ≥85%
- **Core Business Logic**: ≥95%
- **Critical Path Functions**: 100%
- **Integration Points**: ≥90%

### Quality Metrics

```bash
# Generate coverage report
pytest --cov=mcx3d_finance --cov-report=html --cov-report=term

# Check coverage thresholds
pytest --cov=mcx3d_finance --cov-fail-under=85

# Complexity analysis
radon cc mcx3d_finance/ -a
```

### Continuous Monitoring

```yaml
# .github/workflows/quality.yml
- name: Check coverage
  run: |
    pytest --cov=mcx3d_finance --cov-fail-under=85
    
- name: Upload to Codecov
  uses: codecov/codecov-action@v3
  with:
    file: ./coverage.xml
```

## 🚨 Troubleshooting

### Common Test Issues

#### Import Errors
```bash
# Ensure project is in Python path
export PYTHONPATH="${PYTHONPATH}:$(pwd)"

# Install in development mode
pip install -e .
```

#### Database Issues
```bash
# Reset test database
dropdb mcx3d_test && createdb mcx3d_test
alembic upgrade head

# Run with fresh database
pytest --create-db
```

#### Docker Issues
```bash
# Rebuild test containers
docker-compose -f docker-compose.test.yml build --no-cache

# Run tests in Docker
docker-compose -f docker-compose.test.yml run test pytest
```

### Performance Debugging

```python
# Add timing to tests
import time

def test_with_timing():
    start = time.time()
    result = slow_function()
    duration = time.time() - start
    
    assert duration < 5.0  # Performance requirement
    assert result is not None
```

### Memory Debugging

```python
import psutil
import os

def test_memory_usage():
    process = psutil.Process(os.getpid())
    initial_memory = process.memory_info().rss
    
    # Run memory-intensive operation
    large_dataset = generate_large_dataset()
    process_dataset(large_dataset)
    
    final_memory = process.memory_info().rss
    memory_increase = final_memory - initial_memory
    
    # Memory increase should be reasonable
    assert memory_increase < 100 * 1024 * 1024  # 100MB
```

## 📞 Support and Resources

### Documentation
- [Testing Best Practices](./TESTING_BEST_PRACTICES.md)
- [Performance Testing Guide](./PERFORMANCE_TESTING.md)
- [Test Data Management](../scripts/README_test_data.md)

### Tools and Scripts
- `./scripts/setup_test_data.sh` - Test data generation
- `./scripts/production_health_check.py` - Health monitoring
- `./scripts/validate_deployment.py` - Deployment validation

### Getting Help
- Check test logs in `/tmp/mcx3d_test.log`
- Review GitHub Actions build logs
- Run health checks for system validation
- Consult team testing guidelines

---

## 🏆 Testing Excellence

The MCX3D Financial System maintains high testing standards through:

- **Comprehensive Coverage**: Unit, integration, and end-to-end tests
- **Performance Validation**: Continuous performance monitoring
- **Production Safety**: Smoke tests and deployment validation
- **Quality Gates**: Automated quality checks in CI/CD
- **Developer Experience**: Clear documentation and helpful tools

By following this testing guide, you'll contribute to a reliable, high-quality financial system that meets the demanding requirements of our users.