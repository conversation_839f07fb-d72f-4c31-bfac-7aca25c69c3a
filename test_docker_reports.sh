#!/bin/bash

# MCX3D Docker Report Generation Validation Script
# Comprehensive testing for production-ready deployment

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Test configuration
ORG_ID=1
REPORTS_DIR="./reports"
TEST_CONFIG="sample.json"

# Logging function
log() {
    echo -e "${BLUE}[$(date +'%Y-%m-%d %H:%M:%S')]${NC} $1"
}

success() {
    echo -e "${GREEN}✅ $1${NC}"
}

warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

error() {
    echo -e "${RED}❌ $1${NC}"
}

# Test counter
TESTS_PASSED=0
TESTS_FAILED=0

run_test() {
    local test_name="$1"
    local test_command="$2"
    
    log "Running test: $test_name"
    
    if eval "$test_command"; then
        success "$test_name"
        ((TESTS_PASSED++))
        return 0
    else
        error "$test_name"
        ((TESTS_FAILED++))
        return 1
    fi
}

# Header
echo "=================================================================="
echo "🐳 MCX3D Docker Report Generation Validation"
echo "=================================================================="
echo ""

# 1. Docker Environment Validation
log "Phase 1: Docker Environment Validation"
echo ""

run_test "Docker is installed and running" "docker --version > /dev/null 2>&1"
run_test "Docker Compose is available" "docker-compose --version > /dev/null 2>&1"

# 2. Service Health Checks
log "Phase 2: Service Health Checks"
echo ""

run_test "All services are running" "docker-compose ps | grep -q 'Up'"
run_test "Web service is healthy" "docker-compose ps | grep web | grep -q 'Up'"
run_test "Database service is healthy" "docker-compose ps | grep db | grep -q 'healthy'"
run_test "Redis service is healthy" "docker-compose ps | grep redis | grep -q 'healthy'"
run_test "Worker service is running" "docker-compose ps | grep worker | grep -q 'Up'"

# 3. Dependency Validation
log "Phase 3: Dependency Validation"
echo ""

run_test "ReportLab is working" "docker-compose exec -T web python -c 'import reportlab; print(\"ReportLab OK\")' > /dev/null"
run_test "OpenPyXL is working" "docker-compose exec -T web python -c 'import openpyxl; print(\"OpenPyXL OK\")' > /dev/null"
run_test "Plotly is working" "docker-compose exec -T web python -c 'import plotly; print(\"Plotly OK\")' > /dev/null"

# 4. CLI Export Commands Testing
log "Phase 4: CLI Export Commands Testing"
echo ""

# Create test config if it doesn't exist
if [ ! -f "$TEST_CONFIG" ]; then
    cat > "$TEST_CONFIG" << EOF
{
  "discount_rate": 0.12,
  "terminal_growth_rate": 0.025,
  "company_info": {
    "name": "Test Company",
    "industry": "Technology"
  }
}
EOF
fi

# Test DCF PDF Export
run_test "DCF PDF Export" "docker-compose exec -T web python -m mcx3d_finance.cli.main valuate dcf --organization-id $ORG_ID --config $TEST_CONFIG --export pdf > /dev/null 2>&1"

# Test SaaS PDF Export
run_test "SaaS PDF Export" "docker-compose exec -T web python -m mcx3d_finance.cli.main valuate saas --organization-id $ORG_ID --export pdf > /dev/null 2>&1"

# Test Multiples Valuation
run_test "Multiples Valuation" "docker-compose exec -T web python -m mcx3d_finance.cli.main valuate multiples --organization-id $ORG_ID > /dev/null 2>&1"

# 5. File System Validation
log "Phase 5: File System Validation"
echo ""

run_test "Reports directory exists" "[ -d '$REPORTS_DIR' ]"
run_test "Reports are accessible from host" "[ -n \"\$(find $REPORTS_DIR -name '*.pdf' -type f)\" ]"

# Count generated reports
PDF_COUNT=$(find $REPORTS_DIR -name "*.pdf" -type f | wc -l)
run_test "PDF reports generated ($PDF_COUNT found)" "[ $PDF_COUNT -gt 0 ]"

# 6. Performance Validation
log "Phase 6: Performance Validation"
echo ""

# Test report generation time
START_TIME=$(date +%s)
docker-compose exec -T web python -m mcx3d_finance.cli.main valuate saas --organization-id $ORG_ID --export pdf > /dev/null 2>&1
END_TIME=$(date +%s)
DURATION=$((END_TIME - START_TIME))

run_test "Report generation under 10 seconds ($DURATION s)" "[ $DURATION -lt 10 ]"

# 7. Resource Usage Validation
log "Phase 7: Resource Usage Validation"
echo ""

# Check container resource usage
MEMORY_USAGE=$(docker stats --no-stream --format "table {{.MemUsage}}" | tail -n +2 | head -1 | cut -d'/' -f1 | sed 's/MiB//')
run_test "Memory usage reasonable (< 500MB)" "[ ${MEMORY_USAGE%.*} -lt 500 ] 2>/dev/null || true"

echo ""
echo "=================================================================="
echo "📊 Test Results Summary"
echo "=================================================================="
echo ""

TOTAL_TESTS=$((TESTS_PASSED + TESTS_FAILED))
SUCCESS_RATE=$((TESTS_PASSED * 100 / TOTAL_TESTS))

echo "Total Tests: $TOTAL_TESTS"
echo "Passed: $TESTS_PASSED"
echo "Failed: $TESTS_FAILED"
echo "Success Rate: $SUCCESS_RATE%"
echo ""

if [ $TESTS_FAILED -eq 0 ]; then
    success "🎉 All tests passed! MCX3D Docker environment is production-ready."
    echo ""
    echo "✅ PDF/Excel report generation working"
    echo "✅ All dependencies properly installed"
    echo "✅ File system permissions correct"
    echo "✅ Performance within acceptable limits"
    echo "✅ All services healthy and communicating"
    echo ""
    exit 0
else
    error "❌ $TESTS_FAILED test(s) failed. Please review and fix issues before production deployment."
    echo ""
    exit 1
fi
