{"timestamp": "2025-07-23T01:27:09.272074", "system_info": {"python_version": "3.9.23 (main, Jul  2 2025, 01:48:09) \n[GCC 12.2.0]", "platform": "posix"}, "tests": {"dcf_simple": {"data_type": "dcf", "complexity": "simple", "iterations": 3, "successful_iterations": 3, "times": {"average": 0.028774182001749676, "minimum": 0.015288114547729492, "maximum": 0.05495190620422363, "all_times": [0.05495190620422363, 0.0160825252532959, 0.015288114547729492]}, "file_sizes": {"average": 3802.0, "minimum": 3802, "maximum": 3802, "all_sizes": [3802, 3802, 3802]}}, "dcf_medium": {"data_type": "dcf", "complexity": "medium", "iterations": 3, "successful_iterations": 3, "times": {"average": 0.03312365214029948, "minimum": 0.031694889068603516, "maximum": 0.03592538833618164, "all_times": [0.03592538833618164, 0.03175067901611328, 0.031694889068603516]}, "file_sizes": {"average": 4158.0, "minimum": 4158, "maximum": 4158, "all_sizes": [4158, 4158, 4158]}}, "dcf_complex": {"data_type": "dcf", "complexity": "complex", "iterations": 3, "successful_iterations": 3, "times": {"average": 0.0364683469136556, "minimum": 0.03642988204956055, "maximum": 0.03652834892272949, "all_times": [0.03652834892272949, 0.03642988204956055, 0.03644680976867676]}, "file_sizes": {"average": 4414.0, "minimum": 4414, "maximum": 4414, "all_sizes": [4414, 4414, 4414]}}, "saas_simple": {"data_type": "saas", "complexity": "simple", "iterations": 3, "successful_iterations": 3, "times": {"average": 0.022048473358154297, "minimum": 0.021924972534179688, "maximum": 0.022110939025878906, "all_times": [0.022110939025878906, 0.021924972534179688, 0.022109508514404297]}, "file_sizes": {"average": 4717.0, "minimum": 4717, "maximum": 4717, "all_sizes": [4717, 4717, 4717]}}, "saas_medium": {"data_type": "saas", "complexity": "medium", "iterations": 3, "successful_iterations": 3, "times": {"average": 0.025625308354695637, "minimum": 0.025116682052612305, "maximum": 0.026102542877197266, "all_times": [0.025116682052612305, 0.025656700134277344, 0.026102542877197266]}, "file_sizes": {"average": 5280.0, "minimum": 5280, "maximum": 5280, "all_sizes": [5280, 5280, 5280]}}, "saas_complex": {"data_type": "saas", "complexity": "complex", "iterations": 3, "successful_iterations": 3, "times": {"average": 0.025190114974975586, "minimum": 0.0249941349029541, "maximum": 0.02535557746887207, "all_times": [0.02535557746887207, 0.0249941349029541, 0.025220632553100586]}, "file_sizes": {"average": 5289.0, "minimum": 5289, "maximum": 5289, "all_sizes": [5289, 5289, 5289]}}}}