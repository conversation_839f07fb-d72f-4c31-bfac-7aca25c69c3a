# MCX3D Financials v2 - Project Index

## 📋 Table of Contents

- [🚀 Quick Start](#-quick-start)
- [🏗️ System Architecture](#️-system-architecture)
  - [Core Components](#core-components)
  - [Data Flow](#data-flow)
  - [Technology Stack](#technology-stack)
- [📡 API Reference](#-api-reference)
  - [Authentication Endpoints](#authentication-endpoints)
  - [Financial Reports](#financial-reports)
  - [SaaS Metrics](#saas-metrics)
  - [Xero Integration](#xero-integration)
  - [Health & Monitoring](#health--monitoring)
- [💻 CLI Commands](#-cli-commands)
  - [Data Synchronization](#data-synchronization)
  - [Report Generation](#report-generation)
  - [Valuation Models](#valuation-models)
  - [Analytics & KPIs](#analytics--kpis)
- [🧩 Core Modules](#-core-modules)
  - [Financial Calculations](#financial-calculations)
  - [Data Processing](#data-processing)
  - [Validation System](#validation-system)
  - [Account Classification](#account-classification)
- [⚠️ Exception Handling](#️-exception-handling)
- [🧪 Testing Framework](#-testing-framework)
- [🚀 Deployment Guide](#-deployment-guide)
- [📚 Development Resources](#-development-resources)

---

## 🚀 Quick Start

### Prerequisites
- Docker & Docker Compose
- Python 3.9+
- PostgreSQL 13+
- Redis 6.2+

### Setup
```bash
# Clone and navigate to project
git clone <repository> && cd mcx3d_financials/v2

# Copy environment configuration
cp .env.example .env

# Start all services
docker-compose up --build

# Run tests
docker-compose exec web pytest
```

### First Steps
1. **Configure Xero OAuth**: Update `.env` with Xero credentials
2. **Initialize Database**: `docker-compose exec web alembic upgrade head`
3. **Test API**: Visit `http://localhost:8000/docs` for interactive documentation
4. **Run CLI**: `docker-compose exec web python -m mcx3d_finance.cli.main --help`

---

## 🏗️ System Architecture

### Core Components

```mermaid
graph TB
    A[FastAPI Application] --> B[Database Layer]
    A --> C[Redis Cache]
    A --> D[Celery Workers]
    
    B --> E[PostgreSQL]
    C --> F[Background Tasks]
    D --> G[Report Generation]
    
    A --> H[Xero Integration]
    H --> I[OAuth Authentication]
    H --> J[Data Synchronization]
    
    A --> K[Validation Engine]
    K --> L[Real-time Validation]
    K --> M[Business Rules]
```

### Data Flow

1. **Ingestion**: Xero API → `XeroDataProcessor` → Raw Data
2. **Validation**: `IntegratedValidationEngine` → Quality Scoring
3. **Classification**: `TransactionClassifier` → GAAP Mapping
4. **Transformation**: `BatchTransformationEngine` → Enrichment
5. **Storage**: Validated Data → PostgreSQL
6. **Reporting**: Financial Generators → PDF/Excel Output

### Technology Stack

| Component | Technology | Purpose |
|-----------|------------|---------|
| **API Framework** | FastAPI + Uvicorn | REST API with async support |
| **Database** | PostgreSQL + SQLAlchemy | Relational data storage |
| **Cache/Queue** | Redis + Celery | Caching & background tasks |
| **Financial Engine** | Pandas + NumPy | Data processing & calculations |
| **Reporting** | ReportLab + OpenPyXL | PDF & Excel generation |
| **Integration** | Xero Python SDK | External API connectivity |
| **Validation** | Pydantic + Custom | Data validation & schemas |
| **Authentication** | OAuth 2.0 + JWT | Secure API access |

---

## 📡 API Reference

### Authentication Endpoints

| Method | Endpoint | Description |
|--------|----------|-------------|
| `POST` | `/login` | User authentication |
| `GET` | `/xero/authorize` | Initiate Xero OAuth flow |
| `GET` | `/xero/callback` | Handle OAuth callback |
| `POST` | `/xero/refresh/{org_id}` | Refresh access tokens |
| `DELETE` | `/xero/revoke/{org_id}` | Revoke Xero access |
| `GET` | `/xero/status/{org_id}` | Check authentication status |
| `POST` | `/logout` | User logout |

### Financial Reports

| Method | Endpoint | Description |
|--------|----------|-------------|
| `GET` | `/reports/income-statement` | Generate income statement |
| `GET` | `/reports/balance-sheet` | Generate balance sheet |
| `GET` | `/reports/cash-flow` | Generate cash flow statement |
| `POST` | `/valuation/dcf` | DCF valuation model |
| `POST` | `/valuation/multiples` | Multiples-based valuation |

**Query Parameters:**
- `organization_id`: Organization identifier
- `start_date`, `end_date`: Report date range (YYYY-MM-DD)
- `format`: Output format (json, pdf, excel, html)
- `currency`: Base currency (default: USD)

### SaaS Metrics

| Method | Endpoint | Description |
|--------|----------|-------------|
| `GET` | `/api/metrics/saas-kpis` | Calculate SaaS KPIs |

**Metrics Included:**
- Monthly Recurring Revenue (MRR)
- Annual Recurring Revenue (ARR)
- Customer Acquisition Cost (CAC)
- Lifetime Value (LTV)
- Churn Rate
- Net Revenue Retention (NRR)

### Xero Integration

| Method | Endpoint | Description |
|--------|----------|-------------|
| `POST` | `/import` | Import Xero data |
| `GET` | `/auth-status/{org_id}` | Check Xero auth status |
| `GET` | `/sync-status/{org_id}` | Get sync status |
| `POST` | `/sync/{org_id}` | Trigger data sync |
| `GET` | `/task-status/{task_id}` | Check task progress |

### Health & Monitoring

| Method | Endpoint | Description |
|--------|----------|-------------|
| `GET` | `/` | Basic health check |
| `GET` | `/detailed` | Detailed system status |
| `GET` | `/reporting` | Report system health |
| `GET` | `/readiness` | Kubernetes readiness probe |
| `GET` | `/liveness` | Kubernetes liveness probe |

---

## 💻 CLI Commands

The CLI provides comprehensive financial data management capabilities:

### Data Synchronization

```bash
# Sync all data from Xero
python -m mcx3d_finance.cli.main sync xero --org-id 123

# Incremental sync (only changes)
python -m mcx3d_finance.cli.main sync xero --org-id 123 --incremental

# Force full resync
python -m mcx3d_finance.cli.main sync xero --org-id 123 --force
```

### Report Generation

```bash
# Income Statement
python -m mcx3d_finance.cli.main generate income-statement \
  --organization-id 123 \
  --period "2023-01-01,2023-12-31" \
  --format pdf

# Balance Sheet
python -m mcx3d_finance.cli.main generate balance-sheet \
  --organization-id 123 \
  --date "2023-12-31" \
  --format excel

# Cash Flow Statement
python -m mcx3d_finance.cli.main generate cash-flow \
  --organization-id 123 \
  --period "2023-Q4" \
  --format html
```

### Valuation Models

```bash
# DCF Valuation with professional export
python -m mcx3d_finance.cli.main valuate dcf \
  --config dcf_config.json \
  --export pdf

# Multiples Valuation
python -m mcx3d_finance.cli.main valuate multiples \
  --config multiples_config.json \
  --export excel

# SaaS-specific valuation
python -m mcx3d_finance.cli.main valuate saas \
  --organization-id 123 \
  --export pdf
```

### Analytics & KPIs

```bash
# SaaS Metrics Analysis
python -m mcx3d_finance.cli.main analytics saas-metrics \
  --organization-id 123 \
  --period-months 12 \
  --output detailed

# Growth Analysis
python -m mcx3d_finance.cli.main analytics growth-analysis \
  --organization-id 123 \
  --period-months 24

# Financial Health Score
python -m mcx3d_finance.cli.main analytics health-score \
  --organization-id 123 \
  --output summary
```

---

## 🧩 Core Modules

### Financial Calculations

**Location**: `mcx3d_finance/core/financials/`

| Module | Purpose |
|--------|---------|
| `balance_sheet.py` | NASDAQ-compliant balance sheet generation |
| `income_statement.py` | Income statement with earnings calculations |
| `cash_flow.py` | Cash flow statement generation |
| `utils.py` | Common financial utilities |

**Key Classes:**
- `BalanceSheetGenerator`: Assets, liabilities, equity calculations
- `IncomeStatementGenerator`: Revenue, expenses, net income
- `CashFlowGenerator`: Operating, investing, financing activities

### Data Processing

**Location**: `mcx3d_finance/core/`

| Module | Purpose |
|--------|---------|
| `data_processors.py` | Central Xero data processing orchestrator |
| `transaction_classifier.py` | ML-based transaction categorization |
| `duplicate_detector.py` | Fuzzy matching for duplicate detection |
| `transformation_engine.py` | Batch data transformation with quality scoring |
| `currency_converter.py` | Multi-currency support |

**Key Features:**
- GAAP compliance automation
- Real-time data quality scoring
- Configurable transformation rules
- Industry-specific classifications

### Validation System

**Location**: `mcx3d_finance/validation/` & `mcx3d_finance/core/validation_integration.py`

| Module | Purpose |
|--------|---------|
| `financial_validator.py` | Financial data integrity checks |
| `business_rules.py` | Business logic validation |
| `schema_validator.py` | Data structure validation |
| `report_validator.py` | Report generation validation |
| `validation_integration.py` | Real-time validation engine |

**Validation Layers:**
1. **Schema Validation**: Data type & structure checks
2. **Business Rules**: Financial logic validation
3. **Data Integrity**: Cross-reference validation
4. **Compliance**: GAAP & regulatory compliance

### Account Classification

**Location**: `mcx3d_finance/core/account_classifications.py`

**GAAP Categories:**
- **Assets**: Current, non-current, intangible
- **Liabilities**: Current, long-term, contingent
- **Equity**: Share capital, retained earnings
- **Revenue**: Operating, non-operating
- **Expenses**: Operating, interest, tax

---

## ⚠️ Exception Handling

Comprehensive exception hierarchy for robust error handling:

### Base Exceptions

| Exception | Purpose |
|-----------|---------|
| `MCX3DException` | Base exception for all system errors |
| `MCX3DConfigurationError` | Configuration and setup errors |
| `MCX3DSystemError` | System-level failures |
| `MCX3DResourceError` | Resource allocation issues |
| `MCX3DTimeoutError` | Operation timeout errors |

### Financial Exceptions

| Exception | Use Case |
|-----------|----------|
| `FinancialCalculationError` | Mathematical calculation failures |
| `FinancialDataError` | Invalid financial data |
| `ValuationError` | Valuation model errors |
| `DCFCalculationError` | DCF-specific calculation issues |
| `SaaSValuationError` | SaaS metrics validation errors |

### Integration Exceptions

| Exception | Use Case |
|-----------|----------|
| `XeroIntegrationError` | Xero API communication issues |
| `APIConnectionError` | Network connectivity problems |
| `AuthenticationError` | OAuth authentication failures |
| `RateLimitError` | API rate limiting |
| `DataSyncError` | Data synchronization failures |

### Reporting Exceptions

| Exception | Use Case |
|-----------|----------|
| `ReportGenerationError` | Report creation failures |
| `ReportDataValidationError` | Report data validation issues |
| `ReportOutputError` | File generation problems |
| `ChartGenerationError` | Chart/graph creation issues |

### Validation Exceptions

| Exception | Use Case |
|-----------|----------|
| `ValidationError` | General validation failures |
| `DataIntegrityError` | Data consistency issues |
| `SchemaValidationError` | Schema compliance failures |
| `BusinessRuleValidationError` | Business logic violations |

---

## 🧪 Testing Framework

### Test Structure

```
tests/
├── core/              # Core business logic tests
├── integration/       # Database & API integration tests
├── e2e/              # End-to-end workflow tests
├── performance/      # Performance & benchmark tests
└── fixtures/         # Test data and fixtures
```

### Test Categories

| Marker | Purpose | Coverage |
|--------|---------|----------|
| `unit` | Isolated component testing | ≥85% required |
| `integration` | Database & external services | Database models, API clients |
| `e2e` | Full system workflows | Complete user journeys |
| `performance` | Performance benchmarking | Response times, memory usage |
| `smoke` | Critical system validation | Production health checks |

### Running Tests

```bash
# All tests with coverage
docker-compose exec web pytest --cov=mcx3d_finance

# Specific test categories
docker-compose exec web pytest -m unit
docker-compose exec web pytest -m integration
docker-compose exec web pytest -m e2e

# Performance tests
docker-compose exec web pytest -m performance --benchmark-only

# Generate HTML coverage report
docker-compose exec web pytest --cov-report=html
```

---

## 🚀 Deployment Guide

### Docker Compose Services

| Service | Image | Purpose | Ports |
|---------|-------|---------|-------|
| `web` | Custom Python 3.9 | FastAPI application | 8000 |
| `db` | PostgreSQL 13 | Primary database | 5432 |
| `redis` | Redis 6.2 | Cache & task queue | 6379 |
| `worker` | Custom Python 3.9 | Celery background worker | - |

### Environment Configuration

Essential environment variables:

```bash
# Database
DATABASE_URL=**********************************/mcx3d_db

# Redis
REDIS_URL=redis://redis:6379/0

# Xero Integration
XERO_CLIENT_ID=your_client_id
XERO_CLIENT_SECRET=your_client_secret
XERO_WEBHOOK_KEY=your_webhook_key

# Security
SECRET_KEY=your-secret-key-32-chars-minimum
ENCRYPTION_KEY=fernet-generated-base64-key
```

### Production Considerations

1. **Security**: Use strong secrets, enable HTTPS
2. **Scaling**: Configure multiple Celery workers
3. **Monitoring**: Implement health checks and logging
4. **Backup**: Regular database backups
5. **Performance**: Enable Redis persistence, optimize queries

---

## 📚 Development Resources

### Configuration Files

| File | Purpose |
|------|---------|
| `config.yml` | Application configuration |
| `.env.example` | Environment template |
| `alembic.ini` | Database migration settings |
| `pyproject.toml` | Black formatter configuration |
| `pytest.ini` | Test framework settings |

### Development Tools

| Tool | Purpose | Configuration |
|------|---------|---------------|
| **Black** | Code formatting | 88 character line length |
| **Flake8** | Code linting | PEP 8 compliance |
| **Pytest** | Testing framework | 85% coverage requirement |
| **Alembic** | Database migrations | Auto-generation support |

### Key Dependencies

| Package | Version | Purpose |
|---------|---------|---------|
| `fastapi` | Latest | Web framework |
| `sqlalchemy` | Latest | ORM |
| `pandas` | Latest | Data analysis |
| `reportlab` | Latest | PDF generation |
| `xero-python` | Latest | Xero API integration |
| `celery` | Latest | Background tasks |

### Getting Help

1. **Documentation**: Check existing markdown files
2. **API Reference**: Visit `/docs` endpoint
3. **CLI Help**: Use `--help` flag with any command
4. **Error Messages**: Enhanced error reporting with recovery suggestions
5. **Debug Mode**: Use `--debug` flag for detailed information

---

*Last Updated: January 2025*