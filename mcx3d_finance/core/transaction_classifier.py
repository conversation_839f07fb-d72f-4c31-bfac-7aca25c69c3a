"""
Intelligent transaction categorization using rules-based classification.
Provides automated transaction classification with machine learning-like pattern matching.
"""

from typing import Dict, Optional, Any, List, Tuple
from decimal import Decimal
import logging
from enum import Enum
from dataclasses import dataclass
import re
from functools import lru_cache
from datetime import datetime

logger = logging.getLogger(__name__)


class TransactionCategory(Enum):
    """Transaction categories for classification."""
    # Revenue Categories
    PRODUCT_SALES = "product_sales"
    SERVICE_REVENUE = "service_revenue"
    SUBSCRIPTION_REVENUE = "subscription_revenue"
    INTEREST_INCOME = "interest_income"
    OTHER_INCOME = "other_income"
    
    # Expense Categories
    COST_OF_GOODS_SOLD = "cost_of_goods_sold"
    PAYROLL_EXPENSES = "payroll_expenses"
    RENT_UTILITIES = "rent_utilities"
    MARKETING_ADVERTISING = "marketing_advertising"
    PROFESSIONAL_SERVICES = "professional_services"
    TRAVEL_ENTERTAINMENT = "travel_entertainment"
    OFFICE_SUPPLIES = "office_supplies"
    INSURANCE = "insurance"
    TAXES_FEES = "taxes_fees"
    INTEREST_EXPENSE = "interest_expense"
    DEPRECIATION = "depreciation"
    OTHER_EXPENSES = "other_expenses"
    
    # Asset/Liability Categories
    ACCOUNTS_RECEIVABLE = "accounts_receivable"
    ACCOUNTS_PAYABLE = "accounts_payable"
    INVENTORY_PURCHASE = "inventory_purchase"
    EQUIPMENT_PURCHASE = "equipment_purchase"
    LOAN_PAYMENT = "loan_payment"
    INVESTMENT = "investment"
    
    # Cash Flow Categories
    CUSTOMER_PAYMENT = "customer_payment"
    VENDOR_PAYMENT = "vendor_payment"
    BANK_TRANSFER = "bank_transfer"
    CASH_WITHDRAWAL = "cash_withdrawal"
    CASH_DEPOSIT = "cash_deposit"
    
    # Unknown
    UNCATEGORIZED = "uncategorized"


class ConfidenceLevel(Enum):
    """Confidence levels for classification."""
    HIGH = "high"      # 0.8 - 1.0
    MEDIUM = "medium"  # 0.5 - 0.8
    LOW = "low"        # 0.2 - 0.5
    VERY_LOW = "very_low"  # 0.0 - 0.2


@dataclass
class ClassificationRule:
    """Transaction classification rule."""
    category: TransactionCategory
    patterns: List[str]  # Regex patterns to match
    amount_range: Optional[Tuple[Decimal, Decimal]] = None  # Min, Max amount
    account_patterns: Optional[List[str]] = None  # Account name/code patterns
    contact_patterns: Optional[List[str]] = None  # Contact/vendor patterns
    confidence_score: float = 1.0
    description: str = ""
    
    def matches(self, transaction: Dict[str, Any]) -> Tuple[bool, float]:
        """Check if this rule matches the transaction."""
        try:
            total_score = 0.0
            max_score = 0.0
            
            # Check description patterns
            description = transaction.get("description", "").lower()
            if self.patterns:
                max_score += 1.0
                pattern_match = any(
                    re.search(pattern, description, re.IGNORECASE)
                    for pattern in self.patterns
                )
                if pattern_match:
                    total_score += 1.0
            
            # Check amount range
            if self.amount_range:
                max_score += 0.5
                amount = abs(Decimal(str(transaction.get("amount", 0))))
                min_amount, max_amount = self.amount_range
                if min_amount <= amount <= max_amount:
                    total_score += 0.5
            
            # Check account patterns
            if self.account_patterns:
                max_score += 0.3
                account_name = transaction.get("account_name", "").lower()
                account_code = transaction.get("account_code", "").lower()
                account_match = any(
                    re.search(pattern, account_name, re.IGNORECASE) or
                    re.search(pattern, account_code, re.IGNORECASE)
                    for pattern in self.account_patterns
                )
                if account_match:
                    total_score += 0.3
            
            # Check contact patterns
            if self.contact_patterns:
                max_score += 0.2
                contact_name = transaction.get("contact_name", "").lower()
                contact_match = any(
                    re.search(pattern, contact_name, re.IGNORECASE)
                    for pattern in self.contact_patterns
                )
                if contact_match:
                    total_score += 0.2
            
            # Calculate final score
            if max_score > 0:
                final_score = (total_score / max_score) * self.confidence_score
                return total_score > 0, final_score
            
            return False, 0.0
            
        except Exception as e:
            logger.error(f"Error matching classification rule: {e}")
            return False, 0.0


@dataclass
class ClassificationResult:
    """Result of transaction classification."""
    transaction_id: str
    category: TransactionCategory
    confidence_score: float
    confidence_level: ConfidenceLevel
    rule_description: str
    suggested_account: Optional[str] = None
    
    @property
    def is_reliable(self) -> bool:
        """Check if classification is reliable (high or medium confidence)."""
        return self.confidence_level in [ConfidenceLevel.HIGH, ConfidenceLevel.MEDIUM]


class TransactionClassifier:
    """Intelligent transaction classifier using rules-based approach."""
    
    def __init__(self):
        self.classification_rules = self._load_classification_rules()
        self.custom_rules: List[ClassificationRule] = []
        self._classification_cache: Dict[str, ClassificationResult] = {}
    
    def _load_classification_rules(self) -> List[ClassificationRule]:
        """Load comprehensive classification rules."""
        rules = []
        
        # Revenue rules
        rules.extend(self._get_revenue_rules())
        
        # Expense rules
        rules.extend(self._get_expense_rules())
        
        # Asset/Liability rules
        rules.extend(self._get_asset_liability_rules())
        
        # Cash flow rules
        rules.extend(self._get_cash_flow_rules())
        
        return rules
    
    def _get_revenue_rules(self) -> List[ClassificationRule]:
        """Get revenue classification rules."""
        return [
            ClassificationRule(
                category=TransactionCategory.PRODUCT_SALES,
                patterns=[
                    r"sale|sold|product|merchandise|inventory",
                    r"customer payment|invoice.*paid|receipt"
                ],
                amount_range=(Decimal("0"), Decimal("*********")),
                confidence_score=0.9,
                description="Product sales revenue"
            ),
            ClassificationRule(
                category=TransactionCategory.SERVICE_REVENUE,
                patterns=[
                    r"service|consulting|professional|labor|hourly",
                    r"contract|project|maintenance|support"
                ],
                amount_range=(Decimal("0"), Decimal("*********")),
                confidence_score=0.9,
                description="Service revenue"
            ),
            ClassificationRule(
                category=TransactionCategory.SUBSCRIPTION_REVENUE,
                patterns=[
                    r"subscription|monthly|annual|recurring|saas",
                    r"license|software|platform|membership"
                ],
                amount_range=(Decimal("0"), Decimal("*********")),
                confidence_score=0.95,
                description="Subscription revenue"
            ),
            ClassificationRule(
                category=TransactionCategory.INTEREST_INCOME,
                patterns=[
                    r"interest.*income|bank.*interest|dividend",
                    r"investment.*income|yield"
                ],
                confidence_score=0.9,
                description="Interest and investment income"
            ),
        ]
    
    def _get_expense_rules(self) -> List[ClassificationRule]:
        """Get expense classification rules."""
        return [
            ClassificationRule(
                category=TransactionCategory.PAYROLL_EXPENSES,
                patterns=[
                    r"salary|wage|payroll|employee|staff",
                    r"benefits|401k|health.*insurance|fica|tax.*withholding"
                ],
                confidence_score=0.95,
                description="Payroll and employee expenses"
            ),
            ClassificationRule(
                category=TransactionCategory.RENT_UTILITIES,
                patterns=[
                    r"rent|lease|utilities|electric|gas|water|internet",
                    r"phone|telecom|facility|building|office.*space"
                ],
                confidence_score=0.9,
                description="Rent and utilities"
            ),
            ClassificationRule(
                category=TransactionCategory.MARKETING_ADVERTISING,
                patterns=[
                    r"marketing|advertising|promotion|campaign",
                    r"google.*ads|facebook.*ads|linkedin|social.*media"
                ],
                confidence_score=0.9,
                description="Marketing and advertising"
            ),
            ClassificationRule(
                category=TransactionCategory.PROFESSIONAL_SERVICES,
                patterns=[
                    r"legal|attorney|lawyer|accounting|cpa|consultant",
                    r"professional.*services|advisory|audit"
                ],
                confidence_score=0.9,
                description="Professional services"
            ),
            ClassificationRule(
                category=TransactionCategory.TRAVEL_ENTERTAINMENT,
                patterns=[
                    r"travel|flight|hotel|meal|restaurant|entertainment",
                    r"conference|training|seminar|uber|taxi|mileage"
                ],
                confidence_score=0.85,
                description="Travel and entertainment"
            ),
            ClassificationRule(
                category=TransactionCategory.OFFICE_SUPPLIES,
                patterns=[
                    r"office.*supplies|stationery|paper|printer|ink",
                    r"computer|software|equipment|furniture"
                ],
                confidence_score=0.8,
                description="Office supplies and equipment"
            ),
            ClassificationRule(
                category=TransactionCategory.INSURANCE,
                patterns=[
                    r"insurance|premium|coverage|policy",
                    r"liability|property|workers.*comp"
                ],
                confidence_score=0.9,
                description="Insurance expenses"
            ),
            ClassificationRule(
                category=TransactionCategory.TAXES_FEES,
                patterns=[
                    r"tax|fee|penalty|fine|license|permit",
                    r"registration|filing|government|irs|state.*tax"
                ],
                confidence_score=0.9,
                description="Taxes and fees"
            ),
        ]

    @lru_cache(maxsize=1000)
    def classify_transaction(self, transaction_data: str) -> ClassificationResult:
        """Classify a single transaction (cached for performance)."""
        import json
        transaction = json.loads(transaction_data)
        return self._classify_transaction_internal(transaction)

    def _classify_transaction_internal(self, transaction: Dict[str, Any]) -> ClassificationResult:
        """Internal method for transaction classification."""
        try:
            transaction_id = transaction.get("id", "unknown")

            # Check cache first
            cache_key = f"{transaction_id}_{hash(str(transaction))}"
            if cache_key in self._classification_cache:
                return self._classification_cache[cache_key]

            # Try custom rules first (highest priority)
            for rule in self.custom_rules:
                matches, score = rule.matches(transaction)
                if matches and score > 0.5:
                    result = ClassificationResult(
                        transaction_id=transaction_id,
                        category=rule.category,
                        confidence_score=score,
                        confidence_level=self._get_confidence_level(score),
                        rule_description=rule.description
                    )
                    self._classification_cache[cache_key] = result
                    return result

            # Try standard rules
            best_match = None
            best_score = 0.0

            for rule in self.classification_rules:
                matches, score = rule.matches(transaction)
                if matches and score > best_score:
                    best_match = rule
                    best_score = score

            if best_match and best_score > 0.3:  # Minimum threshold
                result = ClassificationResult(
                    transaction_id=transaction_id,
                    category=best_match.category,
                    confidence_score=best_score,
                    confidence_level=self._get_confidence_level(best_score),
                    rule_description=best_match.description
                )
                self._classification_cache[cache_key] = result
                return result

            # Fallback to uncategorized
            result = ClassificationResult(
                transaction_id=transaction_id,
                category=TransactionCategory.UNCATEGORIZED,
                confidence_score=0.1,
                confidence_level=ConfidenceLevel.VERY_LOW,
                rule_description="No matching classification rule found"
            )
            self._classification_cache[cache_key] = result
            return result

        except Exception as e:
            logger.error(f"Error classifying transaction: {e}")
            return ClassificationResult(
                transaction_id=transaction.get("id", "unknown"),
                category=TransactionCategory.UNCATEGORIZED,
                confidence_score=0.0,
                confidence_level=ConfidenceLevel.VERY_LOW,
                rule_description=f"Classification error: {str(e)}"
            )

    def _get_confidence_level(self, score: float) -> ConfidenceLevel:
        """Convert numeric score to confidence level."""
        if score >= 0.8:
            return ConfidenceLevel.HIGH
        elif score >= 0.5:
            return ConfidenceLevel.MEDIUM
        elif score >= 0.2:
            return ConfidenceLevel.LOW
        else:
            return ConfidenceLevel.VERY_LOW

    def classify_transactions_batch(
        self,
        transactions: List[Dict[str, Any]]
    ) -> Dict[str, Any]:
        """Classify multiple transactions in batch."""
        try:
            logger.info(f"Classifying {len(transactions)} transactions")

            results = []
            classification_stats: Dict[str, Any] = {
                "total_transactions": len(transactions),
                "high_confidence": 0,
                "medium_confidence": 0,
                "low_confidence": 0,
                "very_low_confidence": 0,
                "categories": {},
                "uncategorized": 0
            }

            for transaction in transactions:
                # Convert to JSON string for caching
                import json
                transaction_data = json.dumps(transaction, sort_keys=True, default=str)
                classification = self.classify_transaction(transaction_data)

                # Update statistics
                if classification.confidence_level == ConfidenceLevel.HIGH:
                    classification_stats["high_confidence"] += 1
                elif classification.confidence_level == ConfidenceLevel.MEDIUM:
                    classification_stats["medium_confidence"] += 1
                elif classification.confidence_level == ConfidenceLevel.LOW:
                    classification_stats["low_confidence"] += 1
                else:
                    classification_stats["very_low_confidence"] += 1

                # Count categories
                category = classification.category.value
                if category not in classification_stats["categories"]:
                    classification_stats["categories"][category] = 0
                classification_stats["categories"][category] += 1

                if classification.category == TransactionCategory.UNCATEGORIZED:
                    classification_stats["uncategorized"] += 1

                # Create enhanced transaction record
                enhanced_transaction = transaction.copy()
                enhanced_transaction.update({
                    "classification": {
                        "category": classification.category.value,
                        "confidence_score": classification.confidence_score,
                        "confidence_level": classification.confidence_level.value,
                        "rule_description": classification.rule_description,
                        "is_reliable": classification.is_reliable
                    }
                })

                results.append(enhanced_transaction)

            # Calculate overall quality metrics
            total_reliable = (classification_stats["high_confidence"] +
                            classification_stats["medium_confidence"])
            reliability_rate = (total_reliable / len(transactions) * 100) if transactions else 0

            return {
                "classified_transactions": results,
                "classification_statistics": classification_stats,
                "quality_metrics": {
                    "reliability_rate": reliability_rate,
                    "categorization_rate": ((len(transactions) - classification_stats["uncategorized"]) /
                                          len(transactions) * 100) if transactions else 0,
                    "average_confidence": sum(
                        r["classification"]["confidence_score"] for r in results
                    ) / len(results) if results else 0
                }
            }

        except Exception as e:
            logger.error(f"Error in batch classification: {e}")
            return {"error": str(e), "classified_transactions": transactions}

    def add_custom_rule(self, rule: ClassificationRule):
        """Add a custom classification rule."""
        self.custom_rules.append(rule)
        self._classification_cache.clear()
        self.classify_transaction.cache_clear()

    def train_from_examples(
        self,
        training_data: List[Dict[str, Any]]
    ) -> Dict[str, Any]:
        """Train classifier from manually categorized examples."""
        try:
            logger.info(f"Training classifier from {len(training_data)} examples")

            # Analyze patterns in training data
            category_patterns: Dict[str, Any] = {}

            for example in training_data:
                category = example.get("category")
                description = example.get("description", "").lower()

                if category and description:
                    if category not in category_patterns:
                        category_patterns[category] = []
                    category_patterns[category].append(description)

            # Generate new rules from patterns
            new_rules = []
            for category, descriptions in category_patterns.items():
                # Find common words/patterns
                common_patterns = self._extract_common_patterns(descriptions)

                if common_patterns:
                    try:
                        transaction_category = TransactionCategory(category)
                        rule = ClassificationRule(
                            category=transaction_category,
                            patterns=common_patterns,
                            confidence_score=0.7,  # Lower confidence for learned rules
                            description=f"Learned rule for {category}"
                        )
                        new_rules.append(rule)
                    except ValueError:
                        logger.warning(f"Unknown category in training data: {category}")

            # Add new rules
            for rule in new_rules:
                self.add_custom_rule(rule)

            return {
                "rules_created": len(new_rules),
                "categories_learned": list(category_patterns.keys()),
                "training_examples": len(training_data)
            }

        except Exception as e:
            logger.error(f"Error training classifier: {e}")
            return {"error": str(e)}

    def _extract_common_patterns(self, descriptions: List[str]) -> List[str]:
        """Extract common patterns from description text."""
        try:
            # Simple pattern extraction - find words that appear in multiple descriptions
            word_counts = {}

            for description in descriptions:
                words = re.findall(r'\b\w{3,}\b', description.lower())  # Words with 3+ chars
                for word in words:
                    if word not in word_counts:
                        word_counts[word] = 0
                    word_counts[word] += 1

            # Find words that appear in at least 30% of descriptions
            threshold = max(1, len(descriptions) * 0.3)
            common_words = [word for word, count in word_counts.items() if count >= threshold]

            # Convert to regex patterns
            patterns = [f"\\b{word}\\b" for word in common_words[:5]]  # Limit to top 5

            return patterns

        except Exception as e:
            logger.error(f"Error extracting patterns: {e}")
            return []

    def get_classification_report(
        self,
        transactions: List[Dict[str, Any]]
    ) -> Dict[str, Any]:
        """Generate comprehensive classification report."""
        try:
            # Classify all transactions
            batch_result = self.classify_transactions_batch(transactions)

            if "error" in batch_result:
                return batch_result

            classified_transactions = batch_result["classified_transactions"]
            stats = batch_result["classification_statistics"]

            # Generate detailed report
            report = {
                "summary": {
                    "total_transactions": len(transactions),
                    "classification_date": datetime.utcnow().isoformat(),
                    "reliability_rate": batch_result["quality_metrics"]["reliability_rate"],
                    "categorization_rate": batch_result["quality_metrics"]["categorization_rate"]
                },
                "confidence_distribution": {
                    "high": stats["high_confidence"],
                    "medium": stats["medium_confidence"],
                    "low": stats["low_confidence"],
                    "very_low": stats["very_low_confidence"]
                },
                "category_breakdown": stats["categories"],
                "recommendations": [],
                "low_confidence_transactions": []
            }

            # Find low confidence transactions for review
            for txn in classified_transactions:
                classification = txn.get("classification", {})
                if classification.get("confidence_level") in ["low", "very_low"]:
                    report["low_confidence_transactions"].append({
                        "id": txn.get("id"),
                        "description": txn.get("description"),
                        "amount": txn.get("amount"),
                        "category": classification.get("category"),
                        "confidence_score": classification.get("confidence_score")
                    })

            # Generate recommendations
            uncategorized_rate = (stats["uncategorized"] / len(transactions) * 100) if transactions else 0
            low_confidence_rate = ((stats["low_confidence"] + stats["very_low_confidence"]) /
                                 len(transactions) * 100) if transactions else 0

            if uncategorized_rate > 20:
                report["recommendations"].append(
                    "High uncategorized rate - consider adding custom classification rules"
                )

            if low_confidence_rate > 30:
                report["recommendations"].append(
                    "Many low-confidence classifications - review and train with examples"
                )

            if len(self.custom_rules) == 0:
                report["recommendations"].append(
                    "No custom rules defined - add business-specific classification rules"
                )

            return report

        except Exception as e:
            logger.error(f"Error generating classification report: {e}")
            return {"error": str(e)}

    def clear_cache(self):
        """Clear classification cache."""
        self._classification_cache.clear()
        self.classify_transaction.cache_clear()
    
    def _get_asset_liability_rules(self) -> List[ClassificationRule]:
        """Get asset/liability classification rules."""
        return [
            ClassificationRule(
                category=TransactionCategory.EQUIPMENT_PURCHASE,
                patterns=[
                    r"equipment|machinery|computer|vehicle|furniture",
                    r"capital.*purchase|asset.*purchase|depreciation"
                ],
                amount_range=(Decimal("500"), Decimal("*********")),
                confidence_score=0.85,
                description="Equipment and asset purchases"
            ),
            ClassificationRule(
                category=TransactionCategory.LOAN_PAYMENT,
                patterns=[
                    r"loan.*payment|mortgage|interest.*payment",
                    r"principal|debt.*service|financing"
                ],
                confidence_score=0.9,
                description="Loan and debt payments"
            ),
            ClassificationRule(
                category=TransactionCategory.INVESTMENT,
                patterns=[
                    r"investment|stock|bond|mutual.*fund|portfolio",
                    r"securities|trading|brokerage"
                ],
                confidence_score=0.85,
                description="Investments"
            ),
        ]
    
    def _get_cash_flow_rules(self) -> List[ClassificationRule]:
        """Get cash flow classification rules."""
        return [
            ClassificationRule(
                category=TransactionCategory.CUSTOMER_PAYMENT,
                patterns=[
                    r"customer.*payment|invoice.*payment|receivable",
                    r"payment.*received|collection|deposit.*from"
                ],
                amount_range=(Decimal("0"), Decimal("*********")),
                confidence_score=0.9,
                description="Customer payments"
            ),
            ClassificationRule(
                category=TransactionCategory.VENDOR_PAYMENT,
                patterns=[
                    r"vendor.*payment|supplier.*payment|payable",
                    r"payment.*to|bill.*payment|check.*to"
                ],
                amount_range=(Decimal("-*********"), Decimal("0")),
                confidence_score=0.9,
                description="Vendor payments"
            ),
            ClassificationRule(
                category=TransactionCategory.BANK_TRANSFER,
                patterns=[
                    r"transfer|wire|ach|electronic.*transfer",
                    r"bank.*transfer|internal.*transfer"
                ],
                confidence_score=0.8,
                description="Bank transfers"
            ),
        ]
