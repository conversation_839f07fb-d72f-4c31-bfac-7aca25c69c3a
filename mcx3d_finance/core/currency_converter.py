"""
Currency conversion module with real-time exchange rates and multi-currency support.
Provides GAAP-compliant currency conversion for financial data processing.
"""

from typing import Dict, Optional, Any, List
from decimal import Decimal, ROUND_HALF_UP
from datetime import datetime
import logging
from enum import Enum
from dataclasses import dataclass
import requests
from functools import lru_cache

logger = logging.getLogger(__name__)


class CurrencyCode(Enum):
    """Supported currency codes (ISO 4217)."""
    USD = "USD"  # US Dollar
    EUR = "EUR"  # Euro
    GBP = "GBP"  # British Pound
    CAD = "CAD"  # Canadian Dollar
    AUD = "AUD"  # Australian Dollar
    JPY = "JPY"  # Japanese Yen
    CHF = "CHF"  # Swiss Franc
    CNY = "CNY"  # Chinese Yuan
    INR = "INR"  # Indian Rupee
    BRL = "BRL"  # Brazilian Real
    MXN = "MXN"  # Mexican Peso
    SGD = "SGD"  # Singapore Dollar
    HKD = "HKD"  # Hong Kong Dollar
    NZD = "NZD"  # New Zealand Dollar
    SEK = "SEK"  # Swedish Krona
    NOK = "NOK"  # Norwegian Krone
    DKK = "DKK"  # Danish Krone
    PLN = "PLN"  # Polish Zloty
    CZK = "CZK"  # Czech Koruna
    HUF = "HUF"  # Hungarian Forint


@dataclass
class ExchangeRate:
    """Exchange rate data structure."""
    from_currency: str
    to_currency: str
    rate: Decimal
    timestamp: datetime
    source: str
    
    @property
    def is_stale(self, max_age_hours: int = 24) -> bool:
        """Check if exchange rate is stale."""
        return (datetime.utcnow() - self.timestamp).total_seconds() > (max_age_hours * 3600)


@dataclass
class CurrencyConversion:
    """Currency conversion result."""
    original_amount: Decimal
    converted_amount: Decimal
    from_currency: str
    to_currency: str
    exchange_rate: Decimal
    conversion_date: datetime
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for serialization."""
        return {
            "original_amount": float(self.original_amount),
            "converted_amount": float(self.converted_amount),
            "from_currency": self.from_currency,
            "to_currency": self.to_currency,
            "exchange_rate": float(self.exchange_rate),
            "conversion_date": self.conversion_date.isoformat()
        }


class CurrencyConverter:
    """Multi-currency converter with real-time exchange rates."""
    
    def __init__(self, base_currency: str = "USD", api_key: Optional[str] = None):
        self.base_currency = base_currency
        self.api_key = api_key
        self.precision = Decimal("0.0001")  # 4 decimal places for exchange rates
        self.amount_precision = Decimal("0.01")  # 2 decimal places for amounts
        self._rate_cache: Dict[str, ExchangeRate] = {}
        self._fallback_rates = self._load_fallback_rates()
    
    def _load_fallback_rates(self) -> Dict[str, Decimal]:
        """Load fallback exchange rates for when API is unavailable."""
        # These are approximate rates - in production, these would be loaded from a config file
        return {
            "EUR": Decimal("0.85"),
            "GBP": Decimal("0.75"),
            "CAD": Decimal("1.35"),
            "AUD": Decimal("1.50"),
            "JPY": Decimal("110.00"),
            "CHF": Decimal("0.92"),
            "CNY": Decimal("7.20"),
            "INR": Decimal("83.00"),
            "BRL": Decimal("5.20"),
            "MXN": Decimal("17.50"),
            "SGD": Decimal("1.35"),
            "HKD": Decimal("7.80"),
            "NZD": Decimal("1.65"),
            "SEK": Decimal("10.50"),
            "NOK": Decimal("10.80"),
            "DKK": Decimal("6.35"),
            "PLN": Decimal("4.20"),
            "CZK": Decimal("23.50"),
            "HUF": Decimal("360.00")
        }
    
    def _round_rate(self, rate: Decimal) -> Decimal:
        """Round exchange rate to appropriate precision."""
        return rate.quantize(self.precision, rounding=ROUND_HALF_UP)
    
    def _round_amount(self, amount: Decimal) -> Decimal:
        """Round currency amount to appropriate precision."""
        return amount.quantize(self.amount_precision, rounding=ROUND_HALF_UP)
    
    @lru_cache(maxsize=100)
    def _fetch_exchange_rate_api(self, from_currency: str, to_currency: str) -> Optional[ExchangeRate]:
        """Fetch exchange rate from external API (cached)."""
        try:
            # Example using a free API (in production, use a reliable paid service)
            if self.api_key:
                # Use paid API with API key
                url = f"https://api.exchangerate-api.com/v4/latest/{from_currency}"
                headers = {"Authorization": f"Bearer {self.api_key}"}
            else:
                # Use free API (limited requests)
                url = f"https://api.exchangerate-api.com/v4/latest/{from_currency}"
                headers = {}
            
            response = requests.get(url, headers=headers, timeout=10)
            response.raise_for_status()
            
            data = response.json()
            rates = data.get("rates", {})
            
            if to_currency in rates:
                rate = self._round_rate(Decimal(str(rates[to_currency])))
                return ExchangeRate(
                    from_currency=from_currency,
                    to_currency=to_currency,
                    rate=rate,
                    timestamp=datetime.utcnow(),
                    source="api"
                )
            
        except Exception as e:
            logger.warning(f"Failed to fetch exchange rate from API: {e}")
        
        return None
    
    def _get_fallback_rate(self, from_currency: str, to_currency: str) -> Optional[ExchangeRate]:
        """Get fallback exchange rate from static data."""
        try:
            if from_currency == self.base_currency and to_currency in self._fallback_rates:
                rate = self._fallback_rates[to_currency]
            elif to_currency == self.base_currency and from_currency in self._fallback_rates:
                rate = Decimal("1") / self._fallback_rates[from_currency]
            elif from_currency in self._fallback_rates and to_currency in self._fallback_rates:
                # Cross-currency conversion through base currency
                from_to_base = Decimal("1") / self._fallback_rates[from_currency]
                base_to_target = self._fallback_rates[to_currency]
                rate = from_to_base * base_to_target
            else:
                return None
            
            return ExchangeRate(
                from_currency=from_currency,
                to_currency=to_currency,
                rate=self._round_rate(rate),
                timestamp=datetime.utcnow(),
                source="fallback"
            )
            
        except Exception as e:
            logger.error(f"Error calculating fallback rate: {e}")
            return None
    
    def get_exchange_rate(self, from_currency: str, to_currency: str, force_refresh: bool = False) -> Optional[ExchangeRate]:
        """Get exchange rate between two currencies."""
        try:
            # Same currency
            if from_currency == to_currency:
                return ExchangeRate(
                    from_currency=from_currency,
                    to_currency=to_currency,
                    rate=Decimal("1"),
                    timestamp=datetime.utcnow(),
                    source="identity"
                )
            
            # Check cache first
            cache_key = f"{from_currency}_{to_currency}"
            if not force_refresh and cache_key in self._rate_cache:
                cached_rate = self._rate_cache[cache_key]
                if not cached_rate.is_stale:
                    return cached_rate
            
            # Try to fetch from API
            rate = self._fetch_exchange_rate_api(from_currency, to_currency)
            if rate:
                self._rate_cache[cache_key] = rate
                return rate
            
            # Fall back to static rates
            rate = self._get_fallback_rate(from_currency, to_currency)
            if rate:
                self._rate_cache[cache_key] = rate
                logger.warning(f"Using fallback exchange rate for {from_currency} to {to_currency}")
                return rate
            
            logger.error(f"Could not obtain exchange rate for {from_currency} to {to_currency}")
            return None
            
        except Exception as e:
            logger.error(f"Error getting exchange rate: {e}")
            return None
    
    def convert_amount(
        self,
        amount: Decimal,
        from_currency: str,
        to_currency: str,
        conversion_date: Optional[datetime] = None
    ) -> Optional[CurrencyConversion]:
        """Convert amount from one currency to another."""
        try:
            if conversion_date is None:
                conversion_date = datetime.utcnow()
            
            # Get exchange rate
            exchange_rate = self.get_exchange_rate(from_currency, to_currency)
            if not exchange_rate:
                return None
            
            # Perform conversion
            converted_amount = self._round_amount(amount * exchange_rate.rate)
            
            return CurrencyConversion(
                original_amount=amount,
                converted_amount=converted_amount,
                from_currency=from_currency,
                to_currency=to_currency,
                exchange_rate=exchange_rate.rate,
                conversion_date=conversion_date
            )
            
        except Exception as e:
            logger.error(f"Error converting currency: {e}")
            return None
    
    def convert_financial_data(
        self,
        financial_data: Dict[str, Any],
        target_currency: str,
        source_currency: Optional[str] = None
    ) -> Dict[str, Any]:
        """Convert all monetary values in financial data to target currency."""
        try:
            if source_currency is None:
                source_currency = financial_data.get("currency", self.base_currency)
            
            if source_currency == target_currency:
                return financial_data
            
            # Get exchange rate
            exchange_rate = self.get_exchange_rate(source_currency, target_currency)
            if not exchange_rate:
                logger.error("Cannot convert financial data: no exchange rate available")
                return financial_data
            
            # Create converted copy
            converted_data = self._deep_copy_and_convert(financial_data, exchange_rate.rate)
            converted_data["currency"] = target_currency
            converted_data["conversion_info"] = {
                "original_currency": source_currency,
                "exchange_rate": float(exchange_rate.rate),
                "conversion_date": datetime.utcnow().isoformat(),
                "source": exchange_rate.source
            }
            
            return converted_data
            
        except Exception as e:
            logger.error(f"Error converting financial data: {e}")
            return financial_data
    
    def _deep_copy_and_convert(self, data: Any, exchange_rate: Decimal) -> Any:
        """Recursively convert monetary values in nested data structure."""
        if isinstance(data, dict):
            converted = {}
            for key, value in data.items():
                if self._is_monetary_field(key) and isinstance(value, (int, float, Decimal)):
                    converted[key] = float(self._round_amount(Decimal(str(value)) * exchange_rate))
                else:
                    converted[key] = self._deep_copy_and_convert(value, exchange_rate)
            return converted
        elif isinstance(data, list):
            return [self._deep_copy_and_convert(item, exchange_rate) for item in data]
        else:
            return data
    
    def _is_monetary_field(self, field_name: str) -> bool:
        """Check if field name represents a monetary value."""
        monetary_keywords = [
            "amount", "total", "balance", "value", "price", "cost", "revenue", "expense",
            "income", "profit", "loss", "cash", "debt", "equity", "asset", "liability",
            "receivable", "payable", "salary", "wage", "fee", "tax", "interest",
            "dividend", "investment", "capital", "fund", "budget", "payment"
        ]
        
        field_lower = field_name.lower()
        return any(keyword in field_lower for keyword in monetary_keywords)
    
    def get_supported_currencies(self) -> List[str]:
        """Get list of supported currency codes."""
        return [currency.value for currency in CurrencyCode]
    
    def clear_cache(self):
        """Clear the exchange rate cache."""
        self._rate_cache.clear()
        # Clear the LRU cache for API calls
        self._fetch_exchange_rate_api.cache_clear()
    
    def get_cache_stats(self) -> Dict[str, Any]:
        """Get cache statistics."""
        return {
            "cached_rates": len(self._rate_cache),
            "api_cache_info": self._fetch_exchange_rate_api.cache_info()._asdict(),
            "stale_rates": sum(1 for rate in self._rate_cache.values() if rate.is_stale)
        }
