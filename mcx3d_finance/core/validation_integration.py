"""
Enhanced validation integration system that connects data validation to all processing workflows,
provides real-time validation during ingestion, and implements validation-based routing.
"""

from typing import Dict, List, Any, Optional
from dataclasses import dataclass, field
from enum import Enum
from datetime import datetime, timedelta
import logging
import asyncio
from concurrent.futures import ThreadPoolExecutor
import json
import time

from mcx3d_finance.core.data_validation import (
    DataValidationEngine, ValidationResult, ValidationReport, 
    ValidationSeverity, ValidationCategory
)

logger = logging.getLogger(__name__)


class ValidationTrigger(Enum):
    """When validation should be triggered."""
    ON_INGESTION = "on_ingestion"
    ON_TRANSFORMATION = "on_transformation"
    ON_SYNC = "on_sync"
    ON_SCHEDULE = "on_schedule"
    ON_DEMAND = "on_demand"
    REAL_TIME = "real_time"


class ValidationAction(Enum):
    """Actions to take based on validation results."""
    ACCEPT = "accept"           # Data passes validation
    REJECT = "reject"           # Data fails critical validation
    QUARANTINE = "quarantine"   # Data has warnings, needs review
    RETRY = "retry"             # Temporary failure, retry later
    TRANSFORM = "transform"     # Apply transformations and re-validate


class DataRoute(Enum):
    """Data routing destinations based on validation."""
    VALID_QUEUE = "valid_queue"
    INVALID_QUEUE = "invalid_queue"
    REVIEW_QUEUE = "review_queue"
    RETRY_QUEUE = "retry_queue"
    TRANSFORM_QUEUE = "transform_queue"


@dataclass
class ValidationPolicy:
    """Defines validation policies for different data types and scenarios."""
    policy_id: str
    name: str
    description: str
    data_types: List[str]  # ['transaction', 'contact', 'account']
    triggers: List[ValidationTrigger]
    severity_thresholds: Dict[ValidationSeverity, ValidationAction]
    routing_rules: Dict[ValidationAction, DataRoute]
    max_retries: int = 3
    retry_delay_seconds: int = 60
    enabled: bool = True
    created_at: datetime = field(default_factory=datetime.utcnow)


@dataclass
class ValidationContext:
    """Context information for validation operations."""
    organization_id: str
    data_type: str
    trigger: ValidationTrigger
    batch_id: Optional[str] = None
    source_system: Optional[str] = None
    user_id: Optional[str] = None
    metadata: Dict[str, Any] = field(default_factory=dict)


@dataclass
class ValidationRoutingResult:
    """Result of validation-based routing."""
    validation_report: ValidationReport
    action: ValidationAction
    route: DataRoute
    policy_applied: str
    processing_time: float
    retry_count: int = 0
    next_retry_at: Optional[datetime] = None
    metadata: Dict[str, Any] = field(default_factory=dict)


class RealTimeValidator:
    """Real-time validation system for streaming data."""
    
    def __init__(self, validation_engine: DataValidationEngine):
        self.validation_engine = validation_engine
        self.active_validations: Dict[str, Any] = {}
        self.validation_cache: Dict[str, Any] = {}
        self.cache_ttl_seconds = 300  # 5 minutes
    
    async def validate_stream_record(
        self,
        record: Dict[str, Any],
        context: ValidationContext
    ) -> ValidationResult:
        """Validate a single record in real-time."""
        try:
            # Check cache first for similar records
            cache_key = self._generate_cache_key(record, context)
            cached_result = self._get_cached_validation(cache_key)
            
            if cached_result:
                logger.debug(f"Using cached validation result for {context.data_type}")
                return cached_result
            
            # Perform lightweight validation for real-time processing
            validation_data = {context.data_type + 's': [record]}
            
            # Run validation with timeout
            validation_report = await asyncio.wait_for(
                self._async_validate(validation_data, context.organization_id),
                timeout=5.0  # 5 second timeout for real-time
            )
            
            # Extract result for this record
            if validation_report.results:
                result = validation_report.results[0]
                self._cache_validation_result(cache_key, result)
                return result
            
            # Default to passed if no specific issues found
            return ValidationResult(
                check_name="real_time_validation",
                category=ValidationCategory.BUSINESS_RULES,
                severity=ValidationSeverity.INFO,
                passed=True,
                message="Real-time validation passed",
                timestamp=datetime.utcnow()
            )
            
        except asyncio.TimeoutError:
            logger.warning(f"Real-time validation timeout for {context.data_type}")
            return ValidationResult(
                check_name="real_time_timeout",
                category=ValidationCategory.DATA_FRESHNESS,
                severity=ValidationSeverity.WARNING,
                passed=False,
                message="Real-time validation timed out",
                timestamp=datetime.utcnow()
            )
        except Exception as e:
            logger.error(f"Error in real-time validation: {e}")
            return ValidationResult(
                check_name="real_time_error",
                category=ValidationCategory.BUSINESS_RULES,
                severity=ValidationSeverity.ERROR,
                passed=False,
                message=f"Real-time validation error: {str(e)}",
                timestamp=datetime.utcnow()
            )
    
    async def _async_validate(
        self,
        data: Dict[str, Any],
        organization_id: str
    ) -> ValidationReport:
        """Async wrapper for validation engine."""
        loop = asyncio.get_event_loop()
        with ThreadPoolExecutor(max_workers=1) as executor:
            return await loop.run_in_executor(
                executor,
                lambda: self.validation_engine.validate_data(
                    int(organization_id), data
                )
            )
    
    def _generate_cache_key(
        self,
        record: Dict[str, Any],
        context: ValidationContext
    ) -> str:
        """Generate cache key for validation result."""
        # Create a hash based on record structure and key fields
        key_fields = ['amount', 'date', 'type', 'account_id', 'email', 'phone']
        key_data = {
            'data_type': context.data_type,
            'fields': {k: v for k, v in record.items() if k in key_fields}
        }
        return str(hash(json.dumps(key_data, sort_keys=True, default=str)))
    
    def _get_cached_validation(self, cache_key: str) -> Optional[ValidationResult]:
        """Get cached validation result if still valid."""
        if cache_key in self.validation_cache:
            cached_data = self.validation_cache[cache_key]
            if datetime.utcnow() - cached_data['timestamp'] < timedelta(seconds=self.cache_ttl_seconds):
                return cached_data['result']
            else:
                # Remove expired cache entry
                del self.validation_cache[cache_key]
        return None
    
    def _cache_validation_result(self, cache_key: str, result: ValidationResult) -> None:
        """Cache validation result."""
        self.validation_cache[cache_key] = {
            'result': result,
            'timestamp': datetime.utcnow()
        }
        
        # Clean up old cache entries periodically
        if len(self.validation_cache) > 1000:
            self._cleanup_cache()
    
    def _cleanup_cache(self) -> None:
        """Clean up expired cache entries."""
        current_time = datetime.utcnow()
        expired_keys = [
            key for key, data in self.validation_cache.items()
            if current_time - data['timestamp'] > timedelta(seconds=self.cache_ttl_seconds)
        ]
        for key in expired_keys:
            del self.validation_cache[key]


class ValidationRouter:
    """Routes data based on validation results according to policies."""
    
    def __init__(self):
        self.policies: Dict[str, ValidationPolicy] = {}
        self.routing_stats = {
            'total_routed': 0,
            'routes': {route.value: 0 for route in DataRoute}
        }
    
    def add_policy(self, policy: ValidationPolicy) -> None:
        """Add a validation policy."""
        self.policies[policy.policy_id] = policy
        logger.info(f"Added validation policy: {policy.name}")
    
    def remove_policy(self, policy_id: str) -> bool:
        """Remove a validation policy."""
        if policy_id in self.policies:
            del self.policies[policy_id]
            logger.info(f"Removed validation policy: {policy_id}")
            return True
        return False
    
    def route_data(
        self,
        validation_report: ValidationReport,
        context: ValidationContext
    ) -> ValidationRoutingResult:
        """Route data based on validation results and policies."""
        start_time = time.time()
        
        try:
            # Find applicable policy
            policy = self._find_applicable_policy(context)
            if not policy:
                # Default policy: accept if no critical errors
                action = self._determine_default_action(validation_report)
                route = self._get_default_route(action)
            else:
                # Apply policy rules
                action = self._determine_action_by_policy(validation_report, policy)
                route = policy.routing_rules.get(action, DataRoute.REVIEW_QUEUE)
            
            # Update routing statistics
            self.routing_stats['total_routed'] += 1
            self.routing_stats['routes'][route.value] += 1
            
            processing_time = time.time() - start_time
            
            result = ValidationRoutingResult(
                validation_report=validation_report,
                action=action,
                route=route,
                policy_applied=policy.policy_id if policy else "default",
                processing_time=processing_time
            )
            
            logger.info(f"Routed {context.data_type} data to {route.value} "
                       f"(action: {action.value}, policy: {result.policy_applied})")
            
            return result
            
        except Exception as e:
            logger.error(f"Error in validation routing: {e}")
            # Default to review queue on error
            return ValidationRoutingResult(
                validation_report=validation_report,
                action=ValidationAction.QUARANTINE,
                route=DataRoute.REVIEW_QUEUE,
                policy_applied="error_fallback",
                processing_time=time.time() - start_time,
                metadata={'error': str(e)}
            )
    
    def _find_applicable_policy(self, context: ValidationContext) -> Optional[ValidationPolicy]:
        """Find the most applicable policy for the given context."""
        applicable_policies = []
        
        for policy in self.policies.values():
            if not policy.enabled:
                continue
            
            # Check if data type matches
            if context.data_type not in policy.data_types:
                continue
            
            # Check if trigger matches
            if context.trigger not in policy.triggers:
                continue
            
            applicable_policies.append(policy)
        
        # Return the first matching policy (could be enhanced with priority)
        return applicable_policies[0] if applicable_policies else None
    
    def _determine_action_by_policy(
        self,
        validation_report: ValidationReport,
        policy: ValidationPolicy
    ) -> ValidationAction:
        """Determine action based on policy rules."""
        # Find the highest severity level in the validation results
        max_severity = ValidationSeverity.INFO
        
        for result in validation_report.results:
            if not result.passed and result.severity.value > max_severity.value:
                max_severity = result.severity
        
        # Apply policy threshold
        return policy.severity_thresholds.get(max_severity, ValidationAction.QUARANTINE)
    
    def _determine_default_action(self, validation_report: ValidationReport) -> ValidationAction:
        """Determine default action when no policy applies."""
        critical_errors = sum(
            1 for r in validation_report.results 
            if not r.passed and r.severity == ValidationSeverity.CRITICAL
        )
        
        errors = sum(
            1 for r in validation_report.results 
            if not r.passed and r.severity == ValidationSeverity.ERROR
        )
        
        if critical_errors > 0:
            return ValidationAction.REJECT
        elif errors > 0:
            return ValidationAction.QUARANTINE
        else:
            return ValidationAction.ACCEPT
    
    def _get_default_route(self, action: ValidationAction) -> DataRoute:
        """Get default route for an action."""
        default_routes = {
            ValidationAction.ACCEPT: DataRoute.VALID_QUEUE,
            ValidationAction.REJECT: DataRoute.INVALID_QUEUE,
            ValidationAction.QUARANTINE: DataRoute.REVIEW_QUEUE,
            ValidationAction.RETRY: DataRoute.RETRY_QUEUE,
            ValidationAction.TRANSFORM: DataRoute.TRANSFORM_QUEUE
        }
        return default_routes.get(action, DataRoute.REVIEW_QUEUE)
    
    def get_routing_statistics(self) -> Dict[str, Any]:
        """Get routing statistics."""
        return {
            'total_routed': self.routing_stats['total_routed'],
            'route_distribution': self.routing_stats['routes'].copy(),
            'active_policies': len([p for p in self.policies.values() if p.enabled]),
            'total_policies': len(self.policies)
        }


class IntegratedValidationEngine:
    """Main engine that integrates validation into all processing workflows."""

    def __init__(self, validation_engine: DataValidationEngine):
        self.validation_engine = validation_engine
        self.real_time_validator = RealTimeValidator(validation_engine)
        self.router = ValidationRouter()
        self.processing_stats = {
            'total_processed': 0,
            'validation_triggered': 0,
            'routing_performed': 0,
            'real_time_validations': 0
        }

        # Setup default policies
        self._setup_default_policies()

    def _setup_default_policies(self) -> None:
        """Setup default validation policies."""
        try:
            # Financial data policy - strict validation
            financial_policy = ValidationPolicy(
                policy_id="financial_strict",
                name="Financial Data Strict Validation",
                description="Strict validation for financial transactions and accounts",
                data_types=["transaction", "account"],
                triggers=[ValidationTrigger.ON_INGESTION, ValidationTrigger.ON_SYNC],
                severity_thresholds={
                    ValidationSeverity.CRITICAL: ValidationAction.REJECT,
                    ValidationSeverity.ERROR: ValidationAction.QUARANTINE,
                    ValidationSeverity.WARNING: ValidationAction.ACCEPT,
                    ValidationSeverity.INFO: ValidationAction.ACCEPT
                },
                routing_rules={
                    ValidationAction.ACCEPT: DataRoute.VALID_QUEUE,
                    ValidationAction.REJECT: DataRoute.INVALID_QUEUE,
                    ValidationAction.QUARANTINE: DataRoute.REVIEW_QUEUE,
                    ValidationAction.RETRY: DataRoute.RETRY_QUEUE,
                    ValidationAction.TRANSFORM: DataRoute.TRANSFORM_QUEUE
                },
                max_retries=3,
                retry_delay_seconds=300  # 5 minutes
            )
            self.router.add_policy(financial_policy)

            # Contact data policy - more lenient
            contact_policy = ValidationPolicy(
                policy_id="contact_lenient",
                name="Contact Data Lenient Validation",
                description="Lenient validation for contact information",
                data_types=["contact"],
                triggers=[ValidationTrigger.ON_INGESTION, ValidationTrigger.REAL_TIME],
                severity_thresholds={
                    ValidationSeverity.CRITICAL: ValidationAction.QUARANTINE,
                    ValidationSeverity.ERROR: ValidationAction.ACCEPT,
                    ValidationSeverity.WARNING: ValidationAction.ACCEPT,
                    ValidationSeverity.INFO: ValidationAction.ACCEPT
                },
                routing_rules={
                    ValidationAction.ACCEPT: DataRoute.VALID_QUEUE,
                    ValidationAction.REJECT: DataRoute.INVALID_QUEUE,
                    ValidationAction.QUARANTINE: DataRoute.REVIEW_QUEUE,
                    ValidationAction.RETRY: DataRoute.RETRY_QUEUE,
                    ValidationAction.TRANSFORM: DataRoute.TRANSFORM_QUEUE
                },
                max_retries=1,
                retry_delay_seconds=60
            )
            self.router.add_policy(contact_policy)

            logger.info("Default validation policies initialized successfully")

        except Exception as e:
            logger.error(f"Error setting up default validation policies: {e}")

    async def validate_and_route_data(
        self,
        data: Dict[str, Any],
        context: ValidationContext,
        enable_routing: bool = True
    ) -> ValidationRoutingResult:
        """Validate data and route based on results."""
        try:
            self.processing_stats['total_processed'] += 1

            # Perform validation based on trigger type
            if context.trigger == ValidationTrigger.REAL_TIME:
                # Real-time validation for streaming data
                validation_results = []
                for data_type, records in data.items():
                    if isinstance(records, list):
                        for record in records:
                            result = await self.real_time_validator.validate_stream_record(
                                record,
                                ValidationContext(
                                    organization_id=context.organization_id,
                                    data_type=data_type.rstrip('s'),  # Remove plural
                                    trigger=context.trigger,
                                    batch_id=context.batch_id,
                                    source_system=context.source_system,
                                    user_id=context.user_id,
                                    metadata=context.metadata
                                )
                            )
                            validation_results.append(result)

                # Create validation report from real-time results
                validation_report = ValidationReport(
                    organization_id=int(context.organization_id),
                    validation_timestamp=datetime.utcnow(),
                    total_checks=len(validation_results),
                    passed_checks=sum(1 for r in validation_results if r.passed),
                    failed_checks=sum(1 for r in validation_results if not r.passed),
                    results=validation_results,
                    summary={}
                )

                self.processing_stats['real_time_validations'] += len(validation_results)

            else:
                # Standard batch validation
                validation_report = self.validation_engine.validate_data(
                    int(context.organization_id), data
                )

            self.processing_stats['validation_triggered'] += 1

            # Route data based on validation results
            if enable_routing:
                routing_result = self.router.route_data(validation_report, context)
                self.processing_stats['routing_performed'] += 1
                return routing_result
            else:
                # Return result without routing
                return ValidationRoutingResult(
                    validation_report=validation_report,
                    action=ValidationAction.ACCEPT,
                    route=DataRoute.VALID_QUEUE,
                    policy_applied="no_routing",
                    processing_time=0.0
                )

        except Exception as e:
            logger.error(f"Error in integrated validation: {e}")
            return self._create_error_routing_result(context, str(e))

    def _create_error_routing_result(
        self,
        context: ValidationContext,
        error_message: str
    ) -> ValidationRoutingResult:
        """Create error routing result."""
        error_result = ValidationResult(
            check_name="validation_error",
            category=ValidationCategory.BUSINESS_RULES,
            severity=ValidationSeverity.CRITICAL,
            passed=False,
            message=error_message,
            timestamp=datetime.utcnow()
        )

        error_report = ValidationReport(
            organization_id=int(context.organization_id),
            validation_timestamp=datetime.utcnow(),
            total_checks=1,
            passed_checks=0,
            failed_checks=1,
            results=[error_result],
            summary={}
        )

        return ValidationRoutingResult(
            validation_report=error_report,
            action=ValidationAction.REJECT,
            route=DataRoute.INVALID_QUEUE,
            policy_applied="error_fallback",
            processing_time=0.0,
            metadata={'error': error_message}
        )

    def validate_during_transformation(
        self,
        original_data: Dict[str, Any],
        transformed_data: Dict[str, Any],
        context: ValidationContext
    ) -> ValidationRoutingResult:
        """Validate data during transformation process."""
        try:
            logger.info("Performing validation during transformation")

            # Validate transformed data
            validation_report = self.validation_engine.validate_data(
                int(context.organization_id), transformed_data
            )

            # Route based on validation results
            routing_result = self.router.route_data(validation_report, context)

            logger.info(f"Transformation validation completed: {routing_result.action.value}")
            return routing_result

        except Exception as e:
            logger.error(f"Error in transformation validation: {e}")
            return self._create_error_routing_result(context, str(e))

    def validate_during_sync(
        self,
        sync_data: Dict[str, Any],
        context: ValidationContext
    ) -> ValidationRoutingResult:
        """Validate data during sync operations."""
        try:
            logger.info("Performing validation during sync")

            # Validate sync data
            validation_report = self.validation_engine.validate_data(
                int(context.organization_id), sync_data
            )

            # Route based on validation results
            routing_result = self.router.route_data(validation_report, context)

            logger.info(f"Sync validation completed: {routing_result.action.value}")
            return routing_result

        except Exception as e:
            logger.error(f"Error in sync validation: {e}")
            return self._create_error_routing_result(context, str(e))

    def get_processing_statistics(self) -> Dict[str, Any]:
        """Get comprehensive processing statistics."""
        routing_stats = self.router.get_routing_statistics()

        return {
            'processing_stats': self.processing_stats.copy(),
            'routing_stats': routing_stats,
            'cache_stats': {
                'cached_validations': len(self.real_time_validator.validation_cache),
                'cache_hit_rate': self._calculate_cache_hit_rate()
            },
            'policy_stats': {
                'active_policies': len([p for p in self.router.policies.values() if p.enabled]),
                'total_policies': len(self.router.policies)
            }
        }

    def _calculate_cache_hit_rate(self) -> float:
        """Calculate cache hit rate for real-time validations."""
        total_validations = self.processing_stats['real_time_validations']
        if total_validations == 0:
            return 0.0

        # This is a simplified calculation - in a real implementation,
        # you'd track cache hits vs misses
        cached_validations = len(self.real_time_validator.validation_cache)
        return min(cached_validations / total_validations, 1.0) if total_validations > 0 else 0.0
