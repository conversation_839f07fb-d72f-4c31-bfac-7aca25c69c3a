"""
GAAP account classifications and financial statement categorization.
Separated from data_processors to avoid circular imports.
"""

from enum import Enum
from typing import Dict, Optional
import logging

logger = logging.getLogger(__name__)


class GAAPAccountClassification(Enum):
    """GAAP-compliant account classifications for financial reporting."""
    
    # Assets
    CURRENT_ASSETS = "current_assets"
    CASH_AND_EQUIVALENTS = "cash_and_equivalents"
    ACCOUNTS_RECEIVABLE = "accounts_receivable"
    INVENTORY = "inventory"
    PREPAID_EXPENSES = "prepaid_expenses"
    OTHER_CURRENT_ASSETS = "other_current_assets"
    
    NON_CURRENT_ASSETS = "non_current_assets"
    PROPERTY_PLANT_EQUIPMENT = "property_plant_equipment"
    INTANGIBLE_ASSETS = "intangible_assets"
    GOODWILL = "goodwill"
    INVESTMENTS = "investments"
    OTHER_NON_CURRENT_ASSETS = "other_non_current_assets"
    
    # Liabilities
    CURRENT_LIABILITIES = "current_liabilities"
    ACCOUNTS_PAYABLE = "accounts_payable"
    ACCRUED_EXPENSES = "accrued_expenses"
    ACCRUED_LIABILITIES = "accrued_liabilities"
    SHORT_TERM_DEBT = "short_term_debt"
    CURRENT_PORTION_LONG_TERM_DEBT = "current_portion_long_term_debt"
    OTHER_CURRENT_LIABILITIES = "other_current_liabilities"
    
    NON_CURRENT_LIABILITIES = "non_current_liabilities"
    LONG_TERM_DEBT = "long_term_debt"
    DEFERRED_TAX_LIABILITIES = "deferred_tax_liabilities"
    OTHER_NON_CURRENT_LIABILITIES = "other_non_current_liabilities"
    
    # Equity
    SHAREHOLDERS_EQUITY = "shareholders_equity"
    COMMON_STOCK = "common_stock"
    RETAINED_EARNINGS = "retained_earnings"
    ADDITIONAL_PAID_IN_CAPITAL = "additional_paid_in_capital"
    TREASURY_STOCK = "treasury_stock"
    OTHER_COMPREHENSIVE_INCOME = "other_comprehensive_income"
    ACCUMULATED_OTHER_COMPREHENSIVE_INCOME = "accumulated_other_comprehensive_income"
    
    # Revenue
    OPERATING_REVENUE = "operating_revenue"
    PRODUCT_REVENUE = "product_revenue"
    SERVICE_REVENUE = "service_revenue"
    SUBSCRIPTION_REVENUE = "subscription_revenue"
    OTHER_OPERATING_REVENUE = "other_operating_revenue"
    OTHER_REVENUE = "other_revenue"
    
    NON_OPERATING_REVENUE = "non_operating_revenue"
    INTEREST_INCOME = "interest_income"
    INVESTMENT_INCOME = "investment_income"
    GAIN_ON_SALE = "gain_on_sale"
    OTHER_NON_OPERATING_REVENUE = "other_non_operating_revenue"
    
    # Expenses
    COST_OF_GOODS_SOLD = "cost_of_goods_sold"
    COST_OF_SERVICES = "cost_of_services"
    DIRECT_MATERIALS = "direct_materials"
    DIRECT_LABOR = "direct_labor"
    MANUFACTURING_OVERHEAD = "manufacturing_overhead"
    
    OPERATING_EXPENSES = "operating_expenses"
    SELLING_EXPENSES = "selling_expenses"
    SALES_AND_MARKETING = "sales_and_marketing"
    GENERAL_ADMINISTRATIVE = "general_administrative"
    GENERAL_AND_ADMINISTRATIVE = "general_and_administrative"
    RESEARCH_DEVELOPMENT = "research_development"
    RESEARCH_AND_DEVELOPMENT = "research_and_development"
    DEPRECIATION_AMORTIZATION = "depreciation_amortization"
    DEPRECIATION_AND_AMORTIZATION = "depreciation_and_amortization"
    PAYROLL_EXPENSES = "payroll_expenses"
    RENT_AND_UTILITIES = "rent_and_utilities"
    PROFESSIONAL_SERVICES = "professional_services"
    TRAVEL_ENTERTAINMENT = "travel_entertainment"
    OFFICE_SUPPLIES = "office_supplies"
    INSURANCE_EXPENSE = "insurance_expense"
    TAXES_AND_FEES = "taxes_and_fees"
    
    NON_OPERATING_EXPENSES = "non_operating_expenses"
    INTEREST_EXPENSE = "interest_expense"
    LOSS_ON_SALE = "loss_on_sale"
    OTHER_NON_OPERATING_EXPENSES = "other_non_operating_expenses"
    OTHER_EXPENSE = "other_expense"
    
    # Special Categories
    EXTRAORDINARY_ITEMS = "extraordinary_items"
    DISCONTINUED_OPERATIONS = "discontinued_operations"
    TAX_EXPENSE = "tax_expense"


class FinancialStatementCategory(Enum):
    """Financial statement categories for reporting."""
    BALANCE_SHEET = "balance_sheet"
    INCOME_STATEMENT = "income_statement"
    CASH_FLOW_STATEMENT = "cash_flow_statement"
    STATEMENT_OF_EQUITY = "statement_of_equity"


class AccountTypeMapping:
    """Maps Xero account types to GAAP classifications."""
    
    XERO_TO_GAAP_MAPPING = {
        # Assets
        "BANK": GAAPAccountClassification.CASH_AND_EQUIVALENTS,
        "CURRENT": GAAPAccountClassification.ACCOUNTS_RECEIVABLE,
        "CURRLIAB": GAAPAccountClassification.ACCOUNTS_PAYABLE,
        "DEPRECIATN": GAAPAccountClassification.DEPRECIATION_AMORTIZATION,
        "DIRECTCOSTS": GAAPAccountClassification.COST_OF_GOODS_SOLD,
        "EQUITY": GAAPAccountClassification.SHAREHOLDERS_EQUITY,
        "EXPENSE": GAAPAccountClassification.OPERATING_EXPENSES,
        "FIXED": GAAPAccountClassification.PROPERTY_PLANT_EQUIPMENT,
        "INVENTORY": GAAPAccountClassification.INVENTORY,
        "LIABILITY": GAAPAccountClassification.NON_CURRENT_LIABILITIES,
        "NONCURRENT": GAAPAccountClassification.NON_CURRENT_ASSETS,
        "OTHERINCOME": GAAPAccountClassification.NON_OPERATING_REVENUE,
        "OVERHEADS": GAAPAccountClassification.OPERATING_EXPENSES,
        "PREPAYMENT": GAAPAccountClassification.PREPAID_EXPENSES,
        "REVENUE": GAAPAccountClassification.OPERATING_REVENUE,
        "SALES": GAAPAccountClassification.OPERATING_REVENUE,
        "TERMLIAB": GAAPAccountClassification.LONG_TERM_DEBT,
    }
    
    @classmethod
    def get_gaap_classification(cls, xero_account_type: str) -> Optional[GAAPAccountClassification]:
        """Get GAAP classification for a Xero account type."""
        return cls.XERO_TO_GAAP_MAPPING.get(xero_account_type.upper())
    
    @classmethod
    def get_financial_statement_category(cls, gaap_classification: GAAPAccountClassification) -> FinancialStatementCategory:
        """Get financial statement category for a GAAP classification."""
        asset_classifications = {
            GAAPAccountClassification.CURRENT_ASSETS,
            GAAPAccountClassification.CASH_AND_EQUIVALENTS,
            GAAPAccountClassification.ACCOUNTS_RECEIVABLE,
            GAAPAccountClassification.INVENTORY,
            GAAPAccountClassification.PREPAID_EXPENSES,
            GAAPAccountClassification.OTHER_CURRENT_ASSETS,
            GAAPAccountClassification.NON_CURRENT_ASSETS,
            GAAPAccountClassification.PROPERTY_PLANT_EQUIPMENT,
            GAAPAccountClassification.INTANGIBLE_ASSETS,
            GAAPAccountClassification.INVESTMENTS,
            GAAPAccountClassification.OTHER_NON_CURRENT_ASSETS,
        }
        
        liability_classifications = {
            GAAPAccountClassification.CURRENT_LIABILITIES,
            GAAPAccountClassification.ACCOUNTS_PAYABLE,
            GAAPAccountClassification.ACCRUED_EXPENSES,
            GAAPAccountClassification.SHORT_TERM_DEBT,
            GAAPAccountClassification.OTHER_CURRENT_LIABILITIES,
            GAAPAccountClassification.NON_CURRENT_LIABILITIES,
            GAAPAccountClassification.LONG_TERM_DEBT,
            GAAPAccountClassification.DEFERRED_TAX_LIABILITIES,
            GAAPAccountClassification.OTHER_NON_CURRENT_LIABILITIES,
        }
        
        equity_classifications = {
            GAAPAccountClassification.SHAREHOLDERS_EQUITY,
            GAAPAccountClassification.COMMON_STOCK,
            GAAPAccountClassification.RETAINED_EARNINGS,
            GAAPAccountClassification.ADDITIONAL_PAID_IN_CAPITAL,
            GAAPAccountClassification.TREASURY_STOCK,
            GAAPAccountClassification.OTHER_COMPREHENSIVE_INCOME,
        }
        
        income_statement_classifications = {
            GAAPAccountClassification.OPERATING_REVENUE,
            GAAPAccountClassification.PRODUCT_REVENUE,
            GAAPAccountClassification.SERVICE_REVENUE,
            GAAPAccountClassification.OTHER_OPERATING_REVENUE,
            GAAPAccountClassification.NON_OPERATING_REVENUE,
            GAAPAccountClassification.INTEREST_INCOME,
            GAAPAccountClassification.INVESTMENT_INCOME,
            GAAPAccountClassification.GAIN_ON_SALE,
            GAAPAccountClassification.OTHER_NON_OPERATING_REVENUE,
            GAAPAccountClassification.COST_OF_GOODS_SOLD,
            GAAPAccountClassification.DIRECT_MATERIALS,
            GAAPAccountClassification.DIRECT_LABOR,
            GAAPAccountClassification.MANUFACTURING_OVERHEAD,
            GAAPAccountClassification.OPERATING_EXPENSES,
            GAAPAccountClassification.SELLING_EXPENSES,
            GAAPAccountClassification.GENERAL_ADMINISTRATIVE,
            GAAPAccountClassification.RESEARCH_DEVELOPMENT,
            GAAPAccountClassification.DEPRECIATION_AMORTIZATION,
            GAAPAccountClassification.NON_OPERATING_EXPENSES,
            GAAPAccountClassification.INTEREST_EXPENSE,
            GAAPAccountClassification.LOSS_ON_SALE,
            GAAPAccountClassification.OTHER_NON_OPERATING_EXPENSES,
            GAAPAccountClassification.EXTRAORDINARY_ITEMS,
            GAAPAccountClassification.DISCONTINUED_OPERATIONS,
            GAAPAccountClassification.TAX_EXPENSE,
        }
        
        if gaap_classification in asset_classifications or gaap_classification in liability_classifications or gaap_classification in equity_classifications:
            return FinancialStatementCategory.BALANCE_SHEET
        elif gaap_classification in income_statement_classifications:
            return FinancialStatementCategory.INCOME_STATEMENT
        else:
            return FinancialStatementCategory.BALANCE_SHEET  # Default to balance sheet


class IndustrySpecificMappings:
    """Industry-specific account mappings and classifications."""
    
    TECHNOLOGY_MAPPINGS = {
        "software_revenue": GAAPAccountClassification.PRODUCT_REVENUE,
        "saas_revenue": GAAPAccountClassification.SERVICE_REVENUE,
        "licensing_revenue": GAAPAccountClassification.PRODUCT_REVENUE,
        "development_costs": GAAPAccountClassification.RESEARCH_DEVELOPMENT,
        "cloud_infrastructure": GAAPAccountClassification.OPERATING_EXPENSES,
    }
    
    MANUFACTURING_MAPPINGS = {
        "raw_materials": GAAPAccountClassification.DIRECT_MATERIALS,
        "work_in_process": GAAPAccountClassification.INVENTORY,
        "finished_goods": GAAPAccountClassification.INVENTORY,
        "factory_overhead": GAAPAccountClassification.MANUFACTURING_OVERHEAD,
        "equipment_depreciation": GAAPAccountClassification.DEPRECIATION_AMORTIZATION,
    }
    
    RETAIL_MAPPINGS = {
        "merchandise_inventory": GAAPAccountClassification.INVENTORY,
        "store_operations": GAAPAccountClassification.SELLING_EXPENSES,
        "customer_returns": GAAPAccountClassification.OPERATING_REVENUE,  # Contra-revenue
        "loyalty_program": GAAPAccountClassification.SELLING_EXPENSES,
    }
    
    PROFESSIONAL_SERVICES_MAPPINGS = {
        "billable_hours": GAAPAccountClassification.SERVICE_REVENUE,
        "project_costs": GAAPAccountClassification.COST_OF_GOODS_SOLD,
        "professional_development": GAAPAccountClassification.GENERAL_ADMINISTRATIVE,
        "client_entertainment": GAAPAccountClassification.SELLING_EXPENSES,
    }
    
    @classmethod
    def get_industry_mapping(cls, industry: str) -> Dict[str, GAAPAccountClassification]:
        """Get industry-specific account mappings."""
        industry_mappings = {
            "technology": cls.TECHNOLOGY_MAPPINGS,
            "manufacturing": cls.MANUFACTURING_MAPPINGS,
            "retail": cls.RETAIL_MAPPINGS,
            "professional_services": cls.PROFESSIONAL_SERVICES_MAPPINGS,
        }
        return industry_mappings.get(industry.lower(), {})


def classify_account_by_name(account_name: str, account_type: Optional[str] = None) -> Optional[GAAPAccountClassification]:
    """Classify an account based on its name and optional type."""
    if not account_name:
        return None
    
    name_lower = account_name.lower()
    
    # Revenue patterns
    if any(keyword in name_lower for keyword in ['revenue', 'sales', 'income']):
        if any(keyword in name_lower for keyword in ['interest', 'investment', 'dividend']):
            return GAAPAccountClassification.NON_OPERATING_REVENUE
        return GAAPAccountClassification.OPERATING_REVENUE
    
    # Expense patterns
    if any(keyword in name_lower for keyword in ['expense', 'cost', 'fee']):
        if 'interest' in name_lower:
            return GAAPAccountClassification.INTEREST_EXPENSE
        if any(keyword in name_lower for keyword in ['cogs', 'cost of goods', 'cost of sales']):
            return GAAPAccountClassification.COST_OF_GOODS_SOLD
        if any(keyword in name_lower for keyword in ['selling', 'marketing', 'advertising']):
            return GAAPAccountClassification.SELLING_EXPENSES
        if any(keyword in name_lower for keyword in ['admin', 'general', 'office']):
            return GAAPAccountClassification.GENERAL_ADMINISTRATIVE
        if any(keyword in name_lower for keyword in ['research', 'development', 'r&d']):
            return GAAPAccountClassification.RESEARCH_DEVELOPMENT
        return GAAPAccountClassification.OPERATING_EXPENSES
    
    # Asset patterns
    if any(keyword in name_lower for keyword in ['cash', 'bank', 'checking', 'savings']):
        return GAAPAccountClassification.CASH_AND_EQUIVALENTS
    if any(keyword in name_lower for keyword in ['receivable', 'ar', 'accounts receivable']):
        return GAAPAccountClassification.ACCOUNTS_RECEIVABLE
    if 'inventory' in name_lower:
        return GAAPAccountClassification.INVENTORY
    if any(keyword in name_lower for keyword in ['equipment', 'property', 'building', 'vehicle']):
        return GAAPAccountClassification.PROPERTY_PLANT_EQUIPMENT
    
    # Liability patterns
    if any(keyword in name_lower for keyword in ['payable', 'ap', 'accounts payable']):
        return GAAPAccountClassification.ACCOUNTS_PAYABLE
    if any(keyword in name_lower for keyword in ['loan', 'debt', 'note payable']):
        if 'long' in name_lower or 'term' in name_lower:
            return GAAPAccountClassification.LONG_TERM_DEBT
        return GAAPAccountClassification.SHORT_TERM_DEBT
    
    # Equity patterns
    if any(keyword in name_lower for keyword in ['equity', 'capital', 'retained earnings']):
        if 'retained' in name_lower:
            return GAAPAccountClassification.RETAINED_EARNINGS
        return GAAPAccountClassification.SHAREHOLDERS_EQUITY
    
    # Use account type as fallback
    if account_type:
        return AccountTypeMapping.get_gaap_classification(account_type)
    
    return None
