import os
import yaml
from typing import Dict, Any, Optional
from pydantic_settings import BaseSettings as _BaseSettings
import logging

logger = logging.getLogger(__name__)


class Settings(_BaseSettings):
    """Application settings with environment variable support."""

    # Database
    database_url: str = "postgresql://user:password@localhost:5432/mcx3d_db"

    # Redis
    redis_url: str = "redis://localhost:6379/0"

    # Xero API
    xero_client_id: Optional[str] = None
    xero_client_secret: Optional[str] = None
    xero_webhook_key: Optional[str] = None
    xero_redirect_uri: str = "http://localhost:8000/api/auth/xero/callback"
    xero_scopes: str = "accounting.transactions accounting.contacts accounting.reports.read accounting.settings"
    
    # Security
    secret_key: Optional[str] = None
    encryption_key: Optional[str] = None
    jwt_algorithm: str = "HS256"
    access_token_expire_minutes: int = 30

    # Application
    debug: bool = False
    log_level: str = "INFO"

    class Config:
        env_file = ".env"
        case_sensitive = False
        extra = "allow"  # Allow extra fields from environment


def load_config(config_path: str = "config.yml") -> Dict[str, Any]:
    """Load configuration from YAML file with error handling."""
    try:
        if os.path.exists(config_path):
            with open(config_path, "r") as f:
                return yaml.safe_load(f) or {}
        else:
            logger.warning(f"Config file {config_path} not found, using defaults")
            return {}
    except yaml.YAMLError as e:
        logger.error(f"Error parsing config file: {e}")
        return {}
    except Exception as e:
        logger.error(f"Unexpected error loading config: {e}")
        return {}


# Global settings instance
settings = Settings()
config = load_config()


def get_database_url() -> str:
    """Get database URL from settings or config."""
    return settings.database_url or config.get("database", {}).get(
        "url", settings.database_url
    )


def get_xero_config() -> Dict[str, Any]:
    """Get Xero configuration."""
    return {
        "client_id": settings.xero_client_id or config.get("xero", {}).get("client_id"),
        "client_secret": settings.xero_client_secret
        or config.get("xero", {}).get("client_secret"),
        "webhook_key": settings.xero_webhook_key
        or config.get("xero", {}).get("webhook_key"),
        "redirect_uri": settings.xero_redirect_uri 
        or config.get("xero", {}).get("redirect_uri", "http://localhost:8000/api/auth/xero/callback"),
        "scopes": settings.xero_scopes
        or config.get("xero", {}).get("scopes", "accounting.transactions accounting.contacts accounting.reports.read accounting.settings"),
    }


def get_xero_webhook_key() -> Optional[str]:
    """Get Xero webhook key."""
    return settings.xero_webhook_key or config.get("xero", {}).get("webhook_key")


def get_security_config() -> Dict[str, Any]:
    """Get security configuration from environment or config file."""
    return {
        "secret_key": settings.secret_key or config.get("security", {}).get("secret_key", "your-secret-key-here-minimum-32-characters-long"),
        "encryption_key": settings.encryption_key or config.get("security", {}).get("encryption_key"),
        "algorithm": settings.jwt_algorithm or config.get("security", {}).get("algorithm", "HS256"),
        "access_token_expire_minutes": settings.access_token_expire_minutes or config.get("security", {}).get("access_token_expire_minutes", 30),
    }
