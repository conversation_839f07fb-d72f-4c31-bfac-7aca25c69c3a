"""
Advanced account mapping module with industry-specific chart of accounts.
Provides flexible mapping rules and intelligent account classification.
"""

from typing import Dict, Any, List
import logging
from enum import Enum
from dataclasses import dataclass
import re
from functools import lru_cache

from mcx3d_finance.core.account_classifications import GAAPAccountClassification

logger = logging.getLogger(__name__)


class IndustryType(Enum):
    """Industry classifications for specialized account mapping."""
    TECHNOLOGY = "technology"
    MANUFACTURING = "manufacturing"
    RETAIL = "retail"
    HEALTHCARE = "healthcare"
    FINANCIAL_SERVICES = "financial_services"
    REAL_ESTATE = "real_estate"
    CONSTRUCTION = "construction"
    PROFESSIONAL_SERVICES = "professional_services"
    HOSPITALITY = "hospitality"
    EDUCATION = "education"
    NON_PROFIT = "non_profit"
    GENERAL = "general"


class AccountType(Enum):
    """Extended account types for comprehensive classification."""
    ASSET = "asset"
    LIABILITY = "liability"
    EQUITY = "equity"
    REVENUE = "revenue"
    EXPENSE = "expense"
    CONTRA_ASSET = "contra_asset"
    CONTRA_LIABILITY = "contra_liability"
    CONTRA_EQUITY = "contra_equity"
    CONTRA_REVENUE = "contra_revenue"


@dataclass
class AccountMappingRule:
    """Account mapping rule definition."""
    pattern: str  # Regex pattern or exact match
    gaap_classification: GAAPAccountClassification
    account_type: AccountType
    industry_specific: bool = False
    confidence_score: float = 1.0
    description: str = ""
    
    def matches(self, account_code: str, account_name: str) -> bool:
        """Check if this rule matches the given account."""
        try:
            # Try exact code match first
            if self.pattern == account_code:
                return True
            
            # Try regex pattern match on code
            if re.match(self.pattern, account_code, re.IGNORECASE):
                return True
            
            # Try pattern match on name
            if re.search(self.pattern, account_name, re.IGNORECASE):
                return True
            
            return False
        except re.error:
            # If regex is invalid, fall back to string contains
            return (self.pattern.lower() in account_code.lower() or 
                    self.pattern.lower() in account_name.lower())


@dataclass
class MappingResult:
    """Result of account mapping operation."""
    original_code: str
    original_name: str
    gaap_classification: GAAPAccountClassification
    account_type: AccountType
    confidence_score: float
    rule_description: str
    industry_specific: bool = False


class AdvancedAccountMapper:
    """Advanced account mapper with industry-specific rules."""
    
    def __init__(self, industry: IndustryType = IndustryType.GENERAL):
        self.industry = industry
        self.mapping_rules = self._load_mapping_rules()
        self.custom_rules: List[AccountMappingRule] = []
        self._mapping_cache: Dict[str, MappingResult] = {}
    
    def _load_mapping_rules(self) -> List[AccountMappingRule]:
        """Load comprehensive mapping rules."""
        rules = []
        
        # Standard GAAP mappings
        rules.extend(self._get_standard_asset_rules())
        rules.extend(self._get_standard_liability_rules())
        rules.extend(self._get_standard_equity_rules())
        rules.extend(self._get_standard_revenue_rules())
        rules.extend(self._get_standard_expense_rules())
        
        # Industry-specific rules
        if self.industry != IndustryType.GENERAL:
            rules.extend(self._get_industry_specific_rules())
        
        return rules
    
    def _get_standard_asset_rules(self) -> List[AccountMappingRule]:
        """Get standard asset account mapping rules."""
        return [
            # Cash and Cash Equivalents
            AccountMappingRule(
                pattern=r"^10[0-2]\d",
                gaap_classification=GAAPAccountClassification.CASH_AND_EQUIVALENTS,
                account_type=AccountType.ASSET,
                description="Cash and cash equivalents"
            ),
            AccountMappingRule(
                pattern=r"cash|checking|savings|money market",
                gaap_classification=GAAPAccountClassification.CASH_AND_EQUIVALENTS,
                account_type=AccountType.ASSET,
                confidence_score=0.9,
                description="Cash accounts by name"
            ),
            
            # Accounts Receivable
            AccountMappingRule(
                pattern=r"^12\d\d",
                gaap_classification=GAAPAccountClassification.ACCOUNTS_RECEIVABLE,
                account_type=AccountType.ASSET,
                description="Accounts receivable"
            ),
            AccountMappingRule(
                pattern=r"receivable|a/r|trade debtors",
                gaap_classification=GAAPAccountClassification.ACCOUNTS_RECEIVABLE,
                account_type=AccountType.ASSET,
                confidence_score=0.9,
                description="Receivables by name"
            ),
            
            # Inventory
            AccountMappingRule(
                pattern=r"^13\d\d",
                gaap_classification=GAAPAccountClassification.INVENTORY,
                account_type=AccountType.ASSET,
                description="Inventory accounts"
            ),
            AccountMappingRule(
                pattern=r"inventory|stock|raw materials|finished goods|wip",
                gaap_classification=GAAPAccountClassification.INVENTORY,
                account_type=AccountType.ASSET,
                confidence_score=0.9,
                description="Inventory by name"
            ),
            
            # Prepaid Expenses
            AccountMappingRule(
                pattern=r"^14\d\d",
                gaap_classification=GAAPAccountClassification.PREPAID_EXPENSES,
                account_type=AccountType.ASSET,
                description="Prepaid expenses"
            ),
            AccountMappingRule(
                pattern=r"prepaid|deferred|advance",
                gaap_classification=GAAPAccountClassification.PREPAID_EXPENSES,
                account_type=AccountType.ASSET,
                confidence_score=0.8,
                description="Prepaid expenses by name"
            ),
            
            # Property, Plant & Equipment
            AccountMappingRule(
                pattern=r"^15\d\d|^16\d\d",
                gaap_classification=GAAPAccountClassification.PROPERTY_PLANT_EQUIPMENT,
                account_type=AccountType.ASSET,
                description="Property, plant & equipment"
            ),
            AccountMappingRule(
                pattern=r"equipment|machinery|building|land|vehicle|furniture|computer",
                gaap_classification=GAAPAccountClassification.PROPERTY_PLANT_EQUIPMENT,
                account_type=AccountType.ASSET,
                confidence_score=0.8,
                description="PP&E by name"
            ),
            
            # Intangible Assets
            AccountMappingRule(
                pattern=r"^17\d\d",
                gaap_classification=GAAPAccountClassification.INTANGIBLE_ASSETS,
                account_type=AccountType.ASSET,
                description="Intangible assets"
            ),
            AccountMappingRule(
                pattern=r"patent|trademark|copyright|software|license|goodwill",
                gaap_classification=GAAPAccountClassification.INTANGIBLE_ASSETS,
                account_type=AccountType.ASSET,
                confidence_score=0.9,
                description="Intangible assets by name"
            ),
        ]
    
    def _get_standard_liability_rules(self) -> List[AccountMappingRule]:
        """Get standard liability account mapping rules."""
        return [
            # Accounts Payable
            AccountMappingRule(
                pattern=r"^20\d\d",
                gaap_classification=GAAPAccountClassification.ACCOUNTS_PAYABLE,
                account_type=AccountType.LIABILITY,
                description="Accounts payable"
            ),
            AccountMappingRule(
                pattern=r"payable|a/p|trade creditors",
                gaap_classification=GAAPAccountClassification.ACCOUNTS_PAYABLE,
                account_type=AccountType.LIABILITY,
                confidence_score=0.9,
                description="Payables by name"
            ),
            
            # Accrued Liabilities
            AccountMappingRule(
                pattern=r"^21\d\d",
                gaap_classification=GAAPAccountClassification.ACCRUED_LIABILITIES,
                account_type=AccountType.LIABILITY,
                description="Accrued liabilities"
            ),
            AccountMappingRule(
                pattern=r"accrued|accrual|wages payable|interest payable",
                gaap_classification=GAAPAccountClassification.ACCRUED_LIABILITIES,
                account_type=AccountType.LIABILITY,
                confidence_score=0.9,
                description="Accrued liabilities by name"
            ),
            
            # Short-term Debt
            AccountMappingRule(
                pattern=r"^22\d\d",
                gaap_classification=GAAPAccountClassification.SHORT_TERM_DEBT,
                account_type=AccountType.LIABILITY,
                description="Short-term debt"
            ),
            AccountMappingRule(
                pattern=r"short.?term debt|notes payable|line of credit",
                gaap_classification=GAAPAccountClassification.SHORT_TERM_DEBT,
                account_type=AccountType.LIABILITY,
                confidence_score=0.9,
                description="Short-term debt by name"
            ),
            
            # Long-term Debt
            AccountMappingRule(
                pattern=r"^25\d\d|^26\d\d",
                gaap_classification=GAAPAccountClassification.LONG_TERM_DEBT,
                account_type=AccountType.LIABILITY,
                description="Long-term debt"
            ),
            AccountMappingRule(
                pattern=r"long.?term debt|mortgage|loan|bond",
                gaap_classification=GAAPAccountClassification.LONG_TERM_DEBT,
                account_type=AccountType.LIABILITY,
                confidence_score=0.9,
                description="Long-term debt by name"
            ),
        ]
    
    def _get_standard_equity_rules(self) -> List[AccountMappingRule]:
        """Get standard equity account mapping rules."""
        return [
            # Common Stock
            AccountMappingRule(
                pattern=r"^30\d\d",
                gaap_classification=GAAPAccountClassification.COMMON_STOCK,
                account_type=AccountType.EQUITY,
                description="Common stock"
            ),
            AccountMappingRule(
                pattern=r"common stock|share capital|capital stock",
                gaap_classification=GAAPAccountClassification.COMMON_STOCK,
                account_type=AccountType.EQUITY,
                confidence_score=0.9,
                description="Common stock by name"
            ),
            
            # Retained Earnings
            AccountMappingRule(
                pattern=r"^32\d\d",
                gaap_classification=GAAPAccountClassification.RETAINED_EARNINGS,
                account_type=AccountType.EQUITY,
                description="Retained earnings"
            ),
            AccountMappingRule(
                pattern=r"retained earnings|accumulated earnings|profit",
                gaap_classification=GAAPAccountClassification.RETAINED_EARNINGS,
                account_type=AccountType.EQUITY,
                confidence_score=0.9,
                description="Retained earnings by name"
            ),
            
            # Additional Paid-in Capital
            AccountMappingRule(
                pattern=r"^31\d\d",
                gaap_classification=GAAPAccountClassification.ADDITIONAL_PAID_IN_CAPITAL,
                account_type=AccountType.EQUITY,
                description="Additional paid-in capital"
            ),
            AccountMappingRule(
                pattern=r"paid.?in capital|premium|additional capital",
                gaap_classification=GAAPAccountClassification.ADDITIONAL_PAID_IN_CAPITAL,
                account_type=AccountType.EQUITY,
                confidence_score=0.9,
                description="Additional paid-in capital by name"
            ),
        ]
    
    def _get_standard_revenue_rules(self) -> List[AccountMappingRule]:
        """Get standard revenue account mapping rules."""
        return [
            # Product Revenue
            AccountMappingRule(
                pattern=r"^40\d\d",
                gaap_classification=GAAPAccountClassification.PRODUCT_REVENUE,
                account_type=AccountType.REVENUE,
                description="Product revenue"
            ),
            AccountMappingRule(
                pattern=r"product sales|merchandise|goods sold",
                gaap_classification=GAAPAccountClassification.PRODUCT_REVENUE,
                account_type=AccountType.REVENUE,
                confidence_score=0.9,
                description="Product revenue by name"
            ),
            
            # Service Revenue
            AccountMappingRule(
                pattern=r"^41\d\d",
                gaap_classification=GAAPAccountClassification.SERVICE_REVENUE,
                account_type=AccountType.REVENUE,
                description="Service revenue"
            ),
            AccountMappingRule(
                pattern=r"service|consulting|professional fees|labor",
                gaap_classification=GAAPAccountClassification.SERVICE_REVENUE,
                account_type=AccountType.REVENUE,
                confidence_score=0.8,
                description="Service revenue by name"
            ),
            
            # Subscription Revenue
            AccountMappingRule(
                pattern=r"^42\d\d",
                gaap_classification=GAAPAccountClassification.SUBSCRIPTION_REVENUE,
                account_type=AccountType.REVENUE,
                description="Subscription revenue"
            ),
            AccountMappingRule(
                pattern=r"subscription|recurring|saas|monthly|annual",
                gaap_classification=GAAPAccountClassification.SUBSCRIPTION_REVENUE,
                account_type=AccountType.REVENUE,
                confidence_score=0.9,
                description="Subscription revenue by name"
            ),
        ]
    
    def _get_standard_expense_rules(self) -> List[AccountMappingRule]:
        """Get standard expense account mapping rules."""
        return [
            # Cost of Goods Sold
            AccountMappingRule(
                pattern=r"^50\d\d",
                gaap_classification=GAAPAccountClassification.COST_OF_GOODS_SOLD,
                account_type=AccountType.EXPENSE,
                description="Cost of goods sold"
            ),
            AccountMappingRule(
                pattern=r"cost of goods|cogs|cost of sales|direct cost",
                gaap_classification=GAAPAccountClassification.COST_OF_GOODS_SOLD,
                account_type=AccountType.EXPENSE,
                confidence_score=0.9,
                description="COGS by name"
            ),
            
            # Sales and Marketing
            AccountMappingRule(
                pattern=r"^60\d\d",
                gaap_classification=GAAPAccountClassification.SALES_AND_MARKETING,
                account_type=AccountType.EXPENSE,
                description="Sales and marketing"
            ),
            AccountMappingRule(
                pattern=r"sales|marketing|advertising|promotion|commission",
                gaap_classification=GAAPAccountClassification.SALES_AND_MARKETING,
                account_type=AccountType.EXPENSE,
                confidence_score=0.8,
                description="Sales and marketing by name"
            ),
            
            # General and Administrative
            AccountMappingRule(
                pattern=r"^61\d\d",
                gaap_classification=GAAPAccountClassification.GENERAL_AND_ADMINISTRATIVE,
                account_type=AccountType.EXPENSE,
                description="General and administrative"
            ),
            AccountMappingRule(
                pattern=r"administrative|office|legal|accounting|insurance|utilities",
                gaap_classification=GAAPAccountClassification.GENERAL_AND_ADMINISTRATIVE,
                account_type=AccountType.EXPENSE,
                confidence_score=0.8,
                description="G&A by name"
            ),
            
            # Research and Development
            AccountMappingRule(
                pattern=r"^62\d\d",
                gaap_classification=GAAPAccountClassification.RESEARCH_AND_DEVELOPMENT,
                account_type=AccountType.EXPENSE,
                description="Research and development"
            ),
            AccountMappingRule(
                pattern=r"research|development|r&d|engineering",
                gaap_classification=GAAPAccountClassification.RESEARCH_AND_DEVELOPMENT,
                account_type=AccountType.EXPENSE,
                confidence_score=0.9,
                description="R&D by name"
            ),
        ]
    
    def _get_industry_specific_rules(self) -> List[AccountMappingRule]:
        """Get industry-specific mapping rules."""
        rules = []
        
        if self.industry == IndustryType.TECHNOLOGY:
            rules.extend([
                AccountMappingRule(
                    pattern=r"cloud|hosting|server|infrastructure",
                    gaap_classification=GAAPAccountClassification.COST_OF_SERVICES,
                    account_type=AccountType.EXPENSE,
                    industry_specific=True,
                    confidence_score=0.9,
                    description="Technology infrastructure costs"
                ),
                AccountMappingRule(
                    pattern=r"license|software|api|platform",
                    gaap_classification=GAAPAccountClassification.SUBSCRIPTION_REVENUE,
                    account_type=AccountType.REVENUE,
                    industry_specific=True,
                    confidence_score=0.8,
                    description="Technology licensing revenue"
                ),
            ])
        
        elif self.industry == IndustryType.MANUFACTURING:
            rules.extend([
                AccountMappingRule(
                    pattern=r"raw material|component|parts",
                    gaap_classification=GAAPAccountClassification.INVENTORY,
                    account_type=AccountType.ASSET,
                    industry_specific=True,
                    confidence_score=0.9,
                    description="Manufacturing inventory"
                ),
                AccountMappingRule(
                    pattern=r"factory|plant|production|manufacturing",
                    gaap_classification=GAAPAccountClassification.PROPERTY_PLANT_EQUIPMENT,
                    account_type=AccountType.ASSET,
                    industry_specific=True,
                    confidence_score=0.9,
                    description="Manufacturing facilities"
                ),
            ])
        
        elif self.industry == IndustryType.RETAIL:
            rules.extend([
                AccountMappingRule(
                    pattern=r"merchandise|retail|store",
                    gaap_classification=GAAPAccountClassification.INVENTORY,
                    account_type=AccountType.ASSET,
                    industry_specific=True,
                    confidence_score=0.9,
                    description="Retail inventory"
                ),
                AccountMappingRule(
                    pattern=r"store|retail|location",
                    gaap_classification=GAAPAccountClassification.PROPERTY_PLANT_EQUIPMENT,
                    account_type=AccountType.ASSET,
                    industry_specific=True,
                    confidence_score=0.8,
                    description="Retail locations"
                ),
            ])
        
        return rules

    @lru_cache(maxsize=1000)
    def map_account(self, account_code: str, account_name: str = "") -> MappingResult:
        """Map an account to GAAP classification."""
        try:
            # Check cache first
            cache_key = f"{account_code}_{account_name}"
            if cache_key in self._mapping_cache:
                return self._mapping_cache[cache_key]

            # Try custom rules first (highest priority)
            for rule in self.custom_rules:
                if rule.matches(account_code, account_name):
                    result = MappingResult(
                        original_code=account_code,
                        original_name=account_name,
                        gaap_classification=rule.gaap_classification,
                        account_type=rule.account_type,
                        confidence_score=rule.confidence_score,
                        rule_description=rule.description,
                        industry_specific=rule.industry_specific
                    )
                    self._mapping_cache[cache_key] = result
                    return result

            # Try standard mapping rules
            best_match = None
            best_score = 0.0

            for rule in self.mapping_rules:
                if rule.matches(account_code, account_name):
                    if rule.confidence_score > best_score:
                        best_match = rule
                        best_score = rule.confidence_score

            if best_match:
                result = MappingResult(
                    original_code=account_code,
                    original_name=account_name,
                    gaap_classification=best_match.gaap_classification,
                    account_type=best_match.account_type,
                    confidence_score=best_match.confidence_score,
                    rule_description=best_match.description,
                    industry_specific=best_match.industry_specific
                )
                self._mapping_cache[cache_key] = result
                return result

            # Default fallback based on account code pattern
            result = self._fallback_mapping(account_code, account_name)
            self._mapping_cache[cache_key] = result
            return result

        except Exception as e:
            logger.error(f"Error mapping account {account_code}: {e}")
            return MappingResult(
                original_code=account_code,
                original_name=account_name,
                gaap_classification=GAAPAccountClassification.OTHER_CURRENT_ASSETS,
                account_type=AccountType.ASSET,
                confidence_score=0.1,
                rule_description="Error fallback"
            )

    def _fallback_mapping(self, account_code: str, account_name: str) -> MappingResult:
        """Provide fallback mapping based on account code patterns."""
        try:
            # Basic pattern matching based on first digit
            if account_code and account_code[0].isdigit():
                first_digit = int(account_code[0])

                if first_digit == 1:  # Assets
                    return MappingResult(
                        original_code=account_code,
                        original_name=account_name,
                        gaap_classification=GAAPAccountClassification.OTHER_CURRENT_ASSETS,
                        account_type=AccountType.ASSET,
                        confidence_score=0.3,
                        rule_description="Fallback: Asset based on code pattern"
                    )
                elif first_digit == 2:  # Liabilities
                    return MappingResult(
                        original_code=account_code,
                        original_name=account_name,
                        gaap_classification=GAAPAccountClassification.OTHER_CURRENT_LIABILITIES,
                        account_type=AccountType.LIABILITY,
                        confidence_score=0.3,
                        rule_description="Fallback: Liability based on code pattern"
                    )
                elif first_digit == 3:  # Equity
                    return MappingResult(
                        original_code=account_code,
                        original_name=account_name,
                        gaap_classification=GAAPAccountClassification.RETAINED_EARNINGS,
                        account_type=AccountType.EQUITY,
                        confidence_score=0.3,
                        rule_description="Fallback: Equity based on code pattern"
                    )
                elif first_digit == 4:  # Revenue
                    return MappingResult(
                        original_code=account_code,
                        original_name=account_name,
                        gaap_classification=GAAPAccountClassification.OTHER_REVENUE,
                        account_type=AccountType.REVENUE,
                        confidence_score=0.3,
                        rule_description="Fallback: Revenue based on code pattern"
                    )
                elif first_digit >= 5:  # Expenses
                    return MappingResult(
                        original_code=account_code,
                        original_name=account_name,
                        gaap_classification=GAAPAccountClassification.GENERAL_AND_ADMINISTRATIVE,
                        account_type=AccountType.EXPENSE,
                        confidence_score=0.3,
                        rule_description="Fallback: Expense based on code pattern"
                    )

            # Ultimate fallback
            return MappingResult(
                original_code=account_code,
                original_name=account_name,
                gaap_classification=GAAPAccountClassification.OTHER_CURRENT_ASSETS,
                account_type=AccountType.ASSET,
                confidence_score=0.1,
                rule_description="Ultimate fallback"
            )

        except Exception as e:
            logger.error(f"Error in fallback mapping: {e}")
            return MappingResult(
                original_code=account_code,
                original_name=account_name,
                gaap_classification=GAAPAccountClassification.OTHER_CURRENT_ASSETS,
                account_type=AccountType.ASSET,
                confidence_score=0.1,
                rule_description="Error fallback"
            )

    def add_custom_rule(self, rule: AccountMappingRule):
        """Add a custom mapping rule."""
        self.custom_rules.append(rule)
        self._mapping_cache.clear()  # Clear cache when rules change
        self.map_account.cache_clear()  # Clear LRU cache

    def map_chart_of_accounts(self, accounts: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Map entire chart of accounts."""
        try:
            mapped_accounts = []
            mapping_stats = {
                "total_accounts": len(accounts),
                "high_confidence": 0,
                "medium_confidence": 0,
                "low_confidence": 0,
                "industry_specific": 0,
                "unmapped": 0
            }

            for account in accounts:
                account_code = account.get("code", "")
                account_name = account.get("name", "")

                mapping_result = self.map_account(account_code, account_name)

                # Update statistics
                if mapping_result.confidence_score >= 0.8:
                    mapping_stats["high_confidence"] += 1
                elif mapping_result.confidence_score >= 0.5:
                    mapping_stats["medium_confidence"] += 1
                else:
                    mapping_stats["low_confidence"] += 1

                if mapping_result.industry_specific:
                    mapping_stats["industry_specific"] += 1

                if mapping_result.confidence_score < 0.2:
                    mapping_stats["unmapped"] += 1

                # Create enhanced account record
                mapped_account = account.copy()
                mapped_account.update({
                    "gaap_classification": mapping_result.gaap_classification.value,
                    "account_type": mapping_result.account_type.value,
                    "confidence_score": mapping_result.confidence_score,
                    "mapping_rule": mapping_result.rule_description,
                    "industry_specific": mapping_result.industry_specific
                })

                mapped_accounts.append(mapped_account)

            return {
                "mapped_accounts": mapped_accounts,
                "mapping_statistics": mapping_stats,
                "industry": self.industry.value,
                "total_rules_applied": len(self.mapping_rules) + len(self.custom_rules)
            }

        except Exception as e:
            logger.error(f"Error mapping chart of accounts: {e}")
            return {"error": str(e), "mapped_accounts": accounts}

    def validate_mapping_quality(self, accounts: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Validate the quality of account mappings."""
        try:
            total_accounts = len(accounts)
            if total_accounts == 0:
                return {"error": "No accounts to validate"}

            quality_metrics: Dict[str, Any] = {
                "total_accounts": total_accounts,
                "high_confidence_count": 0,
                "medium_confidence_count": 0,
                "low_confidence_count": 0,
                "industry_specific_count": 0,
                "coverage_by_type": {},
                "recommendations": []
            }

            account_types: Dict[str, int] = {}

            for account in accounts:
                account_code = account.get("code", "")
                account_name = account.get("name", "")

                mapping_result = self.map_account(account_code, account_name)

                # Count confidence levels
                if mapping_result.confidence_score >= 0.8:
                    quality_metrics["high_confidence_count"] += 1
                elif mapping_result.confidence_score >= 0.5:
                    quality_metrics["medium_confidence_count"] += 1
                else:
                    quality_metrics["low_confidence_count"] += 1

                if mapping_result.industry_specific:
                    quality_metrics["industry_specific_count"] += 1

                # Count by account type
                account_type = mapping_result.account_type.value
                if account_type not in account_types:
                    account_types[account_type] = 0
                account_types[account_type] += 1

            quality_metrics["coverage_by_type"] = account_types

            # Generate recommendations
            high_confidence_pct = (quality_metrics["high_confidence_count"] / total_accounts) * 100
            low_confidence_pct = (quality_metrics["low_confidence_count"] / total_accounts) * 100

            if high_confidence_pct < 70:
                quality_metrics["recommendations"].append(
                    "Consider adding custom mapping rules to improve confidence scores"
                )

            if low_confidence_pct > 20:
                quality_metrics["recommendations"].append(
                    "Review low-confidence mappings and add specific rules for common patterns"
                )

            if self.industry == IndustryType.GENERAL and quality_metrics["industry_specific_count"] == 0:
                quality_metrics["recommendations"].append(
                    "Consider setting a specific industry type for better mapping accuracy"
                )

            quality_metrics["overall_quality_score"] = (
                (quality_metrics["high_confidence_count"] * 1.0 +
                 quality_metrics["medium_confidence_count"] * 0.6 +
                 quality_metrics["low_confidence_count"] * 0.2) / total_accounts
            ) * 100

            return quality_metrics

        except Exception as e:
            logger.error(f"Error validating mapping quality: {e}")
            return {"error": str(e)}

    def clear_cache(self):
        """Clear all caches."""
        self._mapping_cache.clear()
        self.map_account.cache_clear()
