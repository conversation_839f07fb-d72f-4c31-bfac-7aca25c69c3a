"""
MCX3D Finance CLI - Command Line Interface for financial data management.
"""

import click
import logging
import sys
import os
from .data import sync
# from .enhanced_reports import generate  # Temporarily disabled due to import issues
from .valuation import valuate
from .analytics import analytics
from .error_handler import handle_cli_errors, CLIErrorHandler, display_success_message

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

# Global debug mode for error handling
_debug_mode = False


@click.group()
@click.option('--debug/--no-debug', default=False, help='Enable debug logging and detailed error messages')
@click.option('--quiet/--verbose', default=False, help='Minimize output messages')
@handle_cli_errors(debug_mode=False)  # Will be updated based on debug flag
def cli(debug, quiet):
    """MCX3D Finance CLI - Financial data management and reporting tool.
    
    Features:
    - sync: Synchronize data from Xero
    - generate: Generate financial reports (temporarily disabled)
    - valuate: Run valuation models (DCF, multiples, SaaS) with professional PDF/Excel exports
    - analytics: Calculate SaaS KPIs and business metrics
    
    Professional Report Exports:
    Use 'valuate' commands with --export pdf/excel for investor-grade valuation reports.
    
    Error Handling:
    The CLI provides enhanced error messages and recovery suggestions.
    Use --debug for detailed technical information.
    """
    global _debug_mode
    _debug_mode = debug
    
    # Configure logging based on options
    if debug:
        logging.getLogger().setLevel(logging.DEBUG)
        click.echo('🔧 Debug mode enabled - showing detailed technical information')
    elif quiet:
        logging.getLogger().setLevel(logging.WARNING)
    
    # Display startup information (unless quiet)
    if not quiet:
        display_startup_info(debug)


def display_startup_info(debug_mode: bool):
    """Display startup information and system status."""
    try:
        import psutil
        
        if debug_mode:
            # Show detailed system information in debug mode
            memory = psutil.virtual_memory()
            disk = psutil.disk_usage('/')
            
            click.echo("\n🔍 System Information:")
            click.echo(f"   💾 Memory: {memory.percent:.1f}% used ({memory.available / (1024**3):.1f}GB available)")
            click.echo(f"   💿 Disk: {(disk.used / disk.total) * 100:.1f}% used ({disk.free / (1024**3):.1f}GB free)")
            click.echo(f"   🖥️ CPU cores: {psutil.cpu_count()}")
            
        else:
            # Just show welcome message in normal mode
            click.echo("📊 MCX3D Financial System Ready")
            
    except Exception as e:
        if debug_mode:
            click.echo(f"⚠️ Could not retrieve system info: {e}")


def get_debug_mode() -> bool:
    """Get current debug mode setting."""
    return _debug_mode


# Enhanced command registration with error handling
def register_commands():
    """Register all command groups with enhanced error handling."""
    try:
        cli.add_command(sync)
        # cli.add_command(generate)  # Temporarily disabled due to import issues
        cli.add_command(valuate)
        cli.add_command(analytics)
    except Exception as e:
        handler = CLIErrorHandler(debug_mode=get_debug_mode())
        handler.handle_exception(e, context={'operation': 'command_registration'})
        sys.exit(1)


# Register command groups
register_commands()


# Enhanced main execution with global error handling
def main():
    """Main CLI entry point with comprehensive error handling."""
    try:
        cli()
    except KeyboardInterrupt:
        click.echo("\n🛑 Operation cancelled by user")
        sys.exit(130)  # Standard exit code for SIGINT
    except Exception as e:
        # Final fallback error handler
        handler = CLIErrorHandler(debug_mode=get_debug_mode())
        exit_code = handler.handle_exception(
            error=e,
            context={'operation': 'main_execution'},
            command_name='mcx3d-cli'
        )
        sys.exit(exit_code)


if __name__ == '__main__':
    main()