import click
from typing import Optional
import logging
from mcx3d_finance.tasks.sync_tasks import sync_xero_data

logger = logging.getLogger(__name__)


@click.group()
def sync():
    """Synchronizes data from external sources."""
    pass


@sync.command("xero")
@click.option("--org-id", required=True, type=int, help="The organization ID to sync.")
@click.option(
    "--incremental",
    is_flag=True,
    help="Run an incremental sync instead of a full sync.",
)
@click.option(
    "--async-mode",
    is_flag=True,
    help="Run sync asynchronously using Celery.",
)
@click.option(
    "--show-progress",
    is_flag=True,
    default=True,
    help="Show progress during sync (for synchronous mode).",
)
def sync_xero(org_id: int, incremental: bool, async_mode: bool, show_progress: bool):
    """Sync data from Xero accounting system.
    
    This command imports all data from Xero including:
    - Chart of Accounts with GAAP classification
    - Contacts with business enrichment
    - Invoices (sales and purchase)
    - Bank transactions
    - Financial reports (Trial Balance, P&L)
    
    The data is processed through validation and enrichment pipelines
    before being stored in the database.
    """
    try:
        # Validate Xero configuration
        from mcx3d_finance.core.config import get_xero_config
        xero_config = get_xero_config()
        if not xero_config.get("client_id") or not xero_config.get("client_secret"):
            click.echo("Error: Xero API credentials not configured", err=True)
            click.echo("Please set XERO_CLIENT_ID and XERO_CLIENT_SECRET environment variables")
            return

        click.echo(f"Starting Xero sync for organization: {org_id}")
        click.echo(f"Mode: {'Incremental' if incremental else 'Full'} sync")
        click.echo(f"Processing: {'Asynchronous' if async_mode else 'Synchronous'}")

        if async_mode:
            # Dispatch Celery task
            task = sync_xero_data.delay(org_id, incremental)
            click.echo(f"\n✅ Sync task queued with ID: {task.id}")
            click.echo(f"Use 'mcx3d-finance sync status {task.id}' to check progress")
            click.echo("\nThe sync will run in the background. You can continue using the CLI.")
        else:
            # Run synchronously with progress display
            click.echo("\nStarting synchronous sync...")
            
            if show_progress:
                # Import progress bar utilities
                with click.progressbar(length=100, label='Syncing data') as bar:
                    last_progress = 0
                    
                    # Custom progress callback for direct execution
                    def progress_callback(state, meta):
                        nonlocal last_progress
                        progress = meta.get('progress', 0)
                        if progress > last_progress:
                            bar.update(progress - last_progress)
                            last_progress = progress
                        if meta.get('status'):
                            bar.label = meta['status']
                    
                    # For direct execution, we need to run the task function directly
                    # This simulates the Celery task execution
                    from mcx3d_finance.tasks.sync_tasks import sync_xero_data
                    
                    # Note: The actual sync_xero_data function doesn't support callbacks when run directly
                    # so we'll run it without real-time progress for now
                    result = sync_xero_data.apply(args=(org_id, incremental)).get()
            else:
                result = sync_xero_data(org_id, incremental)
            
            # Display results
            if result.get("success"):
                click.echo(f"\n✅ Sync completed successfully!")
                
                # Display statistics
                results = result.get('results', {})
                import_stats = results.get('import_stats', {})
                storage_stats = results.get('storage_stats', {})
                
                click.echo("\nImport Statistics:")
                for entity, stats in import_stats.items():
                    if isinstance(stats, dict) and 'imported' in stats:
                        click.echo(f"  {entity}: {stats['imported']}/{stats['total']} imported, {stats['errors']} errors")
                
                click.echo("\nStorage Statistics:")
                for entity, stats in storage_stats.items():
                    if isinstance(stats, dict):
                        created = stats.get('created', 0)
                        updated = stats.get('updated', 0)
                        skipped = stats.get('skipped', 0)
                        click.echo(f"  {entity}: {created} created, {updated} updated, {skipped} skipped")
                
                # Display any errors
                errors = results.get('errors', [])
                if errors:
                    click.echo(f"\n⚠️  Sync completed with {len(errors)} errors:")
                    for error in errors[:5]:  # Show first 5 errors
                        click.echo(f"  - {error}")
                    if len(errors) > 5:
                        click.echo(f"  ... and {len(errors) - 5} more errors")
            else:
                click.echo(f"\n❌ Sync failed: {result.get('error')}", err=True)

    except Exception as e:
        logger.error(f"Error in Xero sync command: {e}")
        click.echo(f"\n❌ Error: {e}", err=True)
        click.echo("Please check the logs for more details.")


@sync.command("status")
@click.argument("task_id")
def check_sync_status(task_id: str):
    """Check the status of a sync task."""
    try:
        from mcx3d_finance.tasks.celery_app import celery_app

        task = celery_app.AsyncResult(task_id)

        click.echo(f"Task ID: {task_id}")
        click.echo(f"Status: {task.status}")

        if task.status == "SUCCESS":
            result = task.result
            click.echo(f"Result: {result}")
        elif task.status == "FAILURE":
            click.echo(f"Error: {task.info}")
        elif task.status == "PENDING":
            click.echo("Task is still processing...")

    except Exception as e:
        logger.error(f"Error checking task status: {e}")
        click.echo(f"Error: {e}", err=True)


@click.command()
@click.option("--file", required=True, help="Excel file to import")
@click.option("--mapping", help="JSON file with account mapping configuration")
@click.option("--org-id", help="Target organization ID")
def import_excel(file: str, mapping: Optional[str], org_id: Optional[str]):
    """Import financial data from Excel file."""
    try:
        click.echo(f"Importing data from: {file}")

        # Implementation would go here

        # Process Excel file and import to database

        click.echo("Import completed successfully")

    except Exception as e:
        logger.error(f"Error importing Excel file: {e}")
        click.echo(f"Error: {e}", err=True)


sync.add_command(import_excel)
