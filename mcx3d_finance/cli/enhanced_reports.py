"""
Enhanced reports CLI module with comprehensive error handling and user-friendly messages.

Provides enhanced command-line interface for report generation with robust error handling,
graceful degradation, and detailed user feedback.
"""

import click
import logging
import time
from pathlib import Path
from typing import Optional, Dict, Any
from datetime import datetime, timedelta

from mcx3d_finance.reporting.enhanced_generator import (
    create_enhanced_generator,
    EnhancedReportGenerator
)
from mcx3d_finance.cli.error_handler import (
    handle_cli_errors,
    display_success_message,
    display_progress_info,
    display_warning
)
from mcx3d_finance.exceptions import ValidationError
from mcx3d_finance.db.session import get_db
from mcx3d_finance.db.models import Transaction, Account
from sqlalchemy.orm import Session

logger = logging.getLogger(__name__)


def parse_period(period: str) -> tuple[Optional[str], Optional[str]]:
    """Parse period string into start and end dates."""
    try:
        if "Q" in period:
            year, quarter_str = period.split("-Q")
            quarter = int(quarter_str)
            if quarter not in [1, 2, 3, 4]:
                raise ValueError(f"Invalid quarter: {quarter}. Must be 1-4.")
                
            start_month = (quarter - 1) * 3 + 1
            end_month = start_month + 2
            start_date = datetime(int(year), start_month, 1)
            
            # Get last day of end month
            if end_month == 12:
                end_date = datetime(int(year) + 1, 1, 1) - timedelta(days=1)
            else:
                end_date = datetime(int(year), end_month + 1, 1) - timedelta(days=1)
                
            return start_date.strftime("%Y-%m-%d"), end_date.strftime("%Y-%m-%d")
        
        elif "-" in period and len(period) == 7:  # YYYY-MM format
            year, month = period.split("-")
            start_date = datetime(int(year), int(month), 1)
            if int(month) == 12:
                end_date = datetime(int(year) + 1, 1, 1) - timedelta(days=1)
            else:
                end_date = datetime(int(year), int(month) + 1, 1) - timedelta(days=1)
            return start_date.strftime("%Y-%m-%d"), end_date.strftime("%Y-%m-%d")
        
        elif len(period) == 4:  # YYYY format
            year = int(period)
            start_date = datetime(year, 1, 1)
            end_date = datetime(year, 12, 31)
            return start_date.strftime("%Y-%m-%d"), end_date.strftime("%Y-%m-%d")
            
        else:
            raise ValueError(f"Invalid period format: {period}. Use YYYY-QN, YYYY-MM, or YYYY")
            
    except ValueError as e:
        raise ValidationError(f"Invalid period format: {e}", field_name="period")


def parse_date(date_str: str) -> tuple[str, str]:
    """Parse date string into start and end timestamps."""
    try:
        date_obj = datetime.strptime(date_str, "%Y-%m-%d")
        start_time = date_obj.strftime("%Y-%m-%d 00:00:00")
        end_time = date_obj.strftime("%Y-%m-%d 23:59:59")
        return start_time, end_time
    except ValueError as e:
        raise ValidationError(f"Invalid date format: {e}. Use YYYY-MM-DD", field_name="date")


def get_transactions(db: Session, organization_id: int, start_date: str, end_date: str):
    """Retrieve transactions for the given period with error handling."""
    try:
        return (
            db.query(Transaction)
            .join(Account)
            .filter(
                Transaction.organization_id == organization_id,
                Transaction.date.between(start_date, end_date),
            )
            .all()
        )
    except Exception as e:
        raise ValidationError(
            f"Failed to retrieve transaction data: {e}",
            field_name="database_query"
        )


def validate_organization_id(org_id: str) -> int:
    """Validate and convert organization ID."""
    try:
        org_id_int = int(org_id)
        if org_id_int <= 0:
            raise ValueError("Organization ID must be positive")
        return org_id_int
    except ValueError as e:
        raise ValidationError(
            f"Invalid organization ID: {e}",
            field_name="organization_id"
        )


def ensure_output_directory(output_path: str) -> str:
    """Ensure output directory exists and is writable."""
    try:
        output_file = Path(output_path)
        output_dir = output_file.parent
        
        # Create directory if it doesn't exist
        output_dir.mkdir(parents=True, exist_ok=True)
        
        # Test write permissions
        test_file = output_dir / ".test_write_permissions"
        try:
            test_file.touch()
            test_file.unlink()
        except PermissionError:
            raise PermissionError(f"No write permission for directory: {output_dir}")
            
        return str(output_file.resolve())
        
    except Exception as e:
        raise ValidationError(
            f"Output path validation failed: {e}",
            field_name="output_path"
        )


@click.group()
def generate():
    """Generate financial reports with enhanced error handling and fallback options."""
    pass


@generate.command("income-statement")
@click.option("--organization-id", required=True, help="Organization ID (positive integer)")
@click.option("--period", required=True, help='Financial period: YYYY-QN (e.g., "2023-Q4"), YYYY-MM, or YYYY')
@click.option(
    "--format",
    type=click.Choice(["pdf", "excel", "html", "csv", "json"]),
    default="pdf",
    help="Output format (PDF recommended, others available as fallback)"
)
@click.option("--output", help="Output file path (optional, auto-generated if not provided)")
@click.option("--complexity", 
              type=click.Choice(["full", "standard", "minimal", "essential"]),
              help="Report complexity level (auto-detected if not specified)")
@handle_cli_errors()
def income_statement(organization_id, period, format, output, complexity):
    """
    Generate income statement with enhanced error handling.
    
    Examples:
        mcx3d generate income-statement --organization-id 123 --period 2023-Q4
        mcx3d generate income-statement --organization-id 123 --period 2023-12 --format excel
        mcx3d generate income-statement --organization-id 123 --period 2023 --output /path/to/report.pdf
    """
    start_time = time.time()
    
    # Step 1: Validate inputs
    display_progress_info("Validating input parameters", 1, 5)
    
    org_id = validate_organization_id(organization_id)
    start_date, end_date = parse_period(period)
    
    # Generate output path if not provided
    if not output:
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        output = f"income_statement_org{org_id}_{period}_{timestamp}.{format}"
    
    output_path = ensure_output_directory(output)
    
    # Step 2: Connect to database and retrieve data
    display_progress_info("Connecting to database and retrieving transaction data", 2, 5)
    
    try:
        db = next(get_db())
        transactions = get_transactions(db, org_id, start_date, end_date)
        
        if not transactions:
            display_warning(
                f"No transactions found for organization {org_id} in period {period}",
                "Verify organization ID and period are correct"
            )
            click.echo("📊 Generating empty report template...")
    
    except Exception as e:
        raise ValidationError(f"Database connection failed: {e}", field_name="database")
    
    # Step 3: Process transaction data
    display_progress_info("Processing financial data", 3, 5)
    
    try:
        # Import here to avoid circular imports
        from mcx3d_finance.core.financials.income_statement import calculate_income_statement
        
        transaction_data = [
            {
                "account_type": transaction.account.type,
                "amount": float(transaction.amount),
                "description": transaction.description or "No description",
                "date": transaction.date.isoformat()
            }
            for transaction in transactions
        ]
        
        report_data = calculate_income_statement(transaction_data)
        
        # Add metadata
        report_data.update({
            "header": {
                "organization_id": org_id,
                "company_name": f"Organization {org_id}",
                "statement_title": "INCOME STATEMENT",
                "reporting_date": end_date,
                "period_start": start_date,
                "period_end": end_date,
                "generated_at": datetime.now().isoformat(),
                "currency": "USD"
            }
        })
        
    except Exception as e:
        raise ValidationError(f"Financial data processing failed: {e}", field_name="data_processing")
    
    # Step 4: Generate report using enhanced generator
    display_progress_info(f"Generating {format.upper()} report", 4, 5)
    
    try:
        generator = create_enhanced_generator(
            memory_threshold_mb=512.0,
            disk_threshold_mb=1000.0,
            enable_validation=True,
            enable_resource_monitoring=True
        )
        
        # Convert complexity string to enum if provided
        complexity_level = None
        if complexity:
            from mcx3d_finance.utils.graceful_degradation import ReportComplexity
            complexity_level = ReportComplexity(complexity)
        
        result = generator.generate_income_statement(
            income_statement_data=report_data,
            output_path=output_path,
            output_format=format,
            complexity=complexity_level
        )
        
    except Exception as e:
        # The enhanced generator should handle most errors gracefully
        raise ValidationError(f"Report generation failed: {e}", field_name="report_generation")
    
    # Step 5: Display results
    display_progress_info("Finalizing and displaying results", 5, 5)
    
    generation_time = time.time() - start_time
    
    if result.get('success'):
        display_success_message(
            f"Income statement generated successfully: {result['output_path']}",
            details={
                'format': result['format'],
                'complexity': str(result['complexity']).replace('ReportComplexity.', ''),
                'file_size_mb': result.get('file_size_mb', 0),
                'generation_time_seconds': generation_time,
                'transactions_processed': len(transactions)
            }
        )
        
        # Show any warnings or degradation info
        if result.get('degradation_applied'):
            click.echo("\n⚠️ Report generation adjustments:")
            for adjustment in result['degradation_applied']:
                click.echo(f"   • {adjustment}")
        
        if result.get('warnings'):
            click.echo("\n💡 Warnings:")
            for warning in result['warnings']:
                click.echo(f"   • {warning}")
    
    else:
        # This shouldn't happen with the enhanced generator, but just in case
        click.echo(f"\n❌ Report generation failed", err=True)
        if result.get('errors'):
            for error in result['errors']:
                click.echo(f"   Error: {error}", err=True)


@generate.command("balance-sheet")
@click.option("--organization-id", required=True, help="Organization ID (positive integer)")
@click.option("--date", required=True, help='Balance sheet date in YYYY-MM-DD format (e.g., "2023-12-31")')
@click.option(
    "--format",
    type=click.Choice(["pdf", "excel", "html", "csv", "json"]),
    default="pdf",
    help="Output format (PDF recommended, others available as fallback)"
)
@click.option("--output", help="Output file path (optional, auto-generated if not provided)")
@click.option("--complexity", 
              type=click.Choice(["full", "standard", "minimal", "essential"]),
              help="Report complexity level (auto-detected if not specified)")
@handle_cli_errors()
def balance_sheet(organization_id, date, format, output, complexity):
    """
    Generate balance sheet with enhanced error handling.
    
    Examples:
        mcx3d generate balance-sheet --organization-id 123 --date 2023-12-31
        mcx3d generate balance-sheet --organization-id 123 --date 2023-12-31 --format excel
    """
    start_time = time.time()
    
    # Step 1: Validate inputs
    display_progress_info("Validating input parameters", 1, 5)
    
    org_id = validate_organization_id(organization_id)
    start_date, end_date = parse_date(date)
    
    # Generate output path if not provided
    if not output:
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        date_str = date.replace("-", "")
        output = f"balance_sheet_org{org_id}_{date_str}_{timestamp}.{format}"
    
    output_path = ensure_output_directory(output)
    
    # Step 2: Connect to database and retrieve data
    display_progress_info("Retrieving balance sheet data", 2, 5)
    
    try:
        db = next(get_db())
        transactions = get_transactions(db, org_id, start_date, end_date)
        
        if not transactions:
            display_warning(
                f"No transactions found for organization {org_id} as of {date}",
                "Verify organization ID and date are correct"
            )
    
    except Exception as e:
        raise ValidationError(f"Database connection failed: {e}", field_name="database")
    
    # Step 3: Process balance sheet data
    display_progress_info("Processing balance sheet data", 3, 5)
    
    try:
        from mcx3d_finance.core.financials.balance_sheet import calculate_balance_sheet
        
        transaction_data = [
            {
                "account_type": transaction.account.type,
                "amount": float(transaction.amount),
                "account_name": transaction.account.name or f"Account {transaction.account_id}",
                "date": transaction.date.isoformat()
            }
            for transaction in transactions
        ]
        
        report_data = calculate_balance_sheet(transaction_data, as_of_date=date)
        
        # Add metadata
        report_data.update({
            "header": {
                "organization_id": org_id,
                "company_name": f"Organization {org_id}",
                "statement_title": "BALANCE SHEET",
                "reporting_date": date,
                "generated_at": datetime.now().isoformat(),
                "currency": "USD"
            }
        })
        
    except Exception as e:
        raise ValidationError(f"Balance sheet calculation failed: {e}", field_name="data_processing")
    
    # Step 4: Generate report
    display_progress_info(f"Generating {format.upper()} balance sheet", 4, 5)
    
    try:
        generator = create_enhanced_generator()
        
        complexity_level = None
        if complexity:
            from mcx3d_finance.utils.graceful_degradation import ReportComplexity
            complexity_level = ReportComplexity(complexity)
        
        result = generator.generate_balance_sheet(
            balance_sheet_data=report_data,
            output_path=output_path,
            output_format=format,
            complexity=complexity_level
        )
        
    except Exception as e:
        raise ValidationError(f"Balance sheet generation failed: {e}", field_name="report_generation")
    
    # Step 5: Display results
    display_progress_info("Finalizing results", 5, 5)
    
    generation_time = time.time() - start_time
    
    if result.get('success'):
        display_success_message(
            f"Balance sheet generated successfully: {result['output_path']}",
            details={
                'format': result['format'],
                'complexity': str(result['complexity']).replace('ReportComplexity.', ''),
                'file_size_mb': result.get('file_size_mb', 0),
                'generation_time_seconds': generation_time,
                'as_of_date': date,
                'transactions_processed': len(transactions)
            }
        )
        
        # Show any adjustments or warnings
        if result.get('degradation_applied'):
            click.echo("\n⚠️ Report generation adjustments:")
            for adjustment in result['degradation_applied']:
                click.echo(f"   • {adjustment}")
    
    else:
        click.echo(f"\n❌ Balance sheet generation failed", err=True)


@generate.command("test-system")
@click.option("--output-dir", default="./test_reports", help="Directory for test reports")
@handle_cli_errors()
def test_system(output_dir):
    """
    Test the enhanced reporting system with sample data.
    
    This command generates test reports to verify the system is working correctly.
    """
    click.echo("🧪 Testing MCX3D Enhanced Reporting System")
    
    test_data = {
        "header": {
            "organization_id": 999,
            "company_name": "Test Company Inc.",
            "statement_title": "TEST INCOME STATEMENT",
            "reporting_date": "2023-12-31",
            "period_start": "2023-10-01",
            "period_end": "2023-12-31",
            "currency": "USD"
        },
        "total_revenue": 100000.00,
        "cost_of_goods_sold": 40000.00,
        "gross_profit": 60000.00,
        "operating_expenses": 35000.00,
        "operating_income": 25000.00,
        "net_income": 20000.00
    }
    
    output_path = ensure_output_directory(f"{output_dir}/test_report.pdf")
    
    try:
        generator = create_enhanced_generator()
        
        result = generator.generate_income_statement(
            income_statement_data=test_data,
            output_path=output_path,
            output_format="pdf"
        )
        
        if result.get('success'):
            display_success_message(
                "System test completed successfully",
                details={
                    'test_report_path': result['output_path'],
                    'format': result['format'],
                    'file_size_mb': result.get('file_size_mb', 0)
                }
            )
            
            # Show system capabilities
            click.echo("\n✅ System Capabilities Verified:")
            click.echo("   • Enhanced error handling and recovery")
            click.echo("   • Graceful degradation with format fallbacks")
            click.echo("   • Resource monitoring and optimization")
            click.echo("   • Comprehensive validation and logging")
            
        else:
            click.echo("❌ System test failed", err=True)
            
    except Exception as e:
        raise ValidationError(f"System test failed: {e}", field_name="system_test")


# Add the enhanced reports to the main CLI
if __name__ == '__main__':
    generate()