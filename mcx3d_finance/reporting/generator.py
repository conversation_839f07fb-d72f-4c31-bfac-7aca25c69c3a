"""
Enhanced report generator with PDF and Excel output capabilities.
"""

import logging
import base64
import io
from typing import Dict, Any, List, Optional, Union

# PDF generation
from reportlab.lib import colors
from reportlab.lib.pagesizes import letter
from reportlab.platypus import (
    SimpleDocTemplate,
    Table,
    TableStyle,
    Paragraph,
    Spacer,
    PageBreak,
    Image,
)
from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
from reportlab.lib.units import inch
from reportlab.lib.enums import TA_CENTER, TA_RIGHT

# Excel generation
from openpyxl import Workbook
from openpyxl.styles import Font, Alignment
from openpyxl.chart import (
    <PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON>,
    <PERSON>att<PERSON><PERSON><PERSON>,
    Reference,
    Series,
)
from openpyxl.chart.axis import DateAxis
from openpyxl.chart.layout import Layout, ManualLayout

# Chart generation
import plotly.graph_objects as go
import plotly.express as px
from plotly.subplots import make_subplots
import pandas as pd
import numpy as np

logger = logging.getLogger(__name__)


class ReportGenerator:
    """Enhanced report generator with multiple output formats."""

    def __init__(self):
        self.styles = getSampleStyleSheet()
        self._setup_custom_styles()
        self.chart_generator = FinancialChartGenerator()

    def _adjust_column_widths(self, ws, max_width: int = 50):
        """Helper method to safely adjust column widths, handling merged cells."""
        for column in ws.columns:
            max_length = 0
            # Handle merged cells by checking if the first cell has column_letter attribute
            if hasattr(column[0], 'column_letter'):
                column_letter = column[0].column_letter
                for cell in column:
                    try:
                        if hasattr(cell, 'value') and cell.value is not None:
                            if len(str(cell.value)) > max_length:
                                max_length = len(str(cell.value))
                    except (TypeError, ValueError):
                        pass
                adjusted_width = min(max_length + 2, max_width)
                ws.column_dimensions[column_letter].width = adjusted_width

    def _setup_custom_styles(self):
        """Setup custom styles for PDF generation."""
        # Company header style
        self.styles.add(
            ParagraphStyle(
                name="CompanyHeader",
                parent=self.styles["Heading1"],
                fontSize=16,
                spaceAfter=6,
                alignment=TA_CENTER,
                textColor=colors.black,
            )
        )

        # Statement title style
        self.styles.add(
            ParagraphStyle(
                name="StatementTitle",
                parent=self.styles["Heading2"],
                fontSize=14,
                spaceAfter=12,
                alignment=TA_CENTER,
                textColor=colors.black,
            )
        )

        # Section header style
        self.styles.add(
            ParagraphStyle(
                name="SectionHeader",
                parent=self.styles["Heading3"],
                fontSize=12,
                spaceBefore=12,
                spaceAfter=6,
                textColor=colors.black,
            )
        )

        # Financial data style
        self.styles.add(
            ParagraphStyle(
                name="FinancialData",
                parent=self.styles["Normal"],
                fontSize=10,
                alignment=TA_RIGHT,
            )
        )

    def generate_balance_sheet_pdf(
        self, balance_sheet_data: Dict[str, Any], output_path: str
    ):
        """Generate NASDAQ-compliant balance sheet PDF."""
        try:
            logger.info(f"Generating balance sheet PDF: {output_path}")

            doc = SimpleDocTemplate(
                output_path,
                pagesize=letter,
                rightMargin=72,
                leftMargin=72,
                topMargin=72,
                bottomMargin=18,
            )

            story = []

            # Header information
            header = balance_sheet_data.get("header", {})
            story.append(
                Paragraph(
                    header.get("company_name", "Company Name"),
                    self.styles["CompanyHeader"],
                )
            )
            story.append(
                Paragraph(
                    header.get("statement_title", "BALANCE SHEET"),
                    self.styles["StatementTitle"],
                )
            )
            story.append(
                Paragraph(
                    f"As of {header.get('reporting_date', 'Date')}",
                    self.styles["Normal"],
                )
            )

            if header.get("comparative_date"):
                story.append(
                    Paragraph(
                        f"(With comparative figures as of {header.get('comparative_date')})",
                        self.styles["Normal"],
                    )
                )

            story.append(
                Paragraph(
                    f"(All amounts in {header.get('amounts_in', 'thousands')} of {header.get('currency', 'USD')})",
                    self.styles["Normal"],
                )
            )
            story.append(Spacer(1, 20))

            # Assets section
            story.append(Paragraph("ASSETS", self.styles["SectionHeader"]))
            assets_table_data = self._build_balance_sheet_assets_table(
                balance_sheet_data.get("assets", {})
            )
            assets_table = self._create_financial_table(assets_table_data)
            story.append(assets_table)
            story.append(Spacer(1, 20))

            # Liabilities and Equity section
            story.append(
                Paragraph(
                    "LIABILITIES AND STOCKHOLDERS' EQUITY", self.styles["SectionHeader"]
                )
            )
            liab_equity_table_data = self._build_balance_sheet_liabilities_table(
                balance_sheet_data.get("liabilities_and_equity", {})
            )
            liab_equity_table = self._create_financial_table(liab_equity_table_data)
            story.append(liab_equity_table)
            story.append(Spacer(1, 20))

            # Financial analysis
            if "financial_analysis" in balance_sheet_data:
                story.append(PageBreak())
                story.append(
                    Paragraph("FINANCIAL ANALYSIS", self.styles["SectionHeader"])
                )

                # Add Asset Composition Chart
                asset_chart = self.chart_generator.create_balance_sheet_asset_composition_chart(balance_sheet_data)
                asset_chart_img = self.chart_generator._create_reportlab_image(asset_chart)
                if asset_chart_img:
                    story.append(asset_chart_img)
                    story.append(Spacer(1, 20))

                # Add Working Capital Components Chart
                wc_chart = self.chart_generator.create_working_capital_components_chart(balance_sheet_data)
                wc_chart_img = self.chart_generator._create_reportlab_image(wc_chart)
                if wc_chart_img:
                    story.append(wc_chart_img)
                    story.append(Spacer(1, 20))

                analysis_table_data = self._build_financial_analysis_table(
                    balance_sheet_data["financial_analysis"]
                )
                analysis_table = self._create_financial_table(analysis_table_data)
                story.append(analysis_table)

            # Compliance certifications
            if "compliance" in balance_sheet_data:
                story.append(Spacer(1, 20))
                story.append(
                    Paragraph("COMPLIANCE CERTIFICATIONS", self.styles["SectionHeader"])
                )
                for cert in balance_sheet_data["compliance"].get("certifications", []):
                    story.append(Paragraph(f"• {cert}", self.styles["Normal"]))
                    story.append(Spacer(1, 6))

            doc.build(story)
            logger.info("Balance sheet PDF generated successfully")

        except Exception as e:
            logger.error(f"Error generating balance sheet PDF: {e}")
            raise

    def generate_income_statement_pdf(
        self, income_statement_data: Dict[str, Any], output_path: str
    ):
        """Generate NASDAQ-compliant income statement PDF."""
        try:
            logger.info(f"Generating income statement PDF: {output_path}")

            doc = SimpleDocTemplate(
                output_path,
                pagesize=letter,
                rightMargin=72,
                leftMargin=72,
                topMargin=72,
                bottomMargin=18,
            )

            story = []

            # Header information
            header = income_statement_data.get("header", {})
            story.append(
                Paragraph(
                    header.get("company_name", "Company Name"),
                    self.styles["CompanyHeader"],
                )
            )
            story.append(
                Paragraph(
                    header.get("statement_title", "INCOME STATEMENT"),
                    self.styles["StatementTitle"],
                )
            )
            story.append(
                Paragraph(
                    f"For the {header.get('period_description', 'Period')}",
                    self.styles["Normal"],
                )
            )

            if header.get("comparative_period"):
                story.append(
                    Paragraph(
                        f"(With comparative figures for the {header.get('comparative_period')})",
                        self.styles["Normal"],
                    )
                )

            story.append(
                Paragraph(
                    f"(All amounts in {header.get('amounts_in', 'thousands')} of {header.get('currency', 'USD')}, except per share data)",
                    self.styles["Normal"],
                )
            )
            story.append(Spacer(1, 20))

            # Revenue section
            story.append(Paragraph("REVENUE", self.styles["SectionHeader"]))
            revenue_table_data = self._build_income_statement_revenue_table(
                income_statement_data.get("revenue", {})
            )
            revenue_table = self._create_financial_table(revenue_table_data)
            story.append(revenue_table)
            story.append(Spacer(1, 12))

            # Cost of revenue and gross profit
            cost_table_data = self._build_income_statement_cost_table(
                income_statement_data.get("cost_of_revenue", {}),
                income_statement_data.get("gross_profit", {}),
            )
            cost_table = self._create_financial_table(cost_table_data)
            story.append(cost_table)
            story.append(Spacer(1, 12))

            # Operating expenses and operating income
            opex_table_data = self._build_income_statement_opex_table(
                income_statement_data.get("operating_expenses", {}),
                income_statement_data.get("operating_income", {}),
            )
            opex_table = self._create_financial_table(opex_table_data)
            story.append(opex_table)
            story.append(Spacer(1, 12))

            # Non-operating and net income
            final_table_data = self._build_income_statement_final_table(
                income_statement_data.get("non_operating_income_expense", {}),
                income_statement_data.get("income_before_taxes", {}),
                income_statement_data.get("income_tax_expense", {}),
                income_statement_data.get("net_income", {}),
            )
            final_table = self._create_financial_table(final_table_data)
            story.append(final_table)
            story.append(Spacer(1, 20))

            # Earnings per share
            if "earnings_per_share" in income_statement_data:
                eps_table_data = self._build_eps_table(
                    income_statement_data["earnings_per_share"]
                )
                eps_table = self._create_financial_table(eps_table_data)
                story.append(eps_table)
                story.append(Spacer(1, 20))

            # Financial analysis
            if "financial_analysis" in income_statement_data:
                story.append(PageBreak())
                story.append(
                    Paragraph("FINANCIAL ANALYSIS", self.styles["SectionHeader"])
                )

                # Add Revenue Breakdown Chart
                revenue_chart = self.chart_generator.create_revenue_breakdown_chart(income_statement_data)
                revenue_chart_img = self.chart_generator._create_reportlab_image(revenue_chart)
                if revenue_chart_img:
                    story.append(revenue_chart_img)
                    story.append(Spacer(1, 20))

                # Add Margin Analysis Chart if historical data is available
                if "historical_data" in income_statement_data:
                    margin_chart = self.chart_generator.create_margin_analysis_chart(
                        income_statement_data["historical_data"]
                    )
                    margin_chart_img = self.chart_generator._create_reportlab_image(margin_chart)
                    if margin_chart_img:
                        story.append(margin_chart_img)
                        story.append(Spacer(1, 20))

                analysis_table_data = self._build_income_analysis_table(
                    income_statement_data["financial_analysis"]
                )
                analysis_table = self._create_financial_table(analysis_table_data)
                story.append(analysis_table)

            doc.build(story)
            logger.info("Income statement PDF generated successfully")

        except Exception as e:
            logger.error(f"Error generating income statement PDF: {e}")
            raise

    def generate_balance_sheet_excel(
        self, balance_sheet_data: Dict[str, Any], output_path: str
    ):
        """Generate NASDAQ-compliant balance sheet Excel file."""
        try:
            logger.info(f"Generating balance sheet Excel: {output_path}")

            wb = Workbook()
            ws = wb.active
            ws.title = "Balance Sheet"

            # Setup styles
            header_font = Font(bold=True, size=14)
            section_font = Font(bold=True, size=12)
            currency_format = "#,##0"

            row = 1

            # Header information
            header = balance_sheet_data.get("header", {})
            ws.merge_cells(f"A{row}:D{row}")
            ws[f"A{row}"] = header.get("company_name", "Company Name")
            ws[f"A{row}"].font = header_font
            ws[f"A{row}"].alignment = Alignment(horizontal="center")
            row += 1

            ws.merge_cells(f"A{row}:D{row}")
            ws[f"A{row}"] = header.get("statement_title", "BALANCE SHEET")
            ws[f"A{row}"].font = section_font
            ws[f"A{row}"].alignment = Alignment(horizontal="center")
            row += 1

            ws.merge_cells(f"A{row}:D{row}")
            ws[f"A{row}"] = f"As of {header.get('reporting_date', 'Date')}"
            ws[f"A{row}"].alignment = Alignment(horizontal="center")
            row += 1

            if header.get("comparative_date"):
                ws.merge_cells(f"A{row}:D{row}")
                ws[f"A{row}"] = (
                    f"(With comparative figures as of {header.get('comparative_date')})"
                )
                ws[f"A{row}"].alignment = Alignment(horizontal="center")
                row += 1

            ws.merge_cells(f"A{row}:D{row}")
            ws[f"A{row}"] = (
                f"(All amounts in {header.get('amounts_in', 'thousands')} of {header.get('currency', 'USD')})"
            )
            ws[f"A{row}"].alignment = Alignment(horizontal="center")
            row += 2

            # Column headers
            ws["A" + str(row)] = "Account"
            ws["B" + str(row)] = header.get("reporting_date", "Current")
            if header.get("comparative_date"):
                ws["C" + str(row)] = header.get("comparative_date", "Comparative")

            for col in ["A", "B", "C"]:
                ws[col + str(row)].font = section_font
            row += 1

            # Assets section
            row = self._add_excel_balance_sheet_section(
                ws, row, "ASSETS", balance_sheet_data.get("assets", {}), currency_format
            )
            row += 1

            # Liabilities and Equity section
            row = self._add_excel_balance_sheet_section(
                ws,
                row,
                "LIABILITIES AND STOCKHOLDERS' EQUITY",
                balance_sheet_data.get("liabilities_and_equity", {}),
                currency_format,
            )

            # Auto-adjust column widths
            for column in ws.columns:
                max_length = 0
                # Handle merged cells by checking if the first cell has column_letter attribute
                if hasattr(column[0], 'column_letter'):
                    column_letter = column[0].column_letter
                    for cell in column:
                        try:
                            if hasattr(cell, 'value') and len(str(cell.value)) > max_length:
                                max_length = len(str(cell.value))
                        except (TypeError, ValueError):
                            pass
                    adjusted_width = min(max_length + 2, 50)
                    ws.column_dimensions[column_letter].width = adjusted_width

            # Add financial analysis sheet
            if "financial_analysis" in balance_sheet_data:
                analysis_ws = wb.create_sheet("Financial Analysis")
                self._add_excel_financial_analysis(
                    analysis_ws, balance_sheet_data["financial_analysis"]
                )

            wb.save(output_path)
            logger.info("Balance sheet Excel generated successfully")

        except Exception as e:
            logger.error(f"Error generating balance sheet Excel: {e}")
            raise

    def generate_income_statement_excel(
        self, income_statement_data: Dict[str, Any], output_path: str
    ):
        """Generate NASDAQ-compliant income statement Excel file."""
        try:
            logger.info(f"Generating income statement Excel: {output_path}")

            wb = Workbook()
            ws = wb.active
            ws.title = "Income Statement"

            # Setup styles
            header_font = Font(bold=True, size=14)
            section_font = Font(bold=True, size=12)
            currency_format = "#,##0"

            row = 1

            # Header information
            header = income_statement_data.get("header", {})
            ws.merge_cells(f"A{row}:D{row}")
            ws[f"A{row}"] = header.get("company_name", "Company Name")
            ws[f"A{row}"].font = header_font
            ws[f"A{row}"].alignment = Alignment(horizontal="center")
            row += 1

            ws.merge_cells(f"A{row}:D{row}")
            ws[f"A{row}"] = header.get("statement_title", "INCOME STATEMENT")
            ws[f"A{row}"].font = section_font
            ws[f"A{row}"].alignment = Alignment(horizontal="center")
            row += 1

            ws.merge_cells(f"A{row}:D{row}")
            ws[f"A{row}"] = f"For the {header.get('period_description', 'Period')}"
            ws[f"A{row}"].alignment = Alignment(horizontal="center")
            row += 1

            if header.get("comparative_period"):
                ws.merge_cells(f"A{row}:D{row}")
                ws[f"A{row}"] = (
                    f"(With comparative figures for the {header.get('comparative_period')})"
                )
                ws[f"A{row}"].alignment = Alignment(horizontal="center")
                row += 1

            ws.merge_cells(f"A{row}:D{row}")
            ws[f"A{row}"] = (
                f"(All amounts in {header.get('amounts_in', 'thousands')} of {header.get('currency', 'USD')}, except per share data)"
            )
            ws[f"A{row}"].alignment = Alignment(horizontal="center")
            row += 2

            # Column headers
            ws["A" + str(row)] = "Account"
            ws["B" + str(row)] = "Current Period"
            if header.get("comparative_period"):
                ws["C" + str(row)] = "Comparative Period"

            for col in ["A", "B", "C"]:
                ws[col + str(row)].font = section_font
            row += 1

            # Add income statement sections
            sections = [
                ("REVENUE", income_statement_data.get("revenue", {})),
                ("COST OF REVENUE", income_statement_data.get("cost_of_revenue", {})),
                (
                    "GROSS PROFIT",
                    {"gross_profit": income_statement_data.get("gross_profit", {})},
                ),
                (
                    "OPERATING EXPENSES",
                    income_statement_data.get("operating_expenses", {}),
                ),
                (
                    "OPERATING INCOME",
                    {
                        "operating_income": income_statement_data.get(
                            "operating_income", {}
                        )
                    },
                ),
                (
                    "NON-OPERATING INCOME (EXPENSE)",
                    income_statement_data.get("non_operating_income_expense", {}),
                ),
                (
                    "INCOME BEFORE TAXES",
                    {
                        "income_before_taxes": income_statement_data.get(
                            "income_before_taxes", {}
                        )
                    },
                ),
                (
                    "INCOME TAX EXPENSE",
                    {
                        "income_tax_expense": income_statement_data.get(
                            "income_tax_expense", {}
                        )
                    },
                ),
                (
                    "NET INCOME",
                    {"net_income": income_statement_data.get("net_income", {})},
                ),
            ]

            for section_name, section_data in sections:
                row = self._add_excel_income_statement_section(
                    ws, row, section_name, section_data, currency_format
                )
                row += 1

            # Add EPS section
            if "earnings_per_share" in income_statement_data:
                row = self._add_excel_eps_section(
                    ws, row, income_statement_data["earnings_per_share"]
                )

            # Auto-adjust column widths
            for column in ws.columns:
                max_length = 0
                # Handle merged cells by checking if the first cell has column_letter attribute
                if hasattr(column[0], 'column_letter'):
                    column_letter = column[0].column_letter
                    for cell in column:
                        try:
                            if hasattr(cell, 'value') and len(str(cell.value)) > max_length:
                                max_length = len(str(cell.value))
                        except (TypeError, ValueError):
                            pass
                    adjusted_width = min(max_length + 2, 50)
                    ws.column_dimensions[column_letter].width = adjusted_width

            # Add financial analysis sheet
            if "financial_analysis" in income_statement_data:
                analysis_ws = wb.create_sheet("Financial Analysis")
                self._add_excel_income_analysis(
                    analysis_ws, income_statement_data["financial_analysis"]
                )

            wb.save(output_path)
            logger.info("Income statement Excel generated successfully")

        except Exception as e:
            logger.error(f"Error generating income statement Excel: {e}")
            raise

    # Helper methods for PDF table building
    def _build_balance_sheet_assets_table(
        self, assets_data: Dict[str, Any]
    ) -> List[List[str]]:
        """Build assets table data for PDF."""
        table_data = []

        # Current assets
        current_assets = assets_data.get("current_assets", {})
        table_data.append(["CURRENT ASSETS:", "", ""])

        for key, value in current_assets.items():
            if key != "title" and key != "total_current_assets":
                label = key.replace("_", " ").title()
                current_val = self._format_currency(value.get("current", 0))
                comp_val = (
                    self._format_currency(value.get("comparative", 0))
                    if value.get("comparative") is not None
                    else ""
                )
                table_data.append([f"  {label}", current_val, comp_val])

        # Total current assets
        total_current = current_assets.get("total_current_assets", {})
        table_data.append(
            [
                "  Total current assets",
                self._format_currency(total_current.get("current", 0)),
                (
                    self._format_currency(total_current.get("comparative", 0))
                    if total_current.get("comparative") is not None
                    else ""
                ),
            ]
        )

        table_data.append(["", "", ""])  # Spacer

        # Non-current assets
        non_current_assets = assets_data.get("non_current_assets", {})
        table_data.append(["NON-CURRENT ASSETS:", "", ""])

        for key, value in non_current_assets.items():
            if key != "title" and key != "total_non_current_assets":
                label = key.replace("_", " ").title()
                current_val = self._format_currency(value.get("current", 0))
                comp_val = (
                    self._format_currency(value.get("comparative", 0))
                    if value.get("comparative") is not None
                    else ""
                )
                table_data.append([f"  {label}", current_val, comp_val])

        # Total non-current assets
        total_non_current = non_current_assets.get("total_non_current_assets", {})
        table_data.append(
            [
                "  Total non-current assets",
                self._format_currency(total_non_current.get("current", 0)),
                (
                    self._format_currency(total_non_current.get("comparative", 0))
                    if total_non_current.get("comparative") is not None
                    else ""
                ),
            ]
        )

        table_data.append(["", "", ""])  # Spacer

        # Total assets
        total_assets = assets_data.get("total_assets", {})
        table_data.append(
            [
                "TOTAL ASSETS",
                self._format_currency(total_assets.get("current", 0)),
                (
                    self._format_currency(total_assets.get("comparative", 0))
                    if total_assets.get("comparative") is not None
                    else ""
                ),
            ]
        )

        return table_data

    def _build_balance_sheet_liabilities_table(
        self, liab_equity_data: Dict[str, Any]
    ) -> List[List[str]]:
        """Build liabilities and equity table data for PDF."""
        table_data = []

        # Current liabilities
        current_liabilities = liab_equity_data.get("current_liabilities", {})
        table_data.append(["CURRENT LIABILITIES:", "", ""])

        for key, value in current_liabilities.items():
            if key != "title" and key != "total_current_liabilities":
                label = key.replace("_", " ").title()
                current_val = self._format_currency(value.get("current", 0))
                comp_val = (
                    self._format_currency(value.get("comparative", 0))
                    if value.get("comparative") is not None
                    else ""
                )
                table_data.append([f"  {label}", current_val, comp_val])

        # Total current liabilities
        total_current_liab = current_liabilities.get("total_current_liabilities", {})
        table_data.append(
            [
                "  Total current liabilities",
                self._format_currency(total_current_liab.get("current", 0)),
                (
                    self._format_currency(total_current_liab.get("comparative", 0))
                    if total_current_liab.get("comparative") is not None
                    else ""
                ),
            ]
        )

        table_data.append(["", "", ""])  # Spacer

        # Non-current liabilities
        non_current_liabilities = liab_equity_data.get("non_current_liabilities", {})
        table_data.append(["NON-CURRENT LIABILITIES:", "", ""])

        for key, value in non_current_liabilities.items():
            if key != "title" and key != "total_non_current_liabilities":
                label = key.replace("_", " ").title()
                current_val = self._format_currency(value.get("current", 0))
                comp_val = (
                    self._format_currency(value.get("comparative", 0))
                    if value.get("comparative") is not None
                    else ""
                )
                table_data.append([f"  {label}", current_val, comp_val])

        # Total non-current liabilities
        total_non_current_liab = non_current_liabilities.get(
            "total_non_current_liabilities", {}
        )
        table_data.append(
            [
                "  Total non-current liabilities",
                self._format_currency(total_non_current_liab.get("current", 0)),
                (
                    self._format_currency(total_non_current_liab.get("comparative", 0))
                    if total_non_current_liab.get("comparative") is not None
                    else ""
                ),
            ]
        )

        # Total liabilities
        total_liabilities = liab_equity_data.get("total_liabilities", {})
        table_data.append(
            [
                "  Total liabilities",
                self._format_currency(total_liabilities.get("current", 0)),
                (
                    self._format_currency(total_liabilities.get("comparative", 0))
                    if total_liabilities.get("comparative") is not None
                    else ""
                ),
            ]
        )

        table_data.append(["", "", ""])  # Spacer

        # Stockholders' equity
        stockholders_equity = liab_equity_data.get("stockholders_equity", {})
        table_data.append(["STOCKHOLDERS' EQUITY:", "", ""])

        for key, value in stockholders_equity.items():
            if key != "title" and key != "total_stockholders_equity":
                label = key.replace("_", " ").title()
                current_val = self._format_currency(value.get("current", 0))
                comp_val = (
                    self._format_currency(value.get("comparative", 0))
                    if value.get("comparative") is not None
                    else ""
                )
                table_data.append([f"  {label}", current_val, comp_val])

        # Total stockholders' equity
        total_equity = stockholders_equity.get("total_stockholders_equity", {})
        table_data.append(
            [
                "  Total stockholders' equity",
                self._format_currency(total_equity.get("current", 0)),
                (
                    self._format_currency(total_equity.get("comparative", 0))
                    if total_equity.get("comparative") is not None
                    else ""
                ),
            ]
        )

        # Total liabilities and equity
        total_liab_equity = liab_equity_data.get("total_liabilities_and_equity", {})
        table_data.append(
            [
                "TOTAL LIABILITIES AND EQUITY",
                self._format_currency(total_liab_equity.get("current", 0)),
                (
                    self._format_currency(total_liab_equity.get("comparative", 0))
                    if total_liab_equity.get("comparative") is not None
                    else ""
                ),
            ]
        )

        return table_data

    def _create_financial_table(self, table_data: List[List[str]]) -> Table:
        """Create a formatted financial table for PDF."""
        table = Table(table_data, colWidths=[4 * inch, 1.5 * inch, 1.5 * inch])

        table.setStyle(
            TableStyle(
                [
                    ("ALIGN", (0, 0), (-1, -1), "LEFT"),
                    ("ALIGN", (1, 0), (-1, -1), "RIGHT"),
                    ("FONTNAME", (0, 0), (-1, -1), "Helvetica"),
                    ("FONTSIZE", (0, 0), (-1, -1), 10),
                    ("GRID", (0, 0), (-1, -1), 0.5, colors.black),
                    ("VALIGN", (0, 0), (-1, -1), "MIDDLE"),
                ]
            )
        )

        return table

    def _format_currency(self, amount: float) -> str:
        """Format currency amount for display."""
        if amount == 0:
            return "-"
        return f"{amount:,}"

    def _build_income_statement_revenue_table(self, revenue_data: Dict[str, Any]) -> List[List[str]]:
        """Build revenue table data for income statement PDF."""
        table_data = []
        for key, value in revenue_data.items():
            if key != "title":
                label = key.replace("_", " ").title()
                current_val = self._format_currency(value.get("current", 0))
                comp_val = (
                    self._format_currency(value.get("comparative", 0))
                    if value.get("comparative") is not None
                    else ""
                )
                table_data.append([f"  {label}", current_val, comp_val])
        return table_data

    def _build_income_statement_cost_table(self, cost_data: Dict[str, Any], gross_profit_data: Dict[str, Any]) -> List[List[str]]:
        """Build cost and gross profit table data."""
        table_data = []
        
        # Cost of revenue items
        for key, value in cost_data.items():
            if key != "title":
                label = key.replace("_", " ").title()
                current_val = self._format_currency(value.get("current", 0))
                comp_val = (
                    self._format_currency(value.get("comparative", 0))
                    if value.get("comparative") is not None
                    else ""
                )
                table_data.append([f"  {label}", current_val, comp_val])
        
        # Gross profit
        table_data.append(["", "", ""])  # Spacer
        table_data.append([
            "GROSS PROFIT",
            self._format_currency(gross_profit_data.get("current", 0)),
            (
                self._format_currency(gross_profit_data.get("comparative", 0))
                if gross_profit_data.get("comparative") is not None
                else ""
            )
        ])
        
        return table_data

    def _build_income_statement_opex_table(self, opex_data: Dict[str, Any], operating_income_data: Dict[str, Any]) -> List[List[str]]:
        """Build operating expenses and operating income table."""
        table_data = []
        
        # Operating expenses
        table_data.append(["OPERATING EXPENSES:", "", ""])
        for key, value in opex_data.items():
            if key != "title":
                label = key.replace("_", " ").title()
                current_val = self._format_currency(value.get("current", 0))
                comp_val = (
                    self._format_currency(value.get("comparative", 0))
                    if value.get("comparative") is not None
                    else ""
                )
                table_data.append([f"  {label}", current_val, comp_val])
        
        # Operating income
        table_data.append(["", "", ""])  # Spacer
        table_data.append([
            "OPERATING INCOME",
            self._format_currency(operating_income_data.get("current", 0)),
            (
                self._format_currency(operating_income_data.get("comparative", 0))
                if operating_income_data.get("comparative") is not None
                else ""
            )
        ])
        
        return table_data

    def _build_income_statement_final_table(self, non_op_data: Dict[str, Any], 
                                          income_before_tax_data: Dict[str, Any],
                                          tax_data: Dict[str, Any], 
                                          net_income_data: Dict[str, Any]) -> List[List[str]]:
        """Build final sections of income statement."""
        table_data = []
        
        # Non-operating income/expense
        if non_op_data:
            table_data.append(["NON-OPERATING INCOME (EXPENSE):", "", ""])
            for key, value in non_op_data.items():
                if key != "title":
                    label = key.replace("_", " ").title()
                    current_val = self._format_currency(value.get("current", 0))
                    comp_val = (
                        self._format_currency(value.get("comparative", 0))
                        if value.get("comparative") is not None
                        else ""
                    )
                    table_data.append([f"  {label}", current_val, comp_val])
            
            table_data.append(["", "", ""])  # Spacer
        
        # Income before taxes
        table_data.append([
            "INCOME BEFORE TAXES",
            self._format_currency(income_before_tax_data.get("current", 0)),
            (
                self._format_currency(income_before_tax_data.get("comparative", 0))
                if income_before_tax_data.get("comparative") is not None
                else ""
            )
        ])
        
        # Tax expense
        table_data.append([
            "Income tax expense",
            self._format_currency(tax_data.get("current", 0)),
            (
                self._format_currency(tax_data.get("comparative", 0))
                if tax_data.get("comparative") is not None
                else ""
            )
        ])
        
        # Net income
        table_data.append(["", "", ""])  # Spacer
        table_data.append([
            "NET INCOME",
            self._format_currency(net_income_data.get("current", 0)),
            (
                self._format_currency(net_income_data.get("comparative", 0))
                if net_income_data.get("comparative") is not None
                else ""
            )
        ])
        
        return table_data

    def _build_eps_table(self, eps_data: Dict[str, Any]) -> List[List[str]]:
        """Build earnings per share table."""
        table_data = []
        table_data.append(["EARNINGS PER SHARE:", "", ""])
        
        for key, value in eps_data.items():
            label = key.replace("_", " ").title()
            current_val = f"${value.get('current', 0):.2f}"
            comp_val = f"${value.get('comparative', 0):.2f}" if value.get('comparative') is not None else ""
            table_data.append([f"  {label}", current_val, comp_val])
        
        return table_data

    def _build_financial_analysis_table(self, analysis_data: Dict[str, Any]) -> List[List[str]]:
        """Build financial analysis table for PDFs."""
        table_data = []
        
        if "ratios" in analysis_data:
            table_data.append(["FINANCIAL RATIOS:", "", ""])
            ratios = analysis_data["ratios"]
            
            for category, ratio_data in ratios.items():
                category_label = category.replace("_", " ").title()
                table_data.append([f"{category_label}:", "", ""])
                
                for ratio_name, ratio_value in ratio_data.items():
                    ratio_label = ratio_name.replace("_", " ").title()
                    if isinstance(ratio_value, dict):
                        current_val = f"{ratio_value.get('current', 0):.2f}"
                        comp_val = f"{ratio_value.get('comparative', 0):.2f}" if ratio_value.get('comparative') is not None else ""
                        table_data.append([f"  {ratio_label}", current_val, comp_val])
                    else:
                        table_data.append([f"  {ratio_label}", f"{ratio_value:.2f}", ""])
                
                table_data.append(["", "", ""])  # Spacer
        
        return table_data

    def _build_income_analysis_table(self, analysis_data: Dict[str, Any]) -> List[List[str]]:
        """Build income statement analysis table."""
        table_data = []
        
        # Profitability metrics
        if "profitability_metrics" in analysis_data:
            table_data.append(["PROFITABILITY METRICS:", "", ""])
            metrics = analysis_data["profitability_metrics"]
            
            for metric_name, metric_value in metrics.items():
                metric_label = metric_name.replace("_", " ").title()
                if isinstance(metric_value, dict):
                    current_val = f"{metric_value.get('current', 0)*100:.1f}%"
                    comp_val = f"{metric_value.get('comparative', 0)*100:.1f}%" if metric_value.get('comparative') is not None else ""
                    table_data.append([f"  {metric_label}", current_val, comp_val])
                else:
                    table_data.append([f"  {metric_label}", f"{metric_value*100:.1f}%", ""])
        
        return table_data

    # Excel helper methods
    def _add_excel_balance_sheet_section(self, ws, start_row: int, section_name: str, 
                                       section_data: Dict[str, Any], currency_format: str) -> int:
        """Add a balance sheet section to Excel worksheet."""
        row = start_row
        
        # Section header
        ws[f"A{row}"] = section_name
        ws[f"A{row}"].font = Font(bold=True)
        row += 1
        
        # Add items
        for key, value in section_data.items():
            if key not in ["title", f"total_{section_name.lower().replace(' ', '_')}"]:
                label = key.replace("_", " ").title()
                ws[f"A{row}"] = f"  {label}"
                ws[f"B{row}"] = value.get("current", 0)
                ws[f"B{row}"].number_format = currency_format
                
                if value.get("comparative") is not None:
                    ws[f"C{row}"] = value.get("comparative", 0)
                    ws[f"C{row}"].number_format = currency_format
                
                row += 1
        
        # Add total if present
        total_key = f"total_{section_name.lower().replace(' ', '_').replace('and_', '').replace('stockholders_', '')}"
        if total_key in section_data or "total_assets" in section_data or "total_liabilities_and_equity" in section_data:
            total_data = section_data.get(total_key) or section_data.get("total_assets") or section_data.get("total_liabilities_and_equity")
            if total_data:
                ws[f"A{row}"] = f"  Total {section_name.lower()}"
                ws[f"A{row}"].font = Font(bold=True)
                ws[f"B{row}"] = total_data.get("current", 0)
                ws[f"B{row}"].number_format = currency_format
                ws[f"B{row}"].font = Font(bold=True)
                
                if total_data.get("comparative") is not None:
                    ws[f"C{row}"] = total_data.get("comparative", 0)
                    ws[f"C{row}"].number_format = currency_format
                    ws[f"C{row}"].font = Font(bold=True)
                
                row += 1
        
        return row + 1  # Add extra spacing

    def _add_excel_income_statement_section(self, ws, start_row: int, section_name: str, 
                                          section_data: Dict[str, Any], currency_format: str) -> int:
        """Add income statement section to Excel worksheet."""
        row = start_row
        
        # Section header
        ws[f"A{row}"] = section_name
        ws[f"A{row}"].font = Font(bold=True)
        row += 1
        
        # Add items
        for key, value in section_data.items():
            label = key.replace("_", " ").title()
            ws[f"A{row}"] = f"  {label}"
            
            if isinstance(value, dict):
                ws[f"B{row}"] = value.get("current", 0)
                ws[f"B{row}"].number_format = currency_format
                
                if value.get("comparative") is not None:
                    ws[f"C{row}"] = value.get("comparative", 0)
                    ws[f"C{row}"].number_format = currency_format
            else:
                ws[f"B{row}"] = value
                ws[f"B{row}"].number_format = currency_format
            
            row += 1
        
        return row + 1  # Add extra spacing

    def _add_excel_eps_section(self, ws, start_row: int, eps_data: Dict[str, Any]) -> int:
        """Add earnings per share section to Excel."""
        row = start_row
        
        ws[f"A{row}"] = "EARNINGS PER SHARE"
        ws[f"A{row}"].font = Font(bold=True)
        row += 1
        
        for key, value in eps_data.items():
            label = key.replace("_", " ").title()
            ws[f"A{row}"] = f"  {label}"
            ws[f"B{row}"] = value.get("current", 0)
            ws[f"B{row}"].number_format = "$0.00"
            
            if value.get("comparative") is not None:
                ws[f"C{row}"] = value.get("comparative", 0)
                ws[f"C{row}"].number_format = "$0.00"
            
            row += 1
        
        return row + 1

    def _add_excel_financial_analysis(self, ws, analysis_data: Dict[str, Any]):
        """Add financial analysis to Excel worksheet."""
        row = 1
        
        # Header
        ws.merge_cells(f"A{row}:C{row}")
        ws[f"A{row}"] = "FINANCIAL ANALYSIS"
        ws[f"A{row}"].font = Font(bold=True, size=14)
        ws[f"A{row}"].alignment = Alignment(horizontal="center")
        row += 2
        
        # Column headers
        ws["A" + str(row)] = "Metric"
        ws["B" + str(row)] = "Current"
        ws["C" + str(row)] = "Comparative"
        
        for col in ["A", "B", "C"]:
            ws[col + str(row)].font = Font(bold=True)
        row += 1
        
        # Add analysis data
        if "ratios" in analysis_data:
            for category, ratios in analysis_data["ratios"].items():
                category_label = category.replace("_", " ").title()
                ws[f"A{row}"] = f"{category_label}:"
                ws[f"A{row}"].font = Font(bold=True)
                row += 1
                
                for ratio_name, ratio_value in ratios.items():
                    ratio_label = ratio_name.replace("_", " ").title()
                    ws[f"A{row}"] = f"  {ratio_label}"
                    
                    if isinstance(ratio_value, dict):
                        ws[f"B{row}"] = ratio_value.get("current", 0)
                        if ratio_value.get("comparative") is not None:
                            ws[f"C{row}"] = ratio_value.get("comparative", 0)
                    else:
                        ws[f"B{row}"] = ratio_value
                    
                    row += 1
                
                row += 1  # Extra spacing between categories

    def _add_excel_income_analysis(self, ws, analysis_data: Dict[str, Any]):
        """Add income analysis to Excel worksheet."""
        row = 1
        
        # Header
        ws.merge_cells(f"A{row}:C{row}")
        ws[f"A{row}"] = "INCOME STATEMENT ANALYSIS"
        ws[f"A{row}"].font = Font(bold=True, size=14)
        ws[f"A{row}"].alignment = Alignment(horizontal="center")
        row += 2
        
        # Column headers
        ws["A" + str(row)] = "Metric"
        ws["B" + str(row)] = "Current (%)"
        ws["C" + str(row)] = "Comparative (%)"
        
        for col in ["A", "B", "C"]:
            ws[col + str(row)].font = Font(bold=True)
        row += 1
        
        # Add profitability metrics
        if "profitability_metrics" in analysis_data:
            for metric_name, metric_value in analysis_data["profitability_metrics"].items():
                metric_label = metric_name.replace("_", " ").title()
                ws[f"A{row}"] = metric_label
                
                if isinstance(metric_value, dict):
                    ws[f"B{row}"] = metric_value.get("current", 0) * 100
                    ws[f"B{row}"].number_format = "0.0%"
                    if metric_value.get("comparative") is not None:
                        ws[f"C{row}"] = metric_value.get("comparative", 0) * 100
                        ws[f"C{row}"].number_format = "0.0%"
                else:
                    ws[f"B{row}"] = metric_value * 100
                    ws[f"B{row}"].number_format = "0.0%"
                
                row += 1
    
    # DCF Valuation Report Templates
    def generate_dcf_valuation_pdf(self, dcf_data: Dict[str, Any], output_path: str):
        """Generate professional DCF valuation PDF report."""
        try:
            logger.info(f"Generating DCF valuation PDF: {output_path}")
            
            doc = SimpleDocTemplate(
                output_path,
                pagesize=letter,
                rightMargin=72,
                leftMargin=72,
                topMargin=72,
                bottomMargin=18,
            )
            
            story = []
            
            # Executive Summary Header
            story.append(Paragraph("DCF VALUATION REPORT", self.styles["CompanyHeader"]))
            story.append(Paragraph(f"Valuation Date: {dcf_data.get('valuation_date', 'N/A')}", self.styles["Normal"]))
            story.append(Spacer(1, 20))
            
            # Executive Summary
            if "executive_summary" in dcf_data:
                summary = dcf_data["executive_summary"]
                story.append(Paragraph("EXECUTIVE SUMMARY", self.styles["SectionHeader"]))
                
                summary_data = [
                    ["Company Name:", summary.get("company_name", "N/A")],
                    ["Valuation Method:", "Discounted Cash Flow (DCF)"],
                    ["Enterprise Value:", f"${summary.get('base_enterprise_value', 0):,.2f}"],
                    ["Equity Value:", f"${summary.get('equity_value', 0):,.2f}"],
                    ["Discount Rate:", f"{summary.get('discount_rate', 0)*100:.1f}%"],
                    ["Terminal Growth:", f"{summary.get('terminal_growth_rate', 0)*100:.1f}%"],
                    ["Projection Period:", f"{summary.get('projection_years', 5)} years"],
                ]
                
                if summary.get('value_per_share'):
                    summary_data.append(["Value per Share:", f"${summary.get('value_per_share', 0):.2f}"])
                
                summary_table = self._create_financial_table(summary_data)
                story.append(summary_table)
                story.append(Spacer(1, 20))
            
            # Key Assumptions
            if "key_assumptions" in dcf_data:
                story.append(Paragraph("KEY ASSUMPTIONS", self.styles["SectionHeader"]))
                assumptions = dcf_data["key_assumptions"]
                
                assumptions_data = []
                for key, value in assumptions.items():
                    key_label = key.replace("_", " ").title()
                    if isinstance(value, (int, float)):
                        if "rate" in key.lower() or "growth" in key.lower():
                            assumptions_data.append([key_label, f"{value*100:.1f}%"])
                        else:
                            assumptions_data.append([key_label, f"${value:,.2f}"])
                    else:
                        assumptions_data.append([key_label, str(value)])
                
                assumptions_table = self._create_financial_table(assumptions_data)
                story.append(assumptions_table)
                story.append(Spacer(1, 20))
            
            # Financial Projections
            if "financial_projections" in dcf_data:
                story.append(PageBreak())
                story.append(Paragraph("FINANCIAL PROJECTIONS", self.styles["SectionHeader"]))

                projections = dcf_data["financial_projections"]
                if projections:
                    # Add Revenue Projection Chart
                    revenue_chart = self.chart_generator.create_dcf_revenue_projection_chart(projections)
                    revenue_chart_img = self.chart_generator._create_reportlab_image(revenue_chart)
                    if revenue_chart_img:
                        story.append(revenue_chart_img)
                        story.append(Spacer(1, 20))
                    # Create projection table
                    projection_data = [["Metric"] + [f"Year {i}" for i in range(1, len(projections)+1)]]
                    
                    # Revenue row
                    revenue_row = ["Revenue"] + [f"${proj.get('revenue', 0):,.0f}" for proj in projections]
                    projection_data.append(revenue_row)
                    
                    # Free Cash Flow row
                    fcf_row = ["Free Cash Flow"] + [f"${proj.get('free_cash_flow', 0):,.0f}" for proj in projections]
                    projection_data.append(fcf_row)
                    
                    # Growth rates
                    growth_row = ["Revenue Growth"] + [f"{proj.get('revenue_growth_rate', 0)*100:.1f}%" for proj in projections]
                    projection_data.append(growth_row)
                    
                    projection_table = Table(projection_data, colWidths=[2*inch] + [1*inch]*(len(projections)))
                    projection_table.setStyle(TableStyle([
                        ("ALIGN", (0, 0), (-1, -1), "CENTER"),
                        ("FONTNAME", (0, 0), (-1, 0), "Helvetica-Bold"),
                        ("FONTNAME", (0, 1), (0, -1), "Helvetica-Bold"),
                        ("FONTSIZE", (0, 0), (-1, -1), 10),
                        ("GRID", (0, 0), (-1, -1), 0.5, colors.black),
                        ("VALIGN", (0, 0), (-1, -1), "MIDDLE"),
                    ]))
                    
                    story.append(projection_table)
                    story.append(Spacer(1, 20))
            
            # Sensitivity Analysis
            if "sensitivity_analysis" in dcf_data:
                story.append(Paragraph("SENSITIVITY ANALYSIS", self.styles["SectionHeader"]))
                sensitivity = dcf_data["sensitivity_analysis"]

                # Add Sensitivity Tornado Chart
                tornado_chart = self.chart_generator.create_dcf_sensitivity_tornado_chart(sensitivity)
                tornado_chart_img = self.chart_generator._create_reportlab_image(tornado_chart)
                if tornado_chart_img:
                    story.append(tornado_chart_img)
                    story.append(Spacer(1, 20))
                
                sensitivity_data = [
                    ["Valuation Range:", f"${sensitivity.get('min_valuation', 0):,.2f} - ${sensitivity.get('max_valuation', 0):,.2f}"],
                    ["Volatility:", f"{sensitivity.get('volatility', 0)*100:.1f}%"],
                    ["Base Case:", f"${sensitivity.get('base_case_valuation', 0):,.2f}"],
                    ["Upside Case:", f"${sensitivity.get('upside_valuation', 0):,.2f}"],
                    ["Downside Case:", f"${sensitivity.get('downside_valuation', 0):,.2f}"],
                ]
                
                sensitivity_table = self._create_financial_table(sensitivity_data)
                story.append(sensitivity_table)
                story.append(Spacer(1, 20))
            
            # Monte Carlo Results
            if "monte_carlo_simulation" in dcf_data:
                mc_data = dcf_data["monte_carlo_simulation"]
                if "error" not in mc_data:
                    story.append(Paragraph("MONTE CARLO SIMULATION", self.styles["SectionHeader"]))

                    # Add Monte Carlo Distribution Chart
                    mc_chart = self.chart_generator.create_dcf_monte_carlo_distribution_chart(mc_data)
                    mc_chart_img = self.chart_generator._create_reportlab_image(mc_chart)
                    if mc_chart_img:
                        story.append(mc_chart_img)
                        story.append(Spacer(1, 20))

                    mc_results = [
                        ["Number of Simulations:", f"{mc_data.get('num_simulations', 0):,}"],
                        ["Mean Valuation:", f"${mc_data.get('mean_valuation', 0):,.2f}"],
                        ["Standard Deviation:", f"${mc_data.get('std_deviation', 0):,.2f}"],
                        ["5th Percentile:", f"${mc_data.get('percentiles', {}).get('p5', 0):,.2f}"],
                        ["95th Percentile:", f"${mc_data.get('percentiles', {}).get('p95', 0):,.2f}"],
                        ["Probability > $0:", f"{mc_data.get('probability_positive', 0)*100:.1f}%"],
                    ]
                    
                    mc_table = self._create_financial_table(mc_results)
                    story.append(mc_table)
                    story.append(Spacer(1, 20))
            
            # Methodology notes
            story.append(PageBreak())
            story.append(Paragraph("METHODOLOGY NOTES", self.styles["SectionHeader"]))
            methodology_text = """
            The Discounted Cash Flow (DCF) method estimates the intrinsic value of a business by projecting 
            future free cash flows and discounting them to present value using an appropriate discount rate. 
            This analysis includes:
            
            • Detailed financial projections based on historical performance and growth assumptions
            • Terminal value calculation using the Gordon Growth Model
            • Sensitivity analysis to assess valuation range under different scenarios
            • Monte Carlo simulation (if applicable) to model uncertainty and risk
            
            Key assumptions should be reviewed and validated based on company-specific factors and 
            industry benchmarks.
            """
            story.append(Paragraph(methodology_text, self.styles["Normal"]))
            
            doc.build(story)
            logger.info("DCF valuation PDF generated successfully")
            
        except Exception as e:
            logger.error(f"Error generating DCF valuation PDF: {e}")
            raise
    
    def generate_dcf_valuation_excel(self, dcf_data: Dict[str, Any], output_path: str):
        """Generate detailed DCF valuation Excel model."""
        try:
            logger.info(f"Generating DCF valuation Excel: {output_path}")
            
            wb = Workbook()
            
            # Remove default sheet and create custom sheets
            wb.remove(wb.active)
            
            # Executive Summary Sheet
            summary_ws = wb.create_sheet("Executive Summary")
            self._add_dcf_executive_summary_sheet(summary_ws, dcf_data)
            
            # Financial Projections Sheet
            if "financial_projections" in dcf_data:
                proj_ws = wb.create_sheet("Financial Projections")
                self._add_dcf_projections_sheet(proj_ws, dcf_data["financial_projections"])
            
            # DCF Calculation Sheet
            dcf_ws = wb.create_sheet("DCF Analysis")
            self._add_dcf_calculation_sheet(dcf_ws, dcf_data)
            
            # Sensitivity Analysis Sheet
            if "sensitivity_analysis" in dcf_data:
                sens_ws = wb.create_sheet("Sensitivity Analysis")
                self._add_dcf_sensitivity_sheet(sens_ws, dcf_data["sensitivity_analysis"])
            
            # Assumptions Sheet
            if "key_assumptions" in dcf_data:
                assumptions_ws = wb.create_sheet("Key Assumptions")
                self._add_dcf_assumptions_sheet(assumptions_ws, dcf_data["key_assumptions"])
            
            wb.save(output_path)
            logger.info("DCF valuation Excel generated successfully")
            
        except Exception as e:
            logger.error(f"Error generating DCF valuation Excel: {e}")
            raise
    
    # DCF Excel helper methods
    def _add_dcf_executive_summary_sheet(self, ws, dcf_data: Dict[str, Any]):
        """Add executive summary to DCF Excel workbook."""
        # Header
        ws.merge_cells("A1:D1")
        ws["A1"] = "DCF VALUATION - EXECUTIVE SUMMARY"
        ws["A1"].font = Font(bold=True, size=16)
        ws["A1"].alignment = Alignment(horizontal="center")
        
        row = 3
        
        # Company Information
        if "executive_summary" in dcf_data:
            summary = dcf_data["executive_summary"]
            
            ws[f"A{row}"] = "Company Information"
            ws[f"A{row}"].font = Font(bold=True, size=12)
            row += 1
            
            company_data = [
                ["Company Name:", summary.get("company_name", "N/A")],
                ["Valuation Date:", summary.get("valuation_date", "N/A")],
                ["Analysis Performed By:", summary.get("analyst", "MCX3D Finance")],
            ]
            
            for label, value in company_data:
                ws[f"A{row}"] = label
                ws[f"B{row}"] = value
                row += 1
            
            row += 1
            
            # Valuation Results
            ws[f"A{row}"] = "Valuation Results"
            ws[f"A{row}"].font = Font(bold=True, size=12)
            row += 1
            
            valuation_data = [
                ["Enterprise Value:", f"${summary.get('base_enterprise_value', 0):,.2f}"],
                ["Equity Value:", f"${summary.get('equity_value', 0):,.2f}"],
                ["Value per Share:", f"${summary.get('value_per_share', 0):.2f}"] if summary.get('value_per_share') else None,
            ]
            
            for item in valuation_data:
                if item:
                    ws[f"A{row}"] = item[0]
                    ws[f"B{row}"] = item[1]
                    ws[f"B{row}"].number_format = "#,##0.00"
                    row += 1
            
            row += 1
            
            # Key Parameters
            ws[f"A{row}"] = "Key Parameters"
            ws[f"A{row}"].font = Font(bold=True, size=12)
            row += 1
            
            param_data = [
                ["Discount Rate (WACC):", f"{summary.get('discount_rate', 0)*100:.1f}%"],
                ["Terminal Growth Rate:", f"{summary.get('terminal_growth_rate', 0)*100:.1f}%"],
                ["Projection Period:", f"{summary.get('projection_years', 5)} years"],
            ]
            
            for label, value in param_data:
                ws[f"A{row}"] = label
                ws[f"B{row}"] = value
                row += 1
        
        # Auto-adjust column widths
        self._adjust_column_widths(ws, 50)
    
    def _add_dcf_projections_sheet(self, ws, projections: List[Dict[str, Any]]):
        """Add financial projections to DCF Excel workbook."""
        # Header
        ws.merge_cells("A1:F1")
        ws["A1"] = "FINANCIAL PROJECTIONS"
        ws["A1"].font = Font(bold=True, size=16)
        ws["A1"].alignment = Alignment(horizontal="center")
        
        row = 3
        
        if projections:
            # Column headers
            headers = ["Metric"] + [f"Year {i}" for i in range(1, len(projections)+1)]
            for col, header in enumerate(headers, 1):
                cell = ws.cell(row=row, column=col, value=header)
                cell.font = Font(bold=True)
            row += 1
            
            # Financial metrics
            metrics = [
                ("Revenue", "revenue"),
                ("Revenue Growth %", "revenue_growth_rate"),
                ("EBITDA", "ebitda"),
                ("EBIT", "ebit"),
                ("NOPAT", "nopat"),
                ("Free Cash Flow", "free_cash_flow"),
                ("FCF Growth %", "fcf_growth_rate"),
            ]
            
            for metric_name, metric_key in metrics:
                ws.cell(row=row, column=1, value=metric_name).font = Font(bold=True)
                
                for col, projection in enumerate(projections, 2):
                    value = projection.get(metric_key, 0)
                    cell = ws.cell(row=row, column=col, value=value)
                    
                    if "%" in metric_name:
                        cell.number_format = "0.0%"
                    else:
                        cell.number_format = "#,##0"
                
                row += 1
            
            # Auto-adjust column widths
            self._adjust_column_widths(ws, 15)

        # Add Revenue Projection Chart
        try:
            if len(projections) > 1:  # Need at least 2 data points for a chart
                chart_data = [["Year", "Revenue", "Free Cash Flow"]]
                for proj in projections:
                    chart_data.append([
                        f"Year {proj.get('year', 0)}",
                        proj.get('revenue', 0),
                        proj.get('free_cash_flow', 0)
                    ])

                # Add chart data to worksheet starting at row after existing data
                chart_start_row = row + 3
                for i, chart_row in enumerate(chart_data):
                    for j, value in enumerate(chart_row):
                        ws.cell(row=chart_start_row + i, column=j + 1, value=value)

                # Create and add chart
                self.chart_generator.create_excel_chart_from_data(
                    ws, chart_data, "line", "Revenue & Cash Flow Projections", "H5"
                )
        except Exception as e:
            logger.error(f"Error adding DCF projection chart to Excel: {e}")
    
    def _add_dcf_calculation_sheet(self, ws, dcf_data: Dict[str, Any]):
        """Add DCF calculation details to Excel workbook."""
        # Header
        ws.merge_cells("A1:D1")
        ws["A1"] = "DCF CALCULATION"
        ws["A1"].font = Font(bold=True, size=16)
        ws["A1"].alignment = Alignment(horizontal="center")
        
        row = 3
        
        # Present Value of Operating FCF
        if "valuation_results" in dcf_data:
            results = dcf_data["valuation_results"]
            
            if isinstance(results, dict):
                # Single scenario
                result_data = list(results.values())[0] if results else {}
            else:
                # Multiple scenarios - use base
                result_data = results.get("base", {})
            
            ws[f"A{row}"] = "DCF Calculation Components"
            ws[f"A{row}"].font = Font(bold=True, size=12)
            row += 1
            
            calc_data = [
                ["Present Value of Operating FCF:", f"${result_data.get('pv_operating_fcf', 0):,.2f}"],
                ["Terminal Value:", f"${result_data.get('terminal_value', 0):,.2f}"],
                ["PV of Terminal Value:", f"${result_data.get('pv_terminal_value', 0):,.2f}"],
                ["Enterprise Value:", f"${result_data.get('enterprise_value', 0):,.2f}"],
                ["Less: Net Debt:", f"${result_data.get('net_debt', 0):,.2f}"],
                ["Equity Value:", f"${result_data.get('equity_value', 0):,.2f}"],
            ]
            
            for label, value in calc_data:
                ws[f"A{row}"] = label
                ws[f"B{row}"] = value
                ws[f"A{row}"].font = Font(bold=True)
                row += 1
            
            row += 1
            
            # Key ratios and metrics
            if "financial_metrics" in result_data:
                ws[f"A{row}"] = "Key Financial Metrics"
                ws[f"A{row}"].font = Font(bold=True, size=12)
                row += 1
                
                metrics = result_data["financial_metrics"]
                for metric, value in metrics.items():
                    metric_label = metric.replace("_", " ").title()
                    ws[f"A{row}"] = metric_label
                    if isinstance(value, (int, float)):
                        if "ratio" in metric.lower() or "margin" in metric.lower():
                            ws[f"B{row}"] = f"{value:.2f}"
                        else:
                            ws[f"B{row}"] = f"${value:,.2f}"
                    else:
                        ws[f"B{row}"] = str(value)
                    row += 1
        
        # Auto-adjust column widths
        self._adjust_column_widths(ws, 40)
    
    def _add_dcf_sensitivity_sheet(self, ws, sensitivity_data: Dict[str, Any]):
        """Add sensitivity analysis to Excel workbook."""
        # Header
        ws.merge_cells("A1:E1")
        ws["A1"] = "SENSITIVITY ANALYSIS"
        ws["A1"].font = Font(bold=True, size=16)
        ws["A1"].alignment = Alignment(horizontal="center")
        
        row = 3
        
        # Summary statistics
        ws[f"A{row}"] = "Valuation Range Analysis"
        ws[f"A{row}"].font = Font(bold=True, size=12)
        row += 1
        
        range_data = [
            ["Minimum Valuation:", f"${sensitivity_data.get('min_valuation', 0):,.2f}"],
            ["Maximum Valuation:", f"${sensitivity_data.get('max_valuation', 0):,.2f}"],
            ["Base Case:", f"${sensitivity_data.get('base_case_valuation', 0):,.2f}"],
            ["Standard Deviation:", f"${sensitivity_data.get('std_deviation', 0):,.2f}"],
            ["Volatility:", f"{sensitivity_data.get('volatility', 0)*100:.1f}%"],
        ]
        
        for label, value in range_data:
            ws[f"A{row}"] = label
            ws[f"B{row}"] = value
            ws[f"A{row}"].font = Font(bold=True)
            row += 1
        
        row += 2
        
        # Scenario analysis
        if "scenarios" in sensitivity_data:
            ws[f"A{row}"] = "Scenario Analysis"
            ws[f"A{row}"].font = Font(bold=True, size=12)
            row += 1
            
            scenarios = sensitivity_data["scenarios"]
            for scenario_name, scenario_value in scenarios.items():
                scenario_label = scenario_name.replace("_", " ").title()
                ws[f"A{row}"] = f"{scenario_label} Case:"
                ws[f"B{row}"] = f"${scenario_value:,.2f}"
                row += 1
        
        # Auto-adjust column widths
        self._adjust_column_widths(ws, 30)
    
    def _add_dcf_assumptions_sheet(self, ws, assumptions: Dict[str, Any]):
        """Add key assumptions to Excel workbook."""
        # Header
        ws.merge_cells("A1:C1")
        ws["A1"] = "KEY ASSUMPTIONS"
        ws["A1"].font = Font(bold=True, size=16)
        ws["A1"].alignment = Alignment(horizontal="center")
        
        row = 3
        
        # Column headers
        ws[f"A{row}"] = "Assumption"
        ws[f"B{row}"] = "Value"
        ws[f"C{row}"] = "Notes"
        
        for col in ["A", "B", "C"]:
            ws[f"{col}{row}"].font = Font(bold=True)
        row += 1
        
        # Add assumptions
        for key, value in assumptions.items():
            assumption_label = key.replace("_", " ").title()
            ws[f"A{row}"] = assumption_label
            
            if isinstance(value, dict):
                ws[f"B{row}"] = str(value.get("value", "N/A"))
                ws[f"C{row}"] = value.get("notes", "")
            else:
                if isinstance(value, (int, float)):
                    if "rate" in key.lower() or "growth" in key.lower():
                        ws[f"B{row}"] = f"{value*100:.1f}%"
                    else:
                        ws[f"B{row}"] = f"${value:,.2f}"
                else:
                    ws[f"B{row}"] = str(value)
            
            row += 1
        
        # Auto-adjust column widths
        self._adjust_column_widths(ws, 50)
    
    # SaaS Valuation Report Templates
    def generate_saas_valuation_pdf(self, saas_data: Dict[str, Any], output_path: str):
        """Generate professional SaaS valuation PDF report."""
        try:
            logger.info(f"Generating SaaS valuation PDF: {output_path}")
            
            doc = SimpleDocTemplate(
                output_path,
                pagesize=letter,
                rightMargin=72,
                leftMargin=72,
                topMargin=72,
                bottomMargin=18,
            )
            
            story = []
            
            # Header
            story.append(Paragraph("COMPREHENSIVE SAAS VALUATION REPORT", self.styles["CompanyHeader"]))
            story.append(Paragraph(f"Valuation Date: {saas_data.get('valuation_date', 'N/A')}", self.styles["Normal"]))
            story.append(Spacer(1, 20))
            
            # Executive Summary
            story.append(Paragraph("EXECUTIVE SUMMARY", self.styles["SectionHeader"]))
            
            key_metrics = saas_data.get("key_metrics", {})
            exec_summary_data = [
                ["Organization ID:", str(saas_data.get("organization_id", "N/A"))],
                ["Valuation Method:", saas_data.get("methodology", "Comprehensive SaaS Analysis")],
                ["Annual Recurring Revenue:", f"${key_metrics.get('arr', 0):,.2f}"],
                ["Monthly Recurring Revenue:", f"${key_metrics.get('mrr', 0):,.2f}"],
                ["Growth Rate:", f"{key_metrics.get('growth_rate', 0):.1f}%"],
                ["Customer Churn Rate:", f"{key_metrics.get('churn_rate', 0):.1f}%"],
                ["LTV/CAC Ratio:", f"{key_metrics.get('ltv_cac_ratio', 0):.1f}x"],
                ["Active Customers:", f"{key_metrics.get('active_customers', 0):,}"],
            ]
            
            exec_table = self._create_financial_table(exec_summary_data)
            story.append(exec_table)
            story.append(Spacer(1, 20))

            # Add SaaS Charts
            story.append(PageBreak())
            story.append(Paragraph("SAAS METRICS VISUALIZATION", self.styles["SectionHeader"]))

            # ARR Growth Chart
            arr_chart = self.chart_generator.create_saas_arr_growth_chart(saas_data)
            arr_chart_img = self.chart_generator._create_reportlab_image(arr_chart)
            if arr_chart_img:
                story.append(arr_chart_img)
                story.append(Spacer(1, 20))

            # Unit Economics Chart
            unit_econ_chart = self.chart_generator.create_saas_unit_economics_chart(saas_data)
            unit_econ_chart_img = self.chart_generator._create_reportlab_image(unit_econ_chart)
            if unit_econ_chart_img:
                story.append(unit_econ_chart_img)
                story.append(Spacer(1, 20))

            # Weighted Valuation Results
            weighted_val = saas_data.get("weighted_valuation", {})
            if weighted_val and "error" not in weighted_val:
                story.append(Paragraph("VALUATION SUMMARY", self.styles["SectionHeader"]))
                
                val_range = weighted_val.get("valuation_range", {})
                valuation_data = [
                    ["Primary Valuation:", f"${weighted_val.get('weighted_average_valuation', 0):,.2f}"],
                    ["Valuation Range:", f"${val_range.get('minimum', 0):,.2f} - ${val_range.get('maximum', 0):,.2f}"],
                    ["Valuation Spread:", f"{val_range.get('spread_percentage', 0):.1f}%"],
                    ["Methods Used:", str(weighted_val.get("methods_used", 0))],
                ]
                
                val_table = self._create_financial_table(valuation_data)
                story.append(val_table)
                story.append(Spacer(1, 20))
            
            # Individual Valuation Methods
            valuation_methods = saas_data.get("valuation_methods", {})
            if valuation_methods:
                story.append(PageBreak())
                story.append(Paragraph("INDIVIDUAL VALUATION METHODS", self.styles["SectionHeader"]))

                # Add Valuation Methods Comparison Chart
                comparison_chart = self.chart_generator.create_saas_valuation_methods_comparison_chart(saas_data)
                comparison_chart_img = self.chart_generator._create_reportlab_image(comparison_chart)
                if comparison_chart_img:
                    story.append(comparison_chart_img)
                    story.append(Spacer(1, 20))

                for method, data in valuation_methods.items():
                    if "error" not in data:
                        method_name = method.replace("_", " ").title()
                        story.append(Paragraph(f"{method_name} Analysis:", self.styles["Normal"]))
                        
                        method_data = []
                        if method == "arr_multiple":
                            method_data = [
                                ["Base ARR:", f"${data.get('base_arr', 0):,.2f}"],
                                ["Base Multiple:", f"{data.get('base_multiple', 0):.1f}x"],
                                ["Adjusted Multiple:", f"{data.get('adjusted_multiple', 0):.1f}x"],
                                ["Adjusted Valuation:", f"${data.get('adjusted_valuation', 0):,.2f}"],
                                ["Confidence Score:", f"{data.get('confidence_score', 0):.0%}"],
                            ]
                        elif method == "revenue_multiple":
                            method_data = [
                                ["Annual Revenue:", f"${data.get('annual_revenue', 0):,.2f}"],
                                ["Revenue Multiple:", f"{data.get('revenue_multiple', 0):.1f}x"],
                                ["Valuation:", f"${data.get('valuation', 0):,.2f}"],
                            ]
                        elif method == "saas_dcf":
                            method_data = [
                                ["Enterprise Value:", f"${data.get('enterprise_value', 0):,.2f}"],
                                ["PV of Operating FCF:", f"${data.get('pv_operating_fcf', 0):,.2f}"],
                                ["Terminal Value:", f"${data.get('terminal_value', 0):,.2f}"],
                                ["Discount Rate:", f"{data.get('discount_rate', 0)*100:.1f}%"],
                            ]
                        elif method == "unit_economics":
                            method_data = [
                                ["Total Valuation:", f"${data.get('total_valuation', 0):,.2f}"],
                                ["Customer Base Value:", f"${data.get('customer_base_value', 0):,.2f}"],
                                ["LTV/CAC Ratio:", f"{data.get('ltv_cac_ratio', 0):.1f}x"],
                            ]
                        
                        if method_data:
                            method_table = self._create_financial_table(method_data)
                            story.append(method_table)
                            story.append(Spacer(1, 15))
            
            # Business Quality Assessment
            quality_assessment = saas_data.get("quality_assessment", {})
            if quality_assessment and "error" not in quality_assessment:
                story.append(PageBreak())
                story.append(Paragraph("BUSINESS QUALITY ASSESSMENT", self.styles["SectionHeader"]))
                
                quality_data = [
                    ["Overall Score:", f"{quality_assessment.get('total_score', 0)}/100"],
                    ["Quality Grade:", quality_assessment.get("grade", "N/A")],
                    ["Quality Tier:", quality_assessment.get("quality_tier", "N/A")],
                ]
                
                component_scores = quality_assessment.get("component_scores", {})
                if component_scores:
                    for component, score in component_scores.items():
                        component_name = component.replace("_", " ").title()
                        quality_data.append([f"{component_name}:", f"{score}/25"])
                
                quality_table = self._create_financial_table(quality_data)
                story.append(quality_table)
                story.append(Spacer(1, 20))
            
            # Industry Benchmarks
            benchmarks = saas_data.get("industry_benchmarks", {})
            if benchmarks:
                story.append(Paragraph("INDUSTRY BENCHMARKS", self.styles["SectionHeader"]))
                
                key_benchmarks = benchmarks.get("key_metrics", {})
                if key_benchmarks:
                    benchmark_data = []
                    for metric, levels in key_benchmarks.items():
                        metric_name = metric.replace("_", " ").title()
                        if isinstance(levels, dict):
                            excellent = levels.get("excellent", "N/A")
                            good = levels.get("good", "N/A")
                            poor = levels.get("poor", "N/A")
                            if metric == "growth_rate":
                                benchmark_data.append([f"{metric_name} (Excellent):", f"{excellent}%"])
                                benchmark_data.append([f"{metric_name} (Good):", f"{good}%"])
                            elif metric == "churn_rate":
                                benchmark_data.append([f"{metric_name} (Excellent):", f"<{excellent}%"])
                                benchmark_data.append([f"{metric_name} (Poor):", f">{poor}%"])
                            elif metric == "ltv_cac_ratio":
                                benchmark_data.append([f"{metric_name} (Excellent):", f">{excellent}x"])
                                benchmark_data.append([f"{metric_name} (Poor):", f"<{poor}x"])
                    
                    if benchmark_data:
                        benchmark_table = self._create_financial_table(benchmark_data)
                        story.append(benchmark_table)
                        story.append(Spacer(1, 20))
            
            # Methodology Notes
            story.append(PageBreak())
            story.append(Paragraph("METHODOLOGY NOTES", self.styles["SectionHeader"]))
            methodology_text = """
            This comprehensive SaaS valuation employs multiple industry-standard methodologies:
            
            • ARR Multiple Valuation: Values the business based on Annual Recurring Revenue with quality adjustments for growth, unit economics, retention, and scale
            
            • Revenue Multiple Valuation: Traditional revenue-based valuation adjusted for SaaS-specific metrics including growth rates and gross margins
            
            • SaaS-Optimized DCF: Discounted Cash Flow analysis tailored for SaaS businesses with appropriate assumptions for recurring revenue models
            
            • Unit Economics Valuation: Values the business based on customer lifetime value (LTV) and acquisition costs (CAC)
            
            The weighted average combines these methodologies to provide a comprehensive valuation range. Quality assessment scores are based on growth sustainability, unit economics strength, customer retention, and revenue expansion metrics.
            
            All valuations should be considered within the context of current market conditions and company-specific factors.
            """
            story.append(Paragraph(methodology_text, self.styles["Normal"]))
            
            doc.build(story)
            logger.info("SaaS valuation PDF generated successfully")
            
        except Exception as e:
            logger.error(f"Error generating SaaS valuation PDF: {e}")
            raise
    
    def generate_saas_valuation_excel(self, saas_data: Dict[str, Any], output_path: str):
        """Generate detailed SaaS valuation Excel model."""
        try:
            logger.info(f"Generating SaaS valuation Excel: {output_path}")
            
            wb = Workbook()
            wb.remove(wb.active)
            
            # Executive Summary Sheet
            summary_ws = wb.create_sheet("Executive Summary")
            self._add_saas_executive_summary_sheet(summary_ws, saas_data)
            
            # SaaS Metrics Dashboard
            metrics_ws = wb.create_sheet("SaaS Metrics")
            self._add_saas_metrics_sheet(metrics_ws, saas_data)
            
            # Valuation Analysis Sheet
            valuation_ws = wb.create_sheet("Valuation Analysis")
            self._add_saas_valuation_sheet(valuation_ws, saas_data)
            
            # Quality Assessment Sheet
            if "quality_assessment" in saas_data:
                quality_ws = wb.create_sheet("Quality Assessment")
                self._add_saas_quality_sheet(quality_ws, saas_data["quality_assessment"])
            
            # Industry Benchmarks Sheet
            if "industry_benchmarks" in saas_data:
                benchmarks_ws = wb.create_sheet("Industry Benchmarks")
                self._add_saas_benchmarks_sheet(benchmarks_ws, saas_data["industry_benchmarks"])
            
            wb.save(output_path)
            logger.info("SaaS valuation Excel generated successfully")
            
        except Exception as e:
            logger.error(f"Error generating SaaS valuation Excel: {e}")
            raise
    
    # SaaS Excel helper methods
    def _add_saas_executive_summary_sheet(self, ws, saas_data: Dict[str, Any]):
        """Add SaaS executive summary to Excel workbook."""
        # Header
        ws.merge_cells("A1:D1")
        ws["A1"] = "SAAS VALUATION - EXECUTIVE SUMMARY"
        ws["A1"].font = Font(bold=True, size=16)
        ws["A1"].alignment = Alignment(horizontal="center")
        
        row = 3
        
        # Company Information
        ws[f"A{row}"] = "Company Information"
        ws[f"A{row}"].font = Font(bold=True, size=12)
        row += 1
        
        company_data = [
            ["Organization ID:", str(saas_data.get("organization_id", "N/A"))],
            ["Valuation Date:", saas_data.get("valuation_date", "N/A")],
            ["Analysis Period:", f"{saas_data.get('analysis_period', {}).get('start', 'N/A')} to {saas_data.get('analysis_period', {}).get('end', 'N/A')}"],
        ]
        
        for label, value in company_data:
            ws[f"A{row}"] = label
            ws[f"B{row}"] = value
            row += 1
        
        row += 1
        
        # Key SaaS Metrics
        key_metrics = saas_data.get("key_metrics", {})
        if key_metrics:
            ws[f"A{row}"] = "Key SaaS Metrics"
            ws[f"A{row}"].font = Font(bold=True, size=12)
            row += 1
            
            metrics_data = [
                ["Annual Recurring Revenue:", f"${key_metrics.get('arr', 0):,.2f}"],
                ["Monthly Recurring Revenue:", f"${key_metrics.get('mrr', 0):,.2f}"],
                ["Growth Rate:", f"{key_metrics.get('growth_rate', 0):.1f}%"],
                ["Churn Rate:", f"{key_metrics.get('churn_rate', 0):.1f}%"],
                ["LTV/CAC Ratio:", f"{key_metrics.get('ltv_cac_ratio', 0):.1f}x"],
                ["Net Revenue Retention:", f"{key_metrics.get('nrr', 0):.1f}%"],
                ["Active Customers:", f"{key_metrics.get('active_customers', 0):,}"],
            ]
            
            for label, value in metrics_data:
                ws[f"A{row}"] = label
                ws[f"B{row}"] = value
                row += 1
        
        row += 1
        
        # Valuation Results
        weighted_val = saas_data.get("weighted_valuation", {})
        if weighted_val and "error" not in weighted_val:
            ws[f"A{row}"] = "Valuation Results"
            ws[f"A{row}"].font = Font(bold=True, size=12)
            row += 1
            
            val_range = weighted_val.get("valuation_range", {})
            valuation_data = [
                ["Primary Valuation:", f"${weighted_val.get('weighted_average_valuation', 0):,.2f}"],
                ["Minimum Valuation:", f"${val_range.get('minimum', 0):,.2f}"],
                ["Maximum Valuation:", f"${val_range.get('maximum', 0):,.2f}"],
                ["Valuation Spread:", f"{val_range.get('spread_percentage', 0):.1f}%"],
                ["Methods Used:", str(weighted_val.get("methods_used", 0))],
            ]
            
            for label, value in valuation_data:
                ws[f"A{row}"] = label
                ws[f"B{row}"] = value
                if "$" in value:
                    ws[f"B{row}"].number_format = "#,##0.00"
                row += 1
        
        # Auto-adjust column widths
        for column in ws.columns:
            max_length = 0
            column_letter = column[0].column_letter
            for cell in column:
                try:
                    if len(str(cell.value)) > max_length:
                        max_length = len(str(cell.value))
                except (TypeError, ValueError):
                    pass
            adjusted_width = min(max_length + 2, 40)
            ws.column_dimensions[column_letter].width = adjusted_width
    
    def _add_saas_metrics_sheet(self, ws, saas_data: Dict[str, Any]):
        """Add detailed SaaS metrics to Excel workbook."""
        # Header
        ws.merge_cells("A1:D1")
        ws["A1"] = "SAAS METRICS DASHBOARD"
        ws["A1"].font = Font(bold=True, size=16)
        ws["A1"].alignment = Alignment(horizontal="center")
        
        row = 3
        
        # Extract full KPI analysis
        full_kpis = saas_data.get("full_kpi_analysis", {})
        if full_kpis:
            kpis = full_kpis.get("kpis", {})
            
            # Revenue Metrics Section
            revenue_metrics = kpis.get("revenue_metrics", {})
            if revenue_metrics:
                ws[f"A{row}"] = "Revenue Metrics"
                ws[f"A{row}"].font = Font(bold=True, size=12)
                row += 1
                
                ws[f"A{row}"] = "Metric"
                ws[f"B{row}"] = "Value"
                ws[f"A{row}"].font = Font(bold=True)
                ws[f"B{row}"].font = Font(bold=True)
                row += 1
                
                for metric, value in revenue_metrics.items():
                    metric_name = metric.replace("_", " ").title()
                    ws[f"A{row}"] = metric_name
                    
                    if isinstance(value, dict):
                        # Handle nested metrics like growth rates
                        if "monthly" in value:
                            ws[f"B{row}"] = f"{value.get('monthly', 0):.1f}%"
                        elif "annual" in value:
                            ws[f"B{row}"] = f"{value.get('annual', 0):.1f}%"
                        else:
                            ws[f"B{row}"] = str(value)
                    elif isinstance(value, (int, float)):
                        if "rate" in metric or "percentage" in metric:
                            ws[f"B{row}"] = f"{value:.1f}%"
                        elif "revenue" in metric or "value" in metric:
                            ws[f"B{row}"] = f"${value:,.2f}"
                            ws[f"B{row}"].number_format = "#,##0.00"
                        else:
                            ws[f"B{row}"] = f"{value:,.2f}"
                    else:
                        ws[f"B{row}"] = str(value)
                    
                    row += 1
                
                row += 1
            
            # Customer Metrics Section
            customer_metrics = kpis.get("customer_metrics", {})
            if customer_metrics:
                ws[f"A{row}"] = "Customer Metrics"
                ws[f"A{row}"].font = Font(bold=True, size=12)
                row += 1
                
                ws[f"A{row}"] = "Metric"
                ws[f"B{row}"] = "Value"
                ws[f"A{row}"].font = Font(bold=True)
                ws[f"B{row}"].font = Font(bold=True)
                row += 1
                
                for metric, value in customer_metrics.items():
                    metric_name = metric.replace("_", " ").title()
                    ws[f"A{row}"] = metric_name
                    
                    if isinstance(value, (int, float)):
                        if "rate" in metric or "churn" in metric:
                            ws[f"B{row}"] = f"{value:.1f}%"
                        elif "cost" in metric or "value" in metric:
                            ws[f"B{row}"] = f"${value:,.2f}"
                            ws[f"B{row}"].number_format = "#,##0.00"
                        elif "ratio" in metric:
                            ws[f"B{row}"] = f"{value:.1f}x"
                        elif "customers" in metric:
                            ws[f"B{row}"] = f"{value:,}"
                        else:
                            ws[f"B{row}"] = f"{value:,.2f}"
                    else:
                        ws[f"B{row}"] = str(value)
                    
                    row += 1
        
        # Auto-adjust column widths
        for column in ws.columns:
            max_length = 0
            column_letter = column[0].column_letter
            for cell in column:
                try:
                    if len(str(cell.value)) > max_length:
                        max_length = len(str(cell.value))
                except (TypeError, ValueError):
                    pass
            adjusted_width = min(max_length + 2, 40)
            ws.column_dimensions[column_letter].width = adjusted_width

        # Add SaaS Unit Economics Chart
        try:
            key_metrics = saas_data.get("key_metrics", {})
            if key_metrics:
                chart_data = [["Metric", "Value"]]
                chart_data.append(["CAC", key_metrics.get("customer_acquisition_cost", 150)])
                chart_data.append(["LTV", key_metrics.get("customer_lifetime_value", 750)])
                chart_data.append(["LTV/CAC Ratio", key_metrics.get("ltv_cac_ratio", 5)])

                # Add chart data to worksheet
                chart_start_row = row + 3
                for i, chart_row in enumerate(chart_data):
                    for j, value in enumerate(chart_row):
                        ws.cell(row=chart_start_row + i, column=j + 1, value=value)

                # Create and add chart
                self.chart_generator.create_excel_chart_from_data(
                    ws, chart_data, "bar", "SaaS Unit Economics", "F5"
                )
        except Exception as e:
            logger.error(f"Error adding SaaS metrics chart to Excel: {e}")
    
    def _add_saas_valuation_sheet(self, ws, saas_data: Dict[str, Any]):
        """Add valuation analysis to Excel workbook."""
        # Header
        ws.merge_cells("A1:E1")
        ws["A1"] = "VALUATION ANALYSIS"
        ws["A1"].font = Font(bold=True, size=16)
        ws["A1"].alignment = Alignment(horizontal="center")
        
        row = 3
        
        # Individual Valuation Methods
        valuation_methods = saas_data.get("valuation_methods", {})
        if valuation_methods:
            ws[f"A{row}"] = "Individual Valuation Methods"
            ws[f"A{row}"].font = Font(bold=True, size=12)
            row += 1
            
            # Headers
            ws[f"A{row}"] = "Method"
            ws[f"B{row}"] = "Valuation"
            ws[f"C{row}"] = "Key Metric"
            ws[f"D{row}"] = "Multiple/Rate"
            ws[f"E{row}"] = "Confidence"
            
            for col in ["A", "B", "C", "D", "E"]:
                ws[f"{col}{row}"].font = Font(bold=True)
            row += 1
            
            for method, data in valuation_methods.items():
                if "error" not in data:
                    method_name = method.replace("_", " ").title()
                    ws[f"A{row}"] = method_name
                    
                    if method == "arr_multiple":
                        ws[f"B{row}"] = data.get("adjusted_valuation", 0)
                        ws[f"C{row}"] = f"${data.get('base_arr', 0):,.0f}"
                        ws[f"D{row}"] = f"{data.get('adjusted_multiple', 0):.1f}x"
                        ws[f"E{row}"] = f"{data.get('confidence_score', 0):.0%}"
                    elif method == "revenue_multiple":
                        ws[f"B{row}"] = data.get("valuation", 0)
                        ws[f"C{row}"] = f"${data.get('annual_revenue', 0):,.0f}"
                        ws[f"D{row}"] = f"{data.get('revenue_multiple', 0):.1f}x"
                        ws[f"E{row}"] = "N/A"
                    elif method == "saas_dcf":
                        ws[f"B{row}"] = data.get("enterprise_value", 0)
                        ws[f"C{row}"] = f"${data.get('pv_operating_fcf', 0):,.0f}"
                        ws[f"D{row}"] = f"{data.get('discount_rate', 0)*100:.1f}%"
                        ws[f"E{row}"] = "High"
                    elif method == "unit_economics":
                        ws[f"B{row}"] = data.get("total_valuation", 0)
                        ws[f"C{row}"] = f"{data.get('active_customers', 0):,}"
                        ws[f"D{row}"] = f"{data.get('ltv_cac_ratio', 0):.1f}x"
                        ws[f"E{row}"] = "Medium"
                    
                    ws[f"B{row}"].number_format = "#,##0"
                    row += 1
            
            row += 2
        
        # Weighted Average Results
        weighted_val = saas_data.get("weighted_valuation", {})
        if weighted_val and "error" not in weighted_val:
            ws[f"A{row}"] = "Weighted Average Valuation"
            ws[f"A{row}"].font = Font(bold=True, size=12)
            row += 1
            
            val_data = [
                ["Primary Valuation:", weighted_val.get("weighted_average_valuation", 0)],
                ["Minimum:", weighted_val.get("valuation_range", {}).get("minimum", 0)],
                ["Maximum:", weighted_val.get("valuation_range", {}).get("maximum", 0)],
                ["Methods Used:", weighted_val.get("methods_used", 0)],
            ]
            
            for label, value in val_data:
                ws[f"A{row}"] = label
                ws[f"B{row}"] = value
                if isinstance(value, (int, float)) and value > 1000:
                    ws[f"B{row}"].number_format = "#,##0"
                row += 1
        
        # Auto-adjust column widths
        for column in ws.columns:
            max_length = 0
            column_letter = column[0].column_letter
            for cell in column:
                try:
                    if len(str(cell.value)) > max_length:
                        max_length = len(str(cell.value))
                except (TypeError, ValueError):
                    pass
            adjusted_width = min(max_length + 2, 20)
            ws.column_dimensions[column_letter].width = adjusted_width
    
    def _add_saas_quality_sheet(self, ws, quality_data: Dict[str, Any]):
        """Add business quality assessment to Excel workbook."""
        # Header
        ws.merge_cells("A1:C1")
        ws["A1"] = "BUSINESS QUALITY ASSESSMENT"
        ws["A1"].font = Font(bold=True, size=16)
        ws["A1"].alignment = Alignment(horizontal="center")
        
        row = 3
        
        # Overall Quality Score
        ws[f"A{row}"] = "Overall Assessment"
        ws[f"A{row}"].font = Font(bold=True, size=12)
        row += 1
        
        overall_data = [
            ["Total Score:", f"{quality_data.get('total_score', 0)}/100"],
            ["Quality Grade:", quality_data.get("grade", "N/A")],
            ["Quality Tier:", quality_data.get("quality_tier", "N/A")],
        ]
        
        for label, value in overall_data:
            ws[f"A{row}"] = label
            ws[f"B{row}"] = value
            row += 1
        
        row += 1
        
        # Component Scores
        component_scores = quality_data.get("component_scores", {})
        if component_scores:
            ws[f"A{row}"] = "Component Scores"
            ws[f"A{row}"].font = Font(bold=True, size=12)
            row += 1
            
            ws[f"A{row}"] = "Component"
            ws[f"B{row}"] = "Score"
            ws[f"C{row}"] = "Percentage"
            
            for col in ["A", "B", "C"]:
                ws[f"{col}{row}"].font = Font(bold=True)
            row += 1
            
            for component, score in component_scores.items():
                component_name = component.replace("_", " ").title()
                ws[f"A{row}"] = component_name
                ws[f"B{row}"] = f"{score}/25"
                ws[f"C{row}"] = f"{(score/25)*100:.0f}%"
                row += 1
        
        # Auto-adjust column widths
        for column in ws.columns:
            max_length = 0
            column_letter = column[0].column_letter
            for cell in column:
                try:
                    if len(str(cell.value)) > max_length:
                        max_length = len(str(cell.value))
                except (TypeError, ValueError):
                    pass
            adjusted_width = min(max_length + 2, 30)
            ws.column_dimensions[column_letter].width = adjusted_width
    
    def _add_saas_benchmarks_sheet(self, ws, benchmarks_data: Dict[str, Any]):
        """Add industry benchmarks to Excel workbook."""
        # Header
        ws.merge_cells("A1:E1")
        ws["A1"] = "INDUSTRY BENCHMARKS"
        ws["A1"].font = Font(bold=True, size=16)
        ws["A1"].alignment = Alignment(horizontal="center")
        
        row = 3
        
        # Key Metrics Benchmarks
        key_metrics = benchmarks_data.get("key_metrics", {})
        if key_metrics:
            ws[f"A{row}"] = "SaaS Industry Benchmarks"
            ws[f"A{row}"].font = Font(bold=True, size=12)
            row += 1
            
            # Headers
            ws[f"A{row}"] = "Metric"
            ws[f"B{row}"] = "Excellent"
            ws[f"C{row}"] = "Good"
            ws[f"D{row}"] = "Average"
            ws[f"E{row}"] = "Poor"
            
            for col in ["A", "B", "C", "D", "E"]:
                ws[f"{col}{row}"].font = Font(bold=True)
            row += 1
            
            for metric, levels in key_metrics.items():
                if isinstance(levels, dict):
                    metric_name = metric.replace("_", " ").title()
                    ws[f"A{row}"] = metric_name
                    
                    excellent = levels.get("excellent", "N/A")
                    good = levels.get("good", "N/A")
                    average = levels.get("average", "N/A")
                    poor = levels.get("poor", "N/A")
                    
                    if metric == "growth_rate":
                        ws[f"B{row}"] = f">{excellent}%"
                        ws[f"C{row}"] = f">{good}%"
                        ws[f"D{row}"] = f">{average}%"
                        ws[f"E{row}"] = f"<{poor}%"
                    elif metric == "churn_rate":
                        ws[f"B{row}"] = f"<{excellent}%"
                        ws[f"C{row}"] = f"<{good}%"
                        ws[f"D{row}"] = f"<{average}%"
                        ws[f"E{row}"] = f">{poor}%"
                    elif metric == "ltv_cac_ratio":
                        ws[f"B{row}"] = f">{excellent}x"
                        ws[f"C{row}"] = f">{good}x"
                        ws[f"D{row}"] = f">{average}x"
                        ws[f"E{row}"] = f"<{poor}x"
                    elif metric == "net_revenue_retention":
                        ws[f"B{row}"] = f">{excellent}%"
                        ws[f"C{row}"] = f">{good}%"
                        ws[f"D{row}"] = f">{average}%"
                        ws[f"E{row}"] = f"<{poor}%"
                    else:
                        ws[f"B{row}"] = str(excellent)
                        ws[f"C{row}"] = str(good)
                        ws[f"D{row}"] = str(average)
                        ws[f"E{row}"] = str(poor)
                    
                    row += 1
        
        # Auto-adjust column widths
        for column in ws.columns:
            max_length = 0
            column_letter = column[0].column_letter
            for cell in column:
                try:
                    if len(str(cell.value)) > max_length:
                        max_length = len(str(cell.value))
                except (TypeError, ValueError):
                    pass
            adjusted_width = min(max_length + 2, 20)
            ws.column_dimensions[column_letter].width = adjusted_width


class FinancialChartGenerator:
    """Professional financial chart generator for PDF and Excel reports."""

    def __init__(self):
        """Initialize chart generator with professional styling."""
        self.plotly_config = {
            "displayModeBar": False,
            "staticPlot": True,
            "responsive": False
        }

        # Professional financial color palette
        self.colors = {
            "primary": "#1f77b4",      # Professional blue
            "secondary": "#ff7f0e",    # Orange
            "success": "#2ca02c",      # Green
            "danger": "#d62728",       # Red
            "warning": "#ff7f0e",      # Orange
            "info": "#17a2b8",         # Cyan
            "dark": "#343a40",         # Dark gray
            "light": "#f8f9fa",        # Light gray
            "neutral": "#6c757d"       # Medium gray
        }

        # Chart styling defaults
        self.default_layout = {
            "font": {"family": "Arial, sans-serif", "size": 12, "color": "#333333"},
            "plot_bgcolor": "white",
            "paper_bgcolor": "white",
            "margin": {"l": 60, "r": 40, "t": 60, "b": 60},
            "showlegend": True,
            "legend": {
                "orientation": "h",
                "yanchor": "bottom",
                "y": -0.2,
                "xanchor": "center",
                "x": 0.5
            }
        }

    def _apply_professional_styling(self, fig: go.Figure, title: str = "") -> go.Figure:
        """Apply consistent professional styling to charts."""
        fig.update_layout(
            **self.default_layout,
            title={
                "text": title,
                "x": 0.5,
                "xanchor": "center",
                "font": {"size": 16, "color": "#333333"}
            },
            xaxis={
                "showgrid": True,
                "gridcolor": "#e6e6e6",
                "linecolor": "#cccccc",
                "tickfont": {"size": 10}
            },
            yaxis={
                "showgrid": True,
                "gridcolor": "#e6e6e6",
                "linecolor": "#cccccc",
                "tickfont": {"size": 10}
            }
        )
        return fig

    def _generate_chart_image(self, fig: go.Figure, width: int = 800, height: int = 500) -> str:
        """Convert Plotly figure to base64 encoded PNG for PDF embedding."""
        try:
            img_bytes = fig.to_image(
                format="png",
                width=width,
                height=height,
                scale=2  # High DPI for crisp output
            )
            img_base64 = base64.b64encode(img_bytes).decode()
            return f"data:image/png;base64,{img_base64}"
        except Exception as e:
            logger.error(f"Error generating chart image: {e}")
            return ""

    def _create_reportlab_image(self, fig: go.Figure, width: float = 6*inch, height: float = 4*inch) -> Optional[Image]:
        """Create ReportLab Image object from Plotly figure."""
        try:
            img_data = self._generate_chart_image(fig, int(width*2), int(height*2))
            if img_data:
                # Extract base64 data
                img_base64 = img_data.split(",")[1]
                img_bytes = base64.b64decode(img_base64)
                img_buffer = io.BytesIO(img_bytes)
                return Image(img_buffer, width=width, height=height)
        except Exception as e:
            logger.error(f"Error creating ReportLab image: {e}")
        return None

    # DCF Chart Generation Methods
    def create_dcf_revenue_projection_chart(self, financial_projections: List[Dict[str, Any]]) -> go.Figure:
        """Create revenue projection line chart for DCF analysis."""
        try:
            if not financial_projections:
                return go.Figure()

            years = [proj.get("year", i) for i, proj in enumerate(financial_projections, 1)]
            revenues = [proj.get("revenue", 0) for proj in financial_projections]

            fig = go.Figure()

            # Revenue projection line
            fig.add_trace(go.Scatter(
                x=years,
                y=revenues,
                mode='lines+markers',
                name='Revenue Projection',
                line=dict(color=self.colors["primary"], width=3),
                marker=dict(size=8, color=self.colors["primary"])
            ))

            # Add growth rate annotations if available
            for i, proj in enumerate(financial_projections):
                if "revenue_growth_rate" in proj and i > 0:
                    growth_rate = proj["revenue_growth_rate"] * 100
                    fig.add_annotation(
                        x=years[i],
                        y=revenues[i],
                        text=f"+{growth_rate:.1f}%",
                        showarrow=True,
                        arrowhead=2,
                        arrowsize=1,
                        arrowwidth=2,
                        arrowcolor=self.colors["success"],
                        font=dict(size=10, color=self.colors["success"])
                    )

            fig = self._apply_professional_styling(fig, "Revenue Projections (5-Year Forecast)")
            fig.update_layout(
                yaxis_title="Revenue ($)",
                yaxis_tickformat="$,.0f",
                xaxis_title="Year"
            )

            return fig

        except Exception as e:
            logger.error(f"Error creating revenue projection chart: {e}")
            return go.Figure()

    def create_dcf_cash_flow_waterfall_chart(self, financial_projections: List[Dict[str, Any]]) -> go.Figure:
        """Create cash flow waterfall chart showing operating to free cash flow."""
        try:
            if not financial_projections:
                return go.Figure()

            # Use the latest year's data for waterfall
            latest_proj = financial_projections[-1] if financial_projections else {}

            # Build waterfall components
            categories = ["Revenue", "Operating Expenses", "EBITDA", "Taxes", "CapEx", "Working Capital", "Free Cash Flow"]
            values = [
                latest_proj.get("revenue", 0),
                -latest_proj.get("operating_expenses", 0),
                latest_proj.get("ebitda", 0),
                -latest_proj.get("taxes", 0),
                -latest_proj.get("capex", 0),
                -latest_proj.get("working_capital_change", 0),
                latest_proj.get("free_cash_flow", 0)
            ]

            # Create waterfall chart
            fig = go.Figure(go.Waterfall(
                name="Cash Flow",
                orientation="v",
                measure=["absolute", "relative", "total", "relative", "relative", "relative", "total"],
                x=categories,
                textposition="outside",
                text=[f"${v:,.0f}" for v in values],
                y=values,
                connector={"line": {"color": "rgb(63, 63, 63)"}},
                increasing={"marker": {"color": self.colors["success"]}},
                decreasing={"marker": {"color": self.colors["danger"]}},
                totals={"marker": {"color": self.colors["primary"]}}
            ))

            fig = self._apply_professional_styling(fig, "Cash Flow Waterfall Analysis")
            fig.update_layout(
                yaxis_title="Amount ($)",
                yaxis_tickformat="$,.0f",
                xaxis_title="Cash Flow Components"
            )

            return fig

        except Exception as e:
            logger.error(f"Error creating cash flow waterfall chart: {e}")
            return go.Figure()

    def create_dcf_sensitivity_tornado_chart(self, sensitivity_data: Dict[str, Any]) -> go.Figure:
        """Create tornado diagram for sensitivity analysis."""
        try:
            if not sensitivity_data:
                return go.Figure()

            # Extract sensitivity variables and their impacts
            variables = []
            low_impacts = []
            high_impacts = []

            for var_name, var_data in sensitivity_data.items():
                if isinstance(var_data, dict) and "impact" in var_data:
                    variables.append(var_name.replace("_", " ").title())
                    impacts = var_data["impact"]
                    if isinstance(impacts, list) and len(impacts) >= 2:
                        low_impacts.append(min(impacts))
                        high_impacts.append(max(impacts))
                    else:
                        low_impacts.append(-5)  # Default values
                        high_impacts.append(5)

            if not variables:
                return go.Figure()

            fig = go.Figure()

            # Add bars for negative impact (left side)
            fig.add_trace(go.Bar(
                y=variables,
                x=low_impacts,
                orientation='h',
                name='Downside Impact',
                marker_color=self.colors["danger"],
                text=[f"{x:.1f}%" for x in low_impacts],
                textposition='auto'
            ))

            # Add bars for positive impact (right side)
            fig.add_trace(go.Bar(
                y=variables,
                x=high_impacts,
                orientation='h',
                name='Upside Impact',
                marker_color=self.colors["success"],
                text=[f"+{x:.1f}%" for x in high_impacts],
                textposition='auto'
            ))

            fig = self._apply_professional_styling(fig, "Sensitivity Analysis - Tornado Diagram")
            fig.update_layout(
                xaxis_title="Impact on Valuation (%)",
                xaxis_zeroline=True,
                xaxis_zerolinecolor='black',
                yaxis_title="Key Variables",
                barmode='overlay'
            )


            return fig

        except Exception as e:
            logger.error(f"Error creating sensitivity tornado chart: {e}")
            return go.Figure()

    def create_dcf_monte_carlo_distribution_chart(self, monte_carlo_data: Dict[str, Any]) -> go.Figure:
        """Create histogram showing Monte Carlo valuation distribution."""
        try:
            if not monte_carlo_data or "error" in monte_carlo_data:
                return go.Figure()

            # Extract simulation results
            valuations = monte_carlo_data.get("simulation_results", [])
            if not valuations:
                # Generate sample data if not available
                mean_val = monte_carlo_data.get("mean_valuation", 100000000)
                std_val = monte_carlo_data.get("std_deviation", 20000000)
                valuations = np.random.normal(mean_val, std_val, 1000).tolist()

            fig = go.Figure()

            # Create histogram
            fig.add_trace(go.Histogram(
                x=valuations,
                nbinsx=30,
                name='Valuation Distribution',
                marker_color=self.colors["primary"],
                opacity=0.7
            ))

            # Add mean line
            mean_val = monte_carlo_data.get("mean_valuation", np.mean(valuations))
            fig.add_vline(
                x=mean_val,
                line_dash="dash",
                line_color=self.colors["danger"],
                annotation_text=f"Mean: ${mean_val:,.0f}"
            )

            # Add percentile lines
            p10 = monte_carlo_data.get("percentile_10", np.percentile(valuations, 10))
            p90 = monte_carlo_data.get("percentile_90", np.percentile(valuations, 90))

            fig.add_vline(
                x=p10,
                line_dash="dot",
                line_color=self.colors["warning"],
                annotation_text=f"P10: ${p10:,.0f}"
            )

            fig.add_vline(
                x=p90,
                line_dash="dot",
                line_color=self.colors["success"],
                annotation_text=f"P90: ${p90:,.0f}"
            )

            fig = self._apply_professional_styling(fig, "Monte Carlo Simulation - Valuation Distribution")
            fig.update_layout(
                xaxis_title="Enterprise Value ($)",
                xaxis_tickformat="$,.0f",
                yaxis_title="Frequency"
            )

            return fig

        except Exception as e:
            logger.error(f"Error creating Monte Carlo distribution chart: {e}")
            return go.Figure()

    # SaaS Chart Generation Methods
    def create_saas_arr_growth_chart(self, saas_data: Dict[str, Any]) -> go.Figure:
        """Create ARR growth trajectory chart."""
        try:
            key_metrics = saas_data.get("key_metrics", {})

            # Generate sample historical and projected ARR data
            current_arr = key_metrics.get("arr", 1000000)
            growth_rate_raw = key_metrics.get("growth_rate", 20)
            # Ensure growth_rate is numeric
            if isinstance(growth_rate_raw, str):
                try:
                    growth_rate = float(growth_rate_raw) / 100
                except ValueError:
                    growth_rate = 0.20  # Default 20%
            else:
                growth_rate = float(growth_rate_raw) / 100

            # Create 24 months of data (12 historical + 12 projected)
            months = []
            arr_values = []

            for i in range(-12, 13):  # -12 to +12 months
                if i <= 0:
                    # Historical data (declining backwards)
                    arr = current_arr * ((1 + growth_rate/12) ** i)
                    if i == 0:
                        months.append("Current")
                    else:
                        months.append(f"M{i}")
                else:
                    # Projected data
                    arr = current_arr * ((1 + growth_rate/12) ** i)
                    months.append(f"M+{i}")
                arr_values.append(arr)

            fig = go.Figure()

            # Historical ARR
            fig.add_trace(go.Scatter(
                x=months[:13],  # -12 to 0
                y=arr_values[:13],
                mode='lines+markers',
                name='Historical ARR',
                line=dict(color=self.colors["primary"], width=3),
                marker=dict(size=6)
            ))

            # Projected ARR
            fig.add_trace(go.Scatter(
                x=months[12:],  # 0 to +12
                y=arr_values[12:],
                mode='lines+markers',
                name='Projected ARR',
                line=dict(color=self.colors["secondary"], width=3, dash='dash'),
                marker=dict(size=6)
            ))

            # Add current month marker
            try:
                fig.add_vline(
                    x="Current",
                    line_dash="dot",
                    line_color=self.colors["danger"]
                )
                # Add annotation separately to avoid issues
                fig.add_annotation(
                    x="Current",
                    y=current_arr,
                    text="Current",
                    showarrow=True,
                    arrowhead=2
                )
            except Exception:
                # Skip annotation if it causes issues
                pass

            fig = self._apply_professional_styling(fig, "ARR Growth Trajectory")
            fig.update_layout(
                yaxis_title="Annual Recurring Revenue ($)",
                yaxis_tickformat="$,.0f",
                xaxis_title="Time Period"
            )

            return fig

        except Exception as e:
            logger.error(f"Error creating ARR growth chart: {e}")
            return go.Figure()

    def create_saas_unit_economics_chart(self, saas_data: Dict[str, Any]) -> go.Figure:
        """Create unit economics funnel chart."""
        try:
            key_metrics = saas_data.get("key_metrics", {})

            # Extract unit economics data
            cac = key_metrics.get("customer_acquisition_cost", 150)
            ltv = key_metrics.get("customer_lifetime_value", 750)
            ltv_cac_ratio = key_metrics.get("ltv_cac_ratio", ltv/cac if cac > 0 else 5)
            payback_months = key_metrics.get("payback_period_months", 12)

            # Create funnel visualization
            categories = ["Customer Acquisition Cost", "Customer Lifetime Value", "LTV/CAC Ratio", "Payback Period"]
            values = [cac, ltv, ltv_cac_ratio * 100, payback_months * 10]  # Scale for visualization
            colors_list = [self.colors["danger"], self.colors["success"], self.colors["primary"], self.colors["warning"]]

            fig = go.Figure()

            # Create bar chart for unit economics
            fig.add_trace(go.Bar(
                x=categories,
                y=values,
                marker_color=colors_list,
                text=[f"${cac:.0f}", f"${ltv:.0f}", f"{ltv_cac_ratio:.1f}x", f"{payback_months:.0f} mo"],
                textposition='auto',
                name='Unit Economics'
            ))

            fig = self._apply_professional_styling(fig, "SaaS Unit Economics")
            fig.update_layout(
                yaxis_title="Value (Scaled for Visualization)",
                xaxis_title="Metrics"
            )

            return fig

        except Exception as e:
            logger.error(f"Error creating unit economics chart: {e}")
            return go.Figure()

    def create_saas_valuation_methods_comparison_chart(self, saas_data: Dict[str, Any]) -> go.Figure:
        """Create comparison chart of different valuation methods."""
        try:
            valuation_methods = saas_data.get("valuation_methods", {})

            if not valuation_methods:
                return go.Figure()

            methods = []
            valuations = []

            for method, data in valuation_methods.items():
                if "error" not in data:
                    method_name = method.replace("_", " ").title()
                    methods.append(method_name)

                    # Extract valuation based on method type
                    if method == "arr_multiple":
                        val = data.get("adjusted_valuation", 0)
                    elif method == "revenue_multiple":
                        val = data.get("valuation", 0)
                    elif method == "saas_dcf":
                        val = data.get("enterprise_value", 0)
                    elif method == "unit_economics":
                        val = data.get("total_valuation", 0)
                    else:
                        val = data.get("valuation", 0)

                    valuations.append(val)

            if not methods:
                return go.Figure()

            fig = go.Figure()

            # Create bar chart
            fig.add_trace(go.Bar(
                x=methods,
                y=valuations,
                marker_color=self.colors["primary"],
                text=[f"${v:,.0f}" for v in valuations],
                textposition='auto',
                name='Valuation Methods'
            ))

            # Add weighted average line if available
            weighted_val = saas_data.get("weighted_valuation", {})
            if weighted_val and "weighted_average_valuation" in weighted_val:
                avg_val = weighted_val["weighted_average_valuation"]
                fig.add_hline(
                    y=avg_val,
                    line_dash="dash",
                    line_color=self.colors["danger"],
                    annotation_text=f"Weighted Average: ${avg_val:,.0f}"
                )

            fig = self._apply_professional_styling(fig, "SaaS Valuation Methods Comparison")
            fig.update_layout(
                yaxis_title="Valuation ($)",
                yaxis_tickformat="$,.0f",
                xaxis_title="Valuation Method"
            )

            return fig

        except Exception as e:
            logger.error(f"Error creating valuation methods comparison chart: {e}")
            return go.Figure()

    # Financial Statement Chart Generation Methods
    def create_balance_sheet_asset_composition_chart(self, balance_sheet_data: Dict[str, Any]) -> go.Figure:
        """Create pie chart showing asset composition from balance sheet."""
        try:
            assets = balance_sheet_data.get("assets", {})
            if not assets:
                return go.Figure()

            # Extract asset categories and values
            asset_categories = []
            asset_values = []

            # Current assets
            current_assets = assets.get("current_assets", {})
            if current_assets:
                for asset_type, value in current_assets.items():
                    if isinstance(value, (int, float)) and value > 0:
                        asset_categories.append(asset_type.replace("_", " ").title())
                        asset_values.append(value)

            # Non-current assets
            non_current_assets = assets.get("non_current_assets", {})
            if non_current_assets:
                for asset_type, value in non_current_assets.items():
                    if isinstance(value, (int, float)) and value > 0:
                        asset_categories.append(asset_type.replace("_", " ").title())
                        asset_values.append(value)

            if not asset_categories:
                return self.create_fallback_chart("asset_composition", "No asset data available")

            fig = go.Figure(data=[go.Pie(
                labels=asset_categories,
                values=asset_values,
                hole=0.3,  # Donut chart
                textinfo='label+percent',
                textposition='auto',
                marker=dict(colors=[
                    self.colors["primary"], self.colors["secondary"], self.colors["success"],
                    self.colors["warning"], self.colors["info"], self.colors["neutral"]
                ])
            )])

            fig = self._apply_professional_styling(fig, "Asset Composition")
            return fig

        except Exception as e:
            logger.error(f"Error creating asset composition chart: {e}")
            return go.Figure()

    def create_debt_to_equity_trend_chart(self, financial_data: List[Dict[str, Any]]) -> go.Figure:
        """Create line chart showing debt-to-equity ratio trend over time."""
        try:
            if not financial_data:
                return go.Figure()

            periods = []
            debt_to_equity_ratios = []

            for period_data in financial_data:
                period = period_data.get("period", "N/A")
                balance_sheet = period_data.get("balance_sheet", {})

                # Calculate debt-to-equity ratio
                liabilities = balance_sheet.get("liabilities_and_equity", {})
                total_debt = liabilities.get("total_liabilities", 0)
                total_equity = liabilities.get("total_equity", 0)

                if total_equity > 0:
                    debt_to_equity = total_debt / total_equity
                    periods.append(period)
                    debt_to_equity_ratios.append(debt_to_equity)

            if not periods:
                return self.create_fallback_chart("debt_to_equity_trend", "Insufficient data for trend analysis")

            fig = go.Figure()

            fig.add_trace(go.Scatter(
                x=periods,
                y=debt_to_equity_ratios,
                mode='lines+markers',
                name='Debt-to-Equity Ratio',
                line=dict(color=self.colors["primary"], width=3),
                marker=dict(size=8, color=self.colors["primary"])
            ))

            # Add benchmark line (industry average)
            avg_ratio = sum(debt_to_equity_ratios) / len(debt_to_equity_ratios)
            fig.add_hline(
                y=avg_ratio,
                line_dash="dash",
                line_color=self.colors["neutral"],
                annotation_text=f"Average: {avg_ratio:.2f}"
            )

            fig = self._apply_professional_styling(fig, "Debt-to-Equity Ratio Trend")
            fig.update_layout(
                yaxis_title="Debt-to-Equity Ratio",
                xaxis_title="Period"
            )

            return fig

        except Exception as e:
            logger.error(f"Error creating debt-to-equity trend chart: {e}")
            return go.Figure()

    def create_revenue_breakdown_chart(self, income_statement_data: Dict[str, Any]) -> go.Figure:
        """Create bar chart showing revenue breakdown by segment."""
        try:
            revenue_data = income_statement_data.get("revenue", {})
            if not revenue_data:
                return go.Figure()

            segments = []
            revenues = []

            # Extract revenue segments
            for segment, value in revenue_data.items():
                if isinstance(value, (int, float)) and value > 0:
                    segments.append(segment.replace("_", " ").title())
                    revenues.append(value)

            if not segments:
                return self.create_fallback_chart("revenue_breakdown", "No revenue segment data available")

            fig = go.Figure()

            fig.add_trace(go.Bar(
                x=segments,
                y=revenues,
                marker_color=self.colors["primary"],
                text=[f"${r:,.0f}" for r in revenues],
                textposition='auto',
                name='Revenue by Segment'
            ))

            fig = self._apply_professional_styling(fig, "Revenue Breakdown by Segment")
            fig.update_layout(
                yaxis_title="Revenue ($)",
                yaxis_tickformat="$,.0f",
                xaxis_title="Business Segment"
            )

            return fig

        except Exception as e:
            logger.error(f"Error creating revenue breakdown chart: {e}")
            return go.Figure()

    def create_margin_analysis_chart(self, financial_data: List[Dict[str, Any]]) -> go.Figure:
        """Create line chart showing margin analysis over time."""
        try:
            if not financial_data:
                return go.Figure()

            periods = []
            gross_margins = []
            operating_margins = []
            net_margins = []

            for period_data in financial_data:
                period = period_data.get("period", "N/A")
                income_statement = period_data.get("income_statement", {})

                # Extract financial metrics
                revenue = income_statement.get("revenue", {}).get("total_revenue", 0)
                gross_profit = income_statement.get("gross_profit", 0)
                operating_income = income_statement.get("operating_income", 0)
                net_income = income_statement.get("net_income", 0)

                if revenue > 0:
                    periods.append(period)
                    gross_margins.append((gross_profit / revenue) * 100)
                    operating_margins.append((operating_income / revenue) * 100)
                    net_margins.append((net_income / revenue) * 100)

            if not periods:
                return self.create_fallback_chart("margin_analysis", "Insufficient data for margin analysis")

            fig = go.Figure()

            # Gross margin
            fig.add_trace(go.Scatter(
                x=periods,
                y=gross_margins,
                mode='lines+markers',
                name='Gross Margin',
                line=dict(color=self.colors["success"], width=3),
                marker=dict(size=6)
            ))

            # Operating margin
            fig.add_trace(go.Scatter(
                x=periods,
                y=operating_margins,
                mode='lines+markers',
                name='Operating Margin',
                line=dict(color=self.colors["primary"], width=3),
                marker=dict(size=6)
            ))

            # Net margin
            fig.add_trace(go.Scatter(
                x=periods,
                y=net_margins,
                mode='lines+markers',
                name='Net Margin',
                line=dict(color=self.colors["warning"], width=3),
                marker=dict(size=6)
            ))

            fig = self._apply_professional_styling(fig, "Profitability Margin Analysis")
            fig.update_layout(
                yaxis_title="Margin (%)",
                yaxis_tickformat=".1f",
                xaxis_title="Period"
            )

            return fig

        except Exception as e:
            logger.error(f"Error creating margin analysis chart: {e}")
            return go.Figure()

    def create_working_capital_components_chart(self, balance_sheet_data: Dict[str, Any]) -> go.Figure:
        """Create stacked bar chart showing working capital components."""
        try:
            assets = balance_sheet_data.get("assets", {})
            liabilities = balance_sheet_data.get("liabilities_and_equity", {})

            if not assets or not liabilities:
                return go.Figure()

            current_assets = assets.get("current_assets", {})
            current_liabilities = liabilities.get("current_liabilities", {})

            # Working capital components
            components = ["Current Assets", "Current Liabilities", "Working Capital"]

            # Calculate values
            total_current_assets = sum(v for v in current_assets.values() if isinstance(v, (int, float)))
            total_current_liabilities = sum(v for v in current_liabilities.values() if isinstance(v, (int, float)))
            working_capital = total_current_assets - total_current_liabilities

            values = [total_current_assets, -total_current_liabilities, working_capital]
            colors = [self.colors["success"], self.colors["danger"], self.colors["primary"]]

            fig = go.Figure()

            fig.add_trace(go.Bar(
                x=components,
                y=values,
                marker_color=colors,
                text=[f"${v:,.0f}" for v in values],
                textposition='auto',
                name='Working Capital Analysis'
            ))

            # Add zero line
            fig.add_hline(y=0, line_color="black", line_width=1)

            fig = self._apply_professional_styling(fig, "Working Capital Components")
            fig.update_layout(
                yaxis_title="Amount ($)",
                yaxis_tickformat="$,.0f",
                xaxis_title="Component"
            )

            return fig

        except Exception as e:
            logger.error(f"Error creating working capital components chart: {e}")
            return go.Figure()

    def generate_financial_statement_charts(self, financial_data: Dict[str, Any]) -> Dict[str, go.Figure]:
        """Generate all financial statement charts."""
        charts = {}

        try:
            # Balance sheet charts
            if "balance_sheet" in financial_data:
                balance_sheet = financial_data["balance_sheet"]

                # Asset composition chart
                charts["asset_composition"] = self.create_balance_sheet_asset_composition_chart(balance_sheet)

                # Working capital components chart
                charts["working_capital"] = self.create_working_capital_components_chart(balance_sheet)

            # Income statement charts
            if "income_statement" in financial_data:
                income_statement = financial_data["income_statement"]

                # Revenue breakdown chart
                charts["revenue_breakdown"] = self.create_revenue_breakdown_chart(income_statement)

            # Multi-period analysis charts
            if "historical_data" in financial_data:
                historical_data = financial_data["historical_data"]

                # Debt-to-equity trend chart
                charts["debt_to_equity_trend"] = self.create_debt_to_equity_trend_chart(historical_data)

                # Margin analysis chart
                charts["margin_analysis"] = self.create_margin_analysis_chart(historical_data)

        except Exception as e:
            logger.error(f"Error generating financial statement charts: {e}")

        return charts

    # Main Chart Generation Methods
    def generate_dcf_charts(self, dcf_data: Dict[str, Any]) -> Dict[str, go.Figure]:
        """Generate all DCF-related charts."""
        charts = {}

        try:
            # Revenue projection chart
            if "financial_projections" in dcf_data:
                charts["revenue_projection"] = self.create_dcf_revenue_projection_chart(
                    dcf_data["financial_projections"]
                )
                charts["cash_flow_waterfall"] = self.create_dcf_cash_flow_waterfall_chart(
                    dcf_data["financial_projections"]
                )

            # Sensitivity analysis chart
            if "sensitivity_analysis" in dcf_data:
                charts["sensitivity_tornado"] = self.create_dcf_sensitivity_tornado_chart(
                    dcf_data["sensitivity_analysis"]
                )

            # Monte Carlo simulation chart
            if "monte_carlo_simulation" in dcf_data:
                charts["monte_carlo_distribution"] = self.create_dcf_monte_carlo_distribution_chart(
                    dcf_data["monte_carlo_simulation"]
                )

        except Exception as e:
            logger.error(f"Error generating DCF charts: {e}")

        return charts

    def generate_saas_charts(self, saas_data: Dict[str, Any]) -> Dict[str, go.Figure]:
        """Generate all SaaS-related charts."""
        charts = {}

        try:
            # ARR growth chart
            charts["arr_growth"] = self.create_saas_arr_growth_chart(saas_data)

            # Unit economics chart
            charts["unit_economics"] = self.create_saas_unit_economics_chart(saas_data)

            # Valuation methods comparison
            if "valuation_methods" in saas_data:
                charts["valuation_comparison"] = self.create_saas_valuation_methods_comparison_chart(saas_data)

        except Exception as e:
            logger.error(f"Error generating SaaS charts: {e}")

        return charts

    def create_excel_chart_from_data(self, ws, data: List[List], chart_type: str,
                                   title: str, position: str = "E5") -> None:
        """Create Excel chart object from data and add to worksheet."""
        try:
            if not data or len(data) < 2:
                return

            # Determine data range
            max_row = len(data)
            max_col = len(data[0]) if data else 1

            # Create appropriate chart type
            if chart_type == "line":
                chart = LineChart()
            elif chart_type == "bar":
                chart = BarChart()
            elif chart_type == "pie":
                chart = PieChart()
            else:
                chart = LineChart()  # Default

            # Set chart properties
            chart.title = title
            chart.style = 10  # Professional style
            chart.height = 10  # Height in cm
            chart.width = 16   # Width in cm

            # Add data to chart
            data_ref = Reference(ws, min_col=2, min_row=1, max_col=max_col, max_row=max_row)
            cats_ref = Reference(ws, min_col=1, min_row=2, max_row=max_row)

            chart.add_data(data_ref, titles_from_data=True)
            chart.set_categories(cats_ref)

            # Add chart to worksheet
            ws.add_chart(chart, position)

        except Exception as e:
            logger.error(f"Error creating Excel chart: {e}")

    def prepare_dcf_chart_data_for_excel(self, dcf_data: Dict[str, Any]) -> Dict[str, List[List]]:
        """Prepare DCF data for Excel chart creation."""
        chart_data = {}

        try:
            # Revenue projection data
            if "financial_projections" in dcf_data:
                projections = dcf_data["financial_projections"]
                if projections:
                    revenue_data = [["Year", "Revenue", "Free Cash Flow"]]
                    for proj in projections:
                        revenue_data.append([
                            f"Year {proj.get('year', 0)}",
                            proj.get('revenue', 0),
                            proj.get('free_cash_flow', 0)
                        ])
                    chart_data["revenue_projection"] = revenue_data

            # Sensitivity analysis data
            if "sensitivity_analysis" in dcf_data:
                sensitivity = dcf_data["sensitivity_analysis"]
                sens_data = [["Variable", "Low Impact", "High Impact"]]
                for var_name, var_data in sensitivity.items():
                    if isinstance(var_data, dict) and "impact" in var_data:
                        impacts = var_data["impact"]
                        if isinstance(impacts, list) and len(impacts) >= 2:
                            sens_data.append([
                                var_name.replace("_", " ").title(),
                                min(impacts),
                                max(impacts)
                            ])
                chart_data["sensitivity_analysis"] = sens_data

        except Exception as e:
            logger.error(f"Error preparing DCF chart data: {e}")

        return chart_data

    def prepare_saas_chart_data_for_excel(self, saas_data: Dict[str, Any]) -> Dict[str, List[List]]:
        """Prepare SaaS data for Excel chart creation."""
        chart_data = {}

        try:
            # Unit economics data
            key_metrics = saas_data.get("key_metrics", {})
            unit_econ_data = [["Metric", "Value"]]
            unit_econ_data.append(["CAC", key_metrics.get("customer_acquisition_cost", 150)])
            unit_econ_data.append(["LTV", key_metrics.get("customer_lifetime_value", 750)])
            unit_econ_data.append(["LTV/CAC Ratio", key_metrics.get("ltv_cac_ratio", 5)])
            chart_data["unit_economics"] = unit_econ_data

            # Valuation methods comparison
            valuation_methods = saas_data.get("valuation_methods", {})
            if valuation_methods:
                val_data = [["Method", "Valuation"]]
                for method, data in valuation_methods.items():
                    if "error" not in data:
                        method_name = method.replace("_", " ").title()
                        if method == "arr_multiple":
                            val = data.get("adjusted_valuation", 0)
                        elif method == "revenue_multiple":
                            val = data.get("valuation", 0)
                        elif method == "saas_dcf":
                            val = data.get("enterprise_value", 0)
                        elif method == "unit_economics":
                            val = data.get("total_valuation", 0)
                        else:
                            val = data.get("valuation", 0)
                        val_data.append([method_name, val])
                chart_data["valuation_comparison"] = val_data

        except Exception as e:
            logger.error(f"Error preparing SaaS chart data: {e}")

        return chart_data

    def apply_financial_theme(self, fig: go.Figure, theme: str = "professional") -> go.Figure:
        """Apply specific financial industry themes to charts."""
        themes = {
            "professional": {
                "color_sequence": [self.colors["primary"], self.colors["secondary"],
                                 self.colors["success"], self.colors["danger"],
                                 self.colors["warning"], self.colors["info"]],
                "background": "white",
                "grid_color": "#e6e6e6",
                "font_family": "Arial, sans-serif"
            },
            "dark": {
                "color_sequence": ["#1f77b4", "#ff7f0e", "#2ca02c", "#d62728", "#9467bd", "#8c564b"],
                "background": "#2f2f2f",
                "grid_color": "#404040",
                "font_family": "Arial, sans-serif"
            },
            "minimal": {
                "color_sequence": ["#333333", "#666666", "#999999", "#cccccc"],
                "background": "white",
                "grid_color": "#f0f0f0",
                "font_family": "Helvetica, sans-serif"
            }
        }

        if theme not in themes:
            theme = "professional"

        theme_config = themes[theme]

        fig.update_layout(
            plot_bgcolor=theme_config["background"],
            paper_bgcolor=theme_config["background"],
            font_family=theme_config["font_family"],
            colorway=theme_config["color_sequence"]
        )

        fig.update_layout(
            xaxis_gridcolor=theme_config["grid_color"],
            yaxis_gridcolor=theme_config["grid_color"]
        )

        return fig

    def add_financial_annotations(self, fig: go.Figure, data_type: str,
                                key_insights: Dict[str, Any] = None) -> go.Figure:
        """Add financial industry standard annotations to charts."""
        if not key_insights:
            return fig

        try:
            if data_type == "dcf_revenue":
                # Add CAGR annotation if available
                if "cagr" in key_insights:
                    cagr = key_insights["cagr"]
                    fig.add_annotation(
                        x=0.95, y=0.95,
                        xref="paper", yref="paper",
                        text=f"CAGR: {cagr:.1%}",
                        showarrow=False,
                        bgcolor="rgba(255,255,255,0.8)",
                        bordercolor="black",
                        borderwidth=1
                    )

            elif data_type == "saas_arr":
                # Add growth rate annotation
                if "growth_rate" in key_insights:
                    growth = key_insights["growth_rate"]
                    fig.add_annotation(
                        x=0.95, y=0.95,
                        xref="paper", yref="paper",
                        text=f"Growth Rate: {growth:.1%}",
                        showarrow=False,
                        bgcolor="rgba(255,255,255,0.8)",
                        bordercolor="black",
                        borderwidth=1
                    )

            elif data_type == "valuation_comparison":
                # Add weighted average line if available
                if "weighted_average" in key_insights:
                    avg_val = key_insights["weighted_average"]
                    fig.add_hline(
                        y=avg_val,
                        line_dash="dash",
                        line_color=self.colors["danger"],
                        annotation_text=f"Weighted Avg: ${avg_val:,.0f}"
                    )

        except Exception as e:
            logger.error(f"Error adding financial annotations: {e}")

        return fig

    def create_financial_dashboard(self, data: Dict[str, Any],
                                 dashboard_type: str = "dcf") -> go.Figure:
        """Create a comprehensive financial dashboard with multiple charts."""
        try:
            if dashboard_type == "dcf":
                return self._create_dcf_dashboard(data)
            elif dashboard_type == "saas":
                return self._create_saas_dashboard(data)
            else:
                return go.Figure()
        except Exception as e:
            logger.error(f"Error creating financial dashboard: {e}")
            return go.Figure()

    def _create_dcf_dashboard(self, dcf_data: Dict[str, Any]) -> go.Figure:
        """Create DCF-specific dashboard with multiple subplots."""
        try:
            fig = make_subplots(
                rows=2, cols=2,
                subplot_titles=("Revenue Projections", "Cash Flow Analysis",
                              "Sensitivity Analysis", "Monte Carlo Results"),
                specs=[[{"secondary_y": False}, {"secondary_y": False}],
                       [{"secondary_y": False}, {"secondary_y": False}]]
            )

            # Add revenue projection to subplot 1
            if "financial_projections" in dcf_data:
                projections = dcf_data["financial_projections"]
                years = [proj.get("year", i) for i, proj in enumerate(projections, 1)]
                revenues = [proj.get("revenue", 0) for proj in projections]

                fig.add_trace(
                    go.Scatter(x=years, y=revenues, name="Revenue",
                             line=dict(color=self.colors["primary"])),
                    row=1, col=1
                )

            # Add other subplots as needed...
            fig = self._apply_professional_styling(fig, "DCF Analysis Dashboard")
            return fig

        except Exception as e:
            logger.error(f"Error creating DCF dashboard: {e}")
            return go.Figure()

    def _create_saas_dashboard(self, saas_data: Dict[str, Any]) -> go.Figure:
        """Create SaaS-specific dashboard with multiple subplots."""
        try:
            fig = make_subplots(
                rows=2, cols=2,
                subplot_titles=("ARR Growth", "Unit Economics",
                              "Valuation Methods", "Key Metrics"),
                specs=[[{"secondary_y": False}, {"secondary_y": False}],
                       [{"secondary_y": False}, {"secondary_y": False}]]
            )

            # Add SaaS-specific charts to subplots...
            fig = self._apply_professional_styling(fig, "SaaS Analysis Dashboard")
            return fig

        except Exception as e:
            logger.error(f"Error creating SaaS dashboard: {e}")
            return go.Figure()

    # Data Validation and Error Handling Methods
    def validate_dcf_data(self, dcf_data: Dict[str, Any]) -> Dict[str, bool]:
        """Validate DCF data for chart generation."""
        validation_results = {
            "has_financial_projections": False,
            "has_sensitivity_analysis": False,
            "has_monte_carlo_data": False,
            "projections_valid": False,
            "sensitivity_valid": False,
            "monte_carlo_valid": False
        }

        try:
            # Check financial projections
            if "financial_projections" in dcf_data:
                projections = dcf_data["financial_projections"]
                validation_results["has_financial_projections"] = True

                if isinstance(projections, list) and len(projections) > 0:
                    # Check if projections have required fields
                    required_fields = ["year", "revenue", "free_cash_flow"]
                    first_proj = projections[0]
                    if all(field in first_proj for field in required_fields):
                        validation_results["projections_valid"] = True

            # Check sensitivity analysis
            if "sensitivity_analysis" in dcf_data:
                sensitivity = dcf_data["sensitivity_analysis"]
                validation_results["has_sensitivity_analysis"] = True

                if isinstance(sensitivity, dict) and len(sensitivity) > 0:
                    # Check if at least one variable has impact data
                    for var_data in sensitivity.values():
                        if isinstance(var_data, dict) and "impact" in var_data:
                            validation_results["sensitivity_valid"] = True
                            break

            # Check Monte Carlo data
            if "monte_carlo_simulation" in dcf_data:
                mc_data = dcf_data["monte_carlo_simulation"]
                validation_results["has_monte_carlo_data"] = True

                if isinstance(mc_data, dict) and "error" not in mc_data:
                    required_mc_fields = ["mean_valuation", "std_deviation"]
                    if any(field in mc_data for field in required_mc_fields):
                        validation_results["monte_carlo_valid"] = True

        except Exception as e:
            logger.error(f"Error validating DCF data: {e}")

        return validation_results

    def validate_saas_data(self, saas_data: Dict[str, Any]) -> Dict[str, bool]:
        """Validate SaaS data for chart generation."""
        validation_results = {
            "has_key_metrics": False,
            "has_valuation_methods": False,
            "key_metrics_valid": False,
            "valuation_methods_valid": False
        }

        try:
            # Check key metrics
            if "key_metrics" in saas_data:
                key_metrics = saas_data["key_metrics"]
                validation_results["has_key_metrics"] = True

                if isinstance(key_metrics, dict) and len(key_metrics) > 0:
                    # Check for essential SaaS metrics
                    essential_metrics = ["arr", "mrr", "growth_rate"]
                    if any(metric in key_metrics for metric in essential_metrics):
                        validation_results["key_metrics_valid"] = True

            # Check valuation methods
            if "valuation_methods" in saas_data:
                valuation_methods = saas_data["valuation_methods"]
                validation_results["has_valuation_methods"] = True

                if isinstance(valuation_methods, dict) and len(valuation_methods) > 0:
                    # Check if at least one method has valid data
                    for method_data in valuation_methods.values():
                        if isinstance(method_data, dict) and "error" not in method_data:
                            validation_results["valuation_methods_valid"] = True
                            break

        except Exception as e:
            logger.error(f"Error validating SaaS data: {e}")

        return validation_results

    def create_fallback_chart(self, chart_type: str, error_message: str = "") -> go.Figure:
        """Create a fallback chart when data is invalid or missing."""
        try:
            fig = go.Figure()

            # Add a text annotation explaining the issue
            fig.add_annotation(
                x=0.5, y=0.5,
                xref="paper", yref="paper",
                text=f"Chart Unavailable<br>{error_message}" if error_message else "Chart Unavailable<br>Insufficient data",
                showarrow=False,
                font=dict(size=16, color="gray"),
                align="center"
            )

            fig.update_layout(
                title=f"{chart_type.replace('_', ' ').title()} - Data Not Available",
                xaxis=dict(showgrid=False, showticklabels=False, zeroline=False),
                yaxis=dict(showgrid=False, showticklabels=False, zeroline=False),
                plot_bgcolor="white",
                paper_bgcolor="white"
            )

            return fig

        except Exception as e:
            logger.error(f"Error creating fallback chart: {e}")
            return go.Figure()

    def safe_chart_generation(self, chart_func, *args, **kwargs) -> go.Figure:
        """Safely execute chart generation with error handling."""
        try:
            return chart_func(*args, **kwargs)
        except Exception as e:
            logger.error(f"Error in chart generation: {e}")
            chart_name = getattr(chart_func, '__name__', 'Unknown Chart')
            return self.create_fallback_chart(chart_name, str(e))

    def sanitize_data(self, data: Any, data_type: str = "numeric") -> Any:
        """Sanitize data for chart generation."""
        try:
            if data_type == "numeric":
                if isinstance(data, (int, float)):
                    return data if not (np.isnan(data) or np.isinf(data)) else 0
                elif isinstance(data, str):
                    try:
                        return float(data.replace(',', '').replace('$', ''))
                    except ValueError:
                        return 0
                else:
                    return 0

            elif data_type == "string":
                return str(data) if data is not None else "N/A"

            elif data_type == "list":
                if isinstance(data, list):
                    return [self.sanitize_data(item, "numeric") for item in data]
                else:
                    return []

        except Exception as e:
            logger.error(f"Error sanitizing data: {e}")
            return 0 if data_type == "numeric" else "N/A" if data_type == "string" else []

        return data
