"""
Enhanced report generator with robust error handling and graceful degradation.

Integrates all hardening improvements: exception handling, resource monitoring,
robust file operations, and graceful degradation strategies.
"""

import logging
import json
import csv
import io
import time
from pathlib import Path
from typing import Dict, Any, List, Optional, Union, Callable
from contextlib import contextmanager

# Import our hardening components
from mcx3d_finance.exceptions import (
    ReportGenerationError,
    ReportOutputError,
    ReportMemoryError,
    ChartGenerationError,
    ValidationError,
    MCX3DResourceError
)
from mcx3d_finance.utils.resource_monitor import get_resource_monitor, ResourceMonitor
from mcx3d_finance.utils.report_logging import ReportLogger, PerformanceTracker
from mcx3d_finance.utils.robust_file_ops import get_robust_file_ops, RobustFileOperations
from mcx3d_finance.utils.graceful_degradation import (
    get_degradation_manager,
    GracefulDegradationManager,
    DegradationStrategy,
    ReportComplexity,
    OutputFormat
)
from mcx3d_finance.validation.report_validator import ReportValidator

# Import original generator for delegation
from mcx3d_finance.reporting.generator import ReportGenerator as OriginalReportGenerator

logger = logging.getLogger(__name__)


class EnhancedReportGenerator:
    """
    Enhanced report generator with comprehensive error handling and graceful degradation.
    
    Provides bulletproof report generation with automatic fallback strategies,
    resource monitoring, and detailed logging for mission-critical operations.
    """
    
    def __init__(
        self,
        degradation_strategy: Optional[DegradationStrategy] = None,
        enable_validation: bool = True,
        enable_resource_monitoring: bool = True
    ):
        """
        Initialize the enhanced report generator.
        
        Args:
            degradation_strategy: Graceful degradation configuration
            enable_validation: Enable input validation
            enable_resource_monitoring: Enable resource monitoring
        """
        # Initialize components
        self.original_generator = OriginalReportGenerator()
        self.resource_monitor = get_resource_monitor() if enable_resource_monitoring else None
        self.file_ops = get_robust_file_ops()
        self.degradation_manager = get_degradation_manager(degradation_strategy)
        self.validator = ReportValidator() if enable_validation else None
        self.logger = ReportLogger(__name__)
        
        # Configuration
        self.enable_validation = enable_validation
        self.enable_resource_monitoring = enable_resource_monitoring
        
        # Track generation statistics
        self.generation_stats = {
            'total_reports': 0,
            'successful_reports': 0,
            'failed_reports': 0,
            'degradation_used': 0,
            'format_fallbacks': 0,
            'average_generation_time': 0.0
        }
    
    def generate_balance_sheet(
        self,
        balance_sheet_data: Dict[str, Any],
        output_path: str,
        output_format: str = "pdf",
        complexity: Optional[ReportComplexity] = None,
        **kwargs
    ) -> Dict[str, Any]:
        """
        Generate balance sheet with comprehensive error handling.
        
        Args:
            balance_sheet_data: Balance sheet data
            output_path: Output file path
            output_format: Desired output format (pdf, excel, html, csv, json)
            complexity: Report complexity level (optional, auto-detected)
            **kwargs: Additional generation parameters
            
        Returns:
            Generation result with success status and metadata
        """
        return self._generate_report_with_degradation(
            report_type="balance_sheet",
            data=balance_sheet_data,
            output_path=output_path,
            output_format=output_format,
            complexity=complexity,
            **kwargs
        )
    
    def generate_income_statement(
        self,
        income_statement_data: Dict[str, Any],
        output_path: str,
        output_format: str = "pdf",
        complexity: Optional[ReportComplexity] = None,
        **kwargs
    ) -> Dict[str, Any]:
        """
        Generate income statement with comprehensive error handling.
        
        Args:
            income_statement_data: Income statement data
            output_path: Output file path
            output_format: Desired output format
            complexity: Report complexity level (optional)
            **kwargs: Additional generation parameters
            
        Returns:
            Generation result with success status and metadata
        """
        return self._generate_report_with_degradation(
            report_type="income_statement",
            data=income_statement_data,
            output_path=output_path,
            output_format=output_format,
            complexity=complexity,
            **kwargs
        )
    
    def generate_dcf_valuation(
        self,
        dcf_data: Dict[str, Any],
        output_path: str,
        output_format: str = "pdf",
        complexity: Optional[ReportComplexity] = None,
        **kwargs
    ) -> Dict[str, Any]:
        """
        Generate DCF valuation report with comprehensive error handling.
        
        Args:
            dcf_data: DCF valuation data
            output_path: Output file path
            output_format: Desired output format
            complexity: Report complexity level (optional)
            **kwargs: Additional generation parameters
            
        Returns:
            Generation result with success status and metadata
        """
        return self._generate_report_with_degradation(
            report_type="dcf_valuation",
            data=dcf_data,
            output_path=output_path,
            output_format=output_format,
            complexity=complexity,
            **kwargs
        )
    
    def generate_saas_valuation(
        self,
        saas_data: Dict[str, Any],
        output_path: str,
        output_format: str = "pdf",
        complexity: Optional[ReportComplexity] = None,
        **kwargs
    ) -> Dict[str, Any]:
        """
        Generate SaaS valuation report with comprehensive error handling.
        
        Args:
            saas_data: SaaS valuation data
            output_path: Output file path
            output_format: Desired output format
            complexity: Report complexity level (optional)
            **kwargs: Additional generation parameters
            
        Returns:
            Generation result with success status and metadata
        """
        return self._generate_report_with_degradation(
            report_type="saas_valuation",
            data=saas_data,
            output_path=output_path,
            output_format=output_format,
            complexity=complexity,
            **kwargs
        )
    
    @contextmanager
    def resource_aware_generation(self, report_type: str, estimated_complexity: str = "standard"):
        """
        Context manager for resource-aware report generation.
        
        Args:
            report_type: Type of report being generated
            estimated_complexity: Estimated complexity level
            
        Yields:
            Resource usage snapshot
        """
        if not self.resource_monitor:
            yield None
            return
        
        # Set resource limits based on report complexity
        memory_limits = {
            "simple": 256,
            "standard": 512,
            "complex": 1024
        }
        
        memory_limit = memory_limits.get(estimated_complexity, 512)
        
        with self.resource_monitor.monitor_operation(
            f"report_generation_{report_type}",
            memory_limit_override=memory_limit,
            timeout_override=300  # 5 minutes default timeout
        ) as usage:
            yield usage
    
    def _generate_report_with_degradation(
        self,
        report_type: str,
        data: Dict[str, Any],
        output_path: str,
        output_format: str = "pdf",
        complexity: Optional[ReportComplexity] = None,
        **kwargs
    ) -> Dict[str, Any]:
        """
        Core report generation with graceful degradation.
        
        This method orchestrates the entire generation process with comprehensive
        error handling and automatic fallback strategies.
        """
        # Initialize result structure
        result = {
            'success': False,
            'report_type': report_type,
            'output_path': None,
            'format': output_format,
            'complexity': complexity or ReportComplexity.FULL,
            'degradation_applied': [],
            'warnings': [],
            'errors': [],
            'generation_time_seconds': 0,
            'file_size_mb': 0,
            'resource_usage': None,
            'validation_results': None
        }
        
        start_time = time.time()
        self.generation_stats['total_reports'] += 1
        
        try:
            # Step 1: Log report generation request
            self.logger.log_report_request(
                report_type=report_type,
                config={
                    'output_format': output_format,
                    'complexity': complexity.value if complexity else 'auto',
                    'output_path': output_path
                },
                organization_id=data.get('header', {}).get('organization_id'),
                user_id=kwargs.get('user_id')
            )
            
            # Step 2: Validate input data if validation is enabled
            if self.enable_validation and self.validator:
                try:
                    validation_results = self._validate_report_data(report_type, data)
                    result['validation_results'] = validation_results
                    
                    if not validation_results['is_valid']:
                        result['errors'].extend(validation_results['errors'])
                        result['warnings'].extend(validation_results['warnings'])
                        
                        # Decide whether to proceed with warnings or fail
                        if validation_results['has_critical_errors']:
                            raise ValidationError(
                                "Critical validation errors prevent report generation",
                                field_name="input_data",
                                validation_errors=validation_results['errors']
                            )
                        else:
                            result['warnings'].append("Proceeding with validation warnings")
                            
                except Exception as e:
                    if isinstance(e, ValidationError):
                        raise
                    logger.warning(f"Validation error (proceeding): {e}")
                    result['warnings'].append(f"Validation warning: {str(e)}")
            
            # Step 3: Use graceful degradation for generation
            with self.resource_aware_generation(report_type, "standard") as resource_usage:
                if resource_usage:
                    result['resource_usage'] = {
                        'memory_mb': resource_usage.memory_mb,
                        'cpu_percent': resource_usage.cpu_percent,
                        'disk_free_mb': resource_usage.disk_free_mb
                    }
                
                # Select appropriate generator function
                generator_func = self._get_generator_function(report_type, output_format)
                
                # Execute with graceful degradation
                degradation_result = self.degradation_manager.execute_with_graceful_degradation(
                    primary_generator=generator_func,
                    data=data,
                    output_path=output_path,
                    requested_format=output_format,
                    complexity=complexity,
                    **kwargs
                )
                
                # Merge degradation results
                result.update(degradation_result)
                
                # Update statistics
                if result['success']:
                    self.generation_stats['successful_reports'] += 1
                    if result['degradation_applied']:
                        self.generation_stats['degradation_used'] += 1
                    if result['format'] != output_format:
                        self.generation_stats['format_fallbacks'] += 1
                else:
                    self.generation_stats['failed_reports'] += 1
                
                # Log success
                if result['success']:
                    self.logger.log_report_success(
                        output_path=result['output_path'],
                        file_size_mb=result.get('file_size_mb', 0),
                        generation_time_seconds=result.get('generation_time_seconds', 0),
                        additional_metrics={
                            'format': result['format'],
                            'complexity': result['complexity'].value if isinstance(result['complexity'], ReportComplexity) else str(result['complexity']),
                            'degradation_applied': len(result.get('degradation_applied', []))
                        }
                    )
                
                return result
            
        except Exception as e:
            # Comprehensive error handling and logging
            result.update({
                'success': False,
                'generation_time_seconds': time.time() - start_time,
                'errors': [str(e)]
            })
            
            self.generation_stats['failed_reports'] += 1
            
            # Log the error with context
            self.logger.log_report_error(
                error=e,
                context={
                    'report_type': report_type,
                    'output_format': output_format,
                    'output_path': output_path
                },
                stage="report_generation"
            )
            
            # Try to provide helpful error information
            if isinstance(e, ReportMemoryError):
                result['warnings'].append("Report generation failed due to memory constraints. Try reducing data size or complexity.")
            elif isinstance(e, MCX3DResourceError):
                result['warnings'].append("Report generation failed due to resource constraints. Check disk space and system resources.")
            elif isinstance(e, ValidationError):
                result['warnings'].append("Report generation failed due to data validation errors. Check input data quality.")
            else:
                result['warnings'].append("Report generation failed due to unexpected error. Check system logs for details.")
            
            return result
        
        finally:
            # Update average generation time
            generation_time = time.time() - start_time
            total_reports = self.generation_stats['total_reports']
            current_avg = self.generation_stats['average_generation_time']
            self.generation_stats['average_generation_time'] = (
                (current_avg * (total_reports - 1) + generation_time) / total_reports
            )
    
    def _validate_report_data(self, report_type: str, data: Dict[str, Any]) -> Dict[str, Any]:
        """Validate report data based on report type."""
        try:
            if report_type == "balance_sheet":
                return self.validator.validate_balance_sheet_data(data)
            elif report_type == "income_statement":
                return self.validator.validate_income_statement_data(data)
            elif report_type == "dcf_valuation":
                return self.validator.validate_dcf_data(data)
            elif report_type == "saas_valuation":
                return self.validator.validate_saas_data(data)
            else:
                return {'is_valid': True, 'errors': [], 'warnings': [], 'has_critical_errors': False}
        except Exception as e:
            logger.warning(f"Validation process error: {e}")
            return {
                'is_valid': False,
                'errors': [f"Validation process error: {str(e)}"],
                'warnings': [],
                'has_critical_errors': False
            }
    
    def _get_generator_function(self, report_type: str, output_format: str) -> Callable:
        """Get appropriate generator function based on report type and format."""
        # Map report types and formats to generator methods
        generator_map = {
            ("balance_sheet", "pdf"): self._generate_balance_sheet_pdf,
            ("balance_sheet", "excel"): self._generate_balance_sheet_excel,
            ("balance_sheet", "csv"): self._generate_balance_sheet_csv,
            ("balance_sheet", "json"): self._generate_balance_sheet_json,
            ("income_statement", "pdf"): self._generate_income_statement_pdf,
            ("income_statement", "excel"): self._generate_income_statement_excel,
            ("income_statement", "csv"): self._generate_income_statement_csv,
            ("income_statement", "json"): self._generate_income_statement_json,
            ("dcf_valuation", "pdf"): self._generate_dcf_pdf,
            ("dcf_valuation", "excel"): self._generate_dcf_excel,
            ("dcf_valuation", "csv"): self._generate_dcf_csv,
            ("dcf_valuation", "json"): self._generate_dcf_json,
            ("saas_valuation", "pdf"): self._generate_saas_pdf,
            ("saas_valuation", "excel"): self._generate_saas_excel,
            ("saas_valuation", "csv"): self._generate_saas_csv,
            ("saas_valuation", "json"): self._generate_saas_json,
        }
        
        return generator_map.get((report_type, output_format), self._generate_generic_fallback)
    
    # Enhanced generator methods with robust file operations
    def _generate_balance_sheet_pdf(self, data: Dict[str, Any], output_path: str, **kwargs) -> str:
        """Generate balance sheet PDF with robust file operations."""
        try:
            # Use original generator but with our robust file operations
            with PerformanceTracker(self.logger, "balance_sheet_pdf_generation") as tracker:
                # Generate to temporary location first
                temp_path = output_path + ".tmp"
                
                # Use original generator
                self.original_generator.generate_balance_sheet_pdf(data, temp_path)
                
                # Safely move to final location
                final_path = self.file_ops.safe_copy_file(temp_path, output_path)
                
                # Clean up temp file
                try:
                    Path(temp_path).unlink()
                except:
                    pass
                
                # Track metrics
                file_size_mb = Path(final_path).stat().st_size / 1024 / 1024
                tracker.add_metric("file_size_mb", file_size_mb, "megabytes")
                
                return final_path
                
        except Exception as e:
            raise ReportGenerationError(
                f"Balance sheet PDF generation failed: {e}",
                report_type="balance_sheet",
                output_format="pdf"
            ).add_context("output_path", output_path)
    
    def _generate_balance_sheet_excel(self, data: Dict[str, Any], output_path: str, **kwargs) -> str:
        """Generate balance sheet Excel with robust file operations."""
        try:
            with PerformanceTracker(self.logger, "balance_sheet_excel_generation") as tracker:
                temp_path = output_path + ".tmp"
                self.original_generator.generate_balance_sheet_excel(data, temp_path)
                final_path = self.file_ops.safe_copy_file(temp_path, output_path)
                
                try:
                    Path(temp_path).unlink()
                except:
                    pass
                
                file_size_mb = Path(final_path).stat().st_size / 1024 / 1024
                tracker.add_metric("file_size_mb", file_size_mb, "megabytes")
                
                return final_path
                
        except Exception as e:
            raise ReportGenerationError(
                f"Balance sheet Excel generation failed: {e}",
                report_type="balance_sheet",
                output_format="excel"
            ).add_context("output_path", output_path)
    
    def _generate_balance_sheet_csv(self, data: Dict[str, Any], output_path: str, **kwargs) -> str:
        """Generate balance sheet CSV as fallback format."""
        try:
            # Extract key balance sheet data
            csv_data = self._extract_balance_sheet_csv_data(data)
            
            # Generate CSV content
            output = io.StringIO()
            writer = csv.writer(output)
            
            # Write header
            writer.writerow(["Account", "Current Period", "Prior Period"])
            
            # Write data rows
            for row in csv_data:
                writer.writerow(row)
            
            csv_content = output.getvalue()
            output.close()
            
            # Write with robust file operations
            return self.file_ops.safe_write_file(output_path, csv_content, mode='w')
            
        except Exception as e:
            raise ReportGenerationError(
                f"Balance sheet CSV generation failed: {e}",
                report_type="balance_sheet",
                output_format="csv"
            ).add_context("output_path", output_path)
    
    def _generate_balance_sheet_json(self, data: Dict[str, Any], output_path: str, **kwargs) -> str:
        """Generate balance sheet JSON as fallback format."""
        try:
            # Sanitize data for JSON output
            json_data = {
                "report_type": "balance_sheet",
                "generated_at": time.strftime('%Y-%m-%d %H:%M:%S UTC'),
                "data": self._sanitize_for_json(data)
            }
            
            # Write with robust file operations
            json_content = json.dumps(json_data, indent=2, ensure_ascii=False)
            return self.file_ops.safe_write_file(output_path, json_content, mode='w')
            
        except Exception as e:
            raise ReportGenerationError(
                f"Balance sheet JSON generation failed: {e}",
                report_type="balance_sheet",
                output_format="json"
            ).add_context("output_path", output_path)
    
    # Similar implementations for other report types...
    def _generate_income_statement_pdf(self, data: Dict[str, Any], output_path: str, **kwargs) -> str:
        """Generate income statement PDF with robust file operations."""
        try:
            with PerformanceTracker(self.logger, "income_statement_pdf_generation") as tracker:
                temp_path = output_path + ".tmp"
                self.original_generator.generate_income_statement_pdf(data, temp_path)
                final_path = self.file_ops.safe_copy_file(temp_path, output_path)
                
                try:
                    Path(temp_path).unlink()
                except:
                    pass
                
                file_size_mb = Path(final_path).stat().st_size / 1024 / 1024
                tracker.add_metric("file_size_mb", file_size_mb, "megabytes")
                
                return final_path
                
        except Exception as e:
            raise ReportGenerationError(
                f"Income statement PDF generation failed: {e}",
                report_type="income_statement",
                output_format="pdf"
            ).add_context("output_path", output_path)
    
    def _generate_income_statement_excel(self, data: Dict[str, Any], output_path: str, **kwargs) -> str:
        """Generate income statement Excel with robust file operations."""
        try:
            with PerformanceTracker(self.logger, "income_statement_excel_generation") as tracker:
                temp_path = output_path + ".tmp"
                self.original_generator.generate_income_statement_excel(data, temp_path)
                final_path = self.file_ops.safe_copy_file(temp_path, output_path)
                
                try:
                    Path(temp_path).unlink()
                except:
                    pass
                
                file_size_mb = Path(final_path).stat().st_size / 1024 / 1024
                tracker.add_metric("file_size_mb", file_size_mb, "megabytes")
                
                return final_path
                
        except Exception as e:
            raise ReportGenerationError(
                f"Income statement Excel generation failed: {e}",
                report_type="income_statement",
                output_format="excel"
            ).add_context("output_path", output_path)
    
    def _generate_income_statement_csv(self, data: Dict[str, Any], output_path: str, **kwargs) -> str:
        """Generate income statement CSV as fallback format."""
        try:
            csv_data = self._extract_income_statement_csv_data(data)
            
            output = io.StringIO()
            writer = csv.writer(output)
            writer.writerow(["Line Item", "Current Period", "Prior Period"])
            
            for row in csv_data:
                writer.writerow(row)
            
            csv_content = output.getvalue()
            output.close()
            
            return self.file_ops.safe_write_file(output_path, csv_content, mode='w')
            
        except Exception as e:
            raise ReportGenerationError(
                f"Income statement CSV generation failed: {e}",
                report_type="income_statement",
                output_format="csv"
            ).add_context("output_path", output_path)
    
    def _generate_income_statement_json(self, data: Dict[str, Any], output_path: str, **kwargs) -> str:
        """Generate income statement JSON as fallback format."""
        try:
            json_data = {
                "report_type": "income_statement",
                "generated_at": time.strftime('%Y-%m-%d %H:%M:%S UTC'),
                "data": self._sanitize_for_json(data)
            }
            
            json_content = json.dumps(json_data, indent=2, ensure_ascii=False)
            return self.file_ops.safe_write_file(output_path, json_content, mode='w')
            
        except Exception as e:
            raise ReportGenerationError(
                f"Income statement JSON generation failed: {e}",
                report_type="income_statement",
                output_format="json"
            ).add_context("output_path", output_path)
    
    # DCF and SaaS generators with similar patterns...
    def _generate_dcf_pdf(self, data: Dict[str, Any], output_path: str, **kwargs) -> str:
        """Generate DCF PDF with robust file operations."""
        try:
            with PerformanceTracker(self.logger, "dcf_pdf_generation") as tracker:
                temp_path = output_path + ".tmp"
                self.original_generator.generate_dcf_valuation_pdf(data, temp_path)
                final_path = self.file_ops.safe_copy_file(temp_path, output_path)
                
                try:
                    Path(temp_path).unlink()
                except:
                    pass
                
                file_size_mb = Path(final_path).stat().st_size / 1024 / 1024
                tracker.add_metric("file_size_mb", file_size_mb, "megabytes")
                
                return final_path
                
        except Exception as e:
            raise ReportGenerationError(
                f"DCF PDF generation failed: {e}",
                report_type="dcf_valuation",
                output_format="pdf"
            ).add_context("output_path", output_path)
    
    def _generate_dcf_excel(self, data: Dict[str, Any], output_path: str, **kwargs) -> str:
        """Generate DCF Excel with robust file operations."""
        try:
            with PerformanceTracker(self.logger, "dcf_excel_generation") as tracker:
                temp_path = output_path + ".tmp"
                self.original_generator.generate_dcf_valuation_excel(data, temp_path)
                final_path = self.file_ops.safe_copy_file(temp_path, output_path)
                
                try:
                    Path(temp_path).unlink()
                except:
                    pass
                
                file_size_mb = Path(final_path).stat().st_size / 1024 / 1024
                tracker.add_metric("file_size_mb", file_size_mb, "megabytes")
                
                return final_path
                
        except Exception as e:
            raise ReportGenerationError(
                f"DCF Excel generation failed: {e}",
                report_type="dcf_valuation",
                output_format="excel"
            ).add_context("output_path", output_path)
    
    def _generate_dcf_csv(self, data: Dict[str, Any], output_path: str, **kwargs) -> str:
        """Generate DCF CSV as fallback format."""
        try:
            csv_data = self._extract_dcf_csv_data(data)
            
            output = io.StringIO()
            writer = csv.writer(output)
            writer.writerow(["Metric", "Value", "Unit"])
            
            for row in csv_data:
                writer.writerow(row)
            
            csv_content = output.getvalue()
            output.close()
            
            return self.file_ops.safe_write_file(output_path, csv_content, mode='w')
            
        except Exception as e:
            raise ReportGenerationError(
                f"DCF CSV generation failed: {e}",
                report_type="dcf_valuation",
                output_format="csv"
            ).add_context("output_path", output_path)
    
    def _generate_dcf_json(self, data: Dict[str, Any], output_path: str, **kwargs) -> str:
        """Generate DCF JSON as fallback format."""
        try:
            json_data = {
                "report_type": "dcf_valuation",
                "generated_at": time.strftime('%Y-%m-%d %H:%M:%S UTC'),
                "data": self._sanitize_for_json(data)
            }
            
            json_content = json.dumps(json_data, indent=2, ensure_ascii=False)
            return self.file_ops.safe_write_file(output_path, json_content, mode='w')
            
        except Exception as e:
            raise ReportGenerationError(
                f"DCF JSON generation failed: {e}",
                report_type="dcf_valuation",
                output_format="json"
            ).add_context("output_path", output_path)
    
    def _generate_saas_pdf(self, data: Dict[str, Any], output_path: str, **kwargs) -> str:
        """Generate SaaS PDF with robust file operations."""
        try:
            with PerformanceTracker(self.logger, "saas_pdf_generation") as tracker:
                temp_path = output_path + ".tmp"
                self.original_generator.generate_saas_valuation_pdf(data, temp_path)
                final_path = self.file_ops.safe_copy_file(temp_path, output_path)
                
                try:
                    Path(temp_path).unlink()
                except:
                    pass
                
                file_size_mb = Path(final_path).stat().st_size / 1024 / 1024
                tracker.add_metric("file_size_mb", file_size_mb, "megabytes")
                
                return final_path
                
        except Exception as e:
            raise ReportGenerationError(
                f"SaaS PDF generation failed: {e}",
                report_type="saas_valuation",
                output_format="pdf"
            ).add_context("output_path", output_path)
    
    def _generate_saas_excel(self, data: Dict[str, Any], output_path: str, **kwargs) -> str:
        """Generate SaaS Excel with robust file operations."""
        try:
            with PerformanceTracker(self.logger, "saas_excel_generation") as tracker:
                temp_path = output_path + ".tmp"
                self.original_generator.generate_saas_valuation_excel(data, temp_path)
                final_path = self.file_ops.safe_copy_file(temp_path, output_path)
                
                try:
                    Path(temp_path).unlink()
                except:
                    pass
                
                file_size_mb = Path(final_path).stat().st_size / 1024 / 1024
                tracker.add_metric("file_size_mb", file_size_mb, "megabytes")
                
                return final_path
                
        except Exception as e:
            raise ReportGenerationError(
                f"SaaS Excel generation failed: {e}",
                report_type="saas_valuation",
                output_format="excel"
            ).add_context("output_path", output_path)
    
    def _generate_saas_csv(self, data: Dict[str, Any], output_path: str, **kwargs) -> str:
        """Generate SaaS CSV as fallback format."""
        try:
            csv_data = self._extract_saas_csv_data(data)
            
            output = io.StringIO()
            writer = csv.writer(output)
            writer.writerow(["Metric", "Value", "Unit"])
            
            for row in csv_data:
                writer.writerow(row)
            
            csv_content = output.getvalue()
            output.close()
            
            return self.file_ops.safe_write_file(output_path, csv_content, mode='w')
            
        except Exception as e:
            raise ReportGenerationError(
                f"SaaS CSV generation failed: {e}",
                report_type="saas_valuation",
                output_format="csv"
            ).add_context("output_path", output_path)
    
    def _generate_saas_json(self, data: Dict[str, Any], output_path: str, **kwargs) -> str:
        """Generate SaaS JSON as fallback format."""
        try:
            json_data = {
                "report_type": "saas_valuation",
                "generated_at": time.strftime('%Y-%m-%d %H:%M:%S UTC'),
                "data": self._sanitize_for_json(data)
            }
            
            json_content = json.dumps(json_data, indent=2, ensure_ascii=False)
            return self.file_ops.safe_write_file(output_path, json_content, mode='w')
            
        except Exception as e:
            raise ReportGenerationError(
                f"SaaS JSON generation failed: {e}",
                report_type="saas_valuation",
                output_format="json"
            ).add_context("output_path", output_path)
    
    def _generate_generic_fallback(self, data: Dict[str, Any], output_path: str, **kwargs) -> str:
        """Generic fallback generator for unsupported combinations."""
        try:
            # Generate a basic JSON export as last resort
            fallback_data = {
                "report_type": "generic_fallback",
                "generated_at": time.strftime('%Y-%m-%d %H:%M:%S UTC'),
                "message": "Generated using fallback method due to unsupported format/type combination",
                "data": self._sanitize_for_json(data)
            }
            
            # Ensure JSON extension
            fallback_path = str(Path(output_path).with_suffix('.fallback.json'))
            
            json_content = json.dumps(fallback_data, indent=2, ensure_ascii=False)
            return self.file_ops.safe_write_file(fallback_path, json_content, mode='w')
            
        except Exception as e:
            raise ReportGenerationError(
                f"Even generic fallback generation failed: {e}",
                report_type="generic_fallback",
                output_format="json"
            ).add_context("output_path", output_path)
    
    # Data extraction helpers for CSV generation
    def _extract_balance_sheet_csv_data(self, data: Dict[str, Any]) -> List[List[str]]:
        """Extract balance sheet data for CSV format."""
        rows = []
        try:
            assets = data.get('assets', {})
            liabilities_equity = data.get('liabilities_and_equity', {})
            
            # Add asset rows
            if assets:
                rows.append(['ASSETS', '', ''])
                for key, value in assets.items():
                    if isinstance(value, (int, float)):
                        rows.append([key.replace('_', ' ').title(), f"{value:,.2f}", ""])
            
            # Add liability and equity rows
            if liabilities_equity:
                rows.append(['LIABILITIES AND EQUITY', '', ''])
                for key, value in liabilities_equity.items():
                    if isinstance(value, (int, float)):
                        rows.append([key.replace('_', ' ').title(), f"{value:,.2f}", ""])
            
            return rows
        except Exception:
            return [['Error', 'Could not extract balance sheet data', '']]
    
    def _extract_income_statement_csv_data(self, data: Dict[str, Any]) -> List[List[str]]:
        """Extract income statement data for CSV format."""
        rows = []
        try:
            # Extract key income statement items
            items = [
                ('total_revenue', 'Total Revenue'),
                ('cost_of_goods_sold', 'Cost of Goods Sold'),
                ('gross_profit', 'Gross Profit'),
                ('operating_expenses', 'Operating Expenses'),
                ('operating_income', 'Operating Income'),
                ('net_income', 'Net Income')
            ]
            
            for key, label in items:
                value = data.get(key, 0)
                if isinstance(value, (int, float)):
                    rows.append([label, f"{value:,.2f}", ""])
            
            return rows
        except Exception:
            return [['Error', 'Could not extract income statement data', '']]
    
    def _extract_dcf_csv_data(self, data: Dict[str, Any]) -> List[List[str]]:
        """Extract DCF data for CSV format."""
        rows = []
        try:
            # DCF summary metrics
            summary = data.get('valuation_summary', {})
            for key, value in summary.items():
                if isinstance(value, (int, float)):
                    rows.append([key.replace('_', ' ').title(), f"{value:,.2f}", "USD"])
                else:
                    rows.append([key.replace('_', ' ').title(), str(value), ""])
            
            return rows
        except Exception:
            return [['Error', 'Could not extract DCF data', '']]
    
    def _extract_saas_csv_data(self, data: Dict[str, Any]) -> List[List[str]]:
        """Extract SaaS data for CSV format."""
        rows = []
        try:
            # SaaS metrics
            metrics = data.get('saas_metrics', {})
            for key, value in metrics.items():
                if isinstance(value, (int, float)):
                    unit = "USD" if "revenue" in key.lower() or "value" in key.lower() else ""
                    rows.append([key.replace('_', ' ').title(), f"{value:,.2f}", unit])
                else:
                    rows.append([key.replace('_', ' ').title(), str(value), ""])
            
            return rows
        except Exception:
            return [['Error', 'Could not extract SaaS data', '']]
    
    def _sanitize_for_json(self, data: Any) -> Any:
        """Sanitize data for JSON serialization."""
        try:
            if isinstance(data, dict):
                return {k: self._sanitize_for_json(v) for k, v in data.items()}
            elif isinstance(data, list):
                return [self._sanitize_for_json(item) for item in data]
            elif isinstance(data, (int, float, str, bool, type(None))):
                return data
            else:
                return str(data)
        except Exception:
            return "Data serialization error"
    
    def get_generation_statistics(self) -> Dict[str, Any]:
        """Get generation statistics for monitoring and analysis."""
        stats = self.generation_stats.copy()
        
        # Calculate success rate
        if stats['total_reports'] > 0:
            stats['success_rate'] = (stats['successful_reports'] / stats['total_reports']) * 100
        else:
            stats['success_rate'] = 0
        
        return stats
    
    def cleanup_resources(self):
        """Clean up any resources used by the generator."""
        try:
            if self.file_ops:
                self.file_ops.cleanup_temp_files()
            
            if self.resource_monitor:
                self.resource_monitor.cleanup_resources()
                
            logger.info("Enhanced report generator resources cleaned up")
            
        except Exception as e:
            logger.warning(f"Error during resource cleanup: {e}")


# Convenience function to create a configured enhanced generator
def create_enhanced_generator(
    memory_threshold_mb: float = 512.0,
    disk_threshold_mb: float = 1000.0,
    enable_validation: bool = True,
    enable_resource_monitoring: bool = True
) -> EnhancedReportGenerator:
    """
    Create a configured enhanced report generator.
    
    Args:
        memory_threshold_mb: Memory threshold for degradation
        disk_threshold_mb: Disk space threshold for degradation
        enable_validation: Enable input validation
        enable_resource_monitoring: Enable resource monitoring
        
    Returns:
        Configured EnhancedReportGenerator instance
    """
    degradation_strategy = DegradationStrategy(
        memory_threshold_mb=memory_threshold_mb,
        disk_threshold_mb=disk_threshold_mb,
        execution_time_threshold_seconds=300,  # 5 minutes
        format_priority=['pdf', 'excel', 'html', 'csv', 'json']
    )
    
    return EnhancedReportGenerator(
        degradation_strategy=degradation_strategy,
        enable_validation=enable_validation,
        enable_resource_monitoring=enable_resource_monitoring
    )