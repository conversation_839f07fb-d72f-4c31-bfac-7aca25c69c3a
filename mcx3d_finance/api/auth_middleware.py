"""
Authentication middleware for protecting API endpoints.
Implements JWT-based authentication with organization access control.
"""
import logging
from typing import Optional, Dict, Any
from datetime import datetime, timedelta, timezone
from functools import wraps

from fastapi import HTTPException, Request, Depends
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from jose import JWTError, jwt
from passlib.context import CryptContext

from ..core.config import get_security_config

logger = logging.getLogger(__name__)

# Security configuration
security_config = get_security_config()
SECRET_KEY = security_config.get("secret_key", "your-secret-key-here")
ALGORITHM = "HS256"
ACCESS_TOKEN_EXPIRE_MINUTES = 30

# Password hashing
pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")

# Bearer token security
security = HTTPBearer()


class AuthenticationError(HTTPException):
    """Custom authentication error."""
    def __init__(self, detail: str):
        super().__init__(status_code=401, detail=detail)


class AuthorizationError(HTTPException):
    """Custom authorization error."""
    def __init__(self, detail: str):
        super().__init__(status_code=403, detail=detail)


def create_access_token(data: dict, expires_delta: Optional[timedelta] = None):
    """
    Create JWT access token.
    
    Args:
        data: Token payload data
        expires_delta: Token expiration time
        
    Returns:
        Encoded JWT token
    """
    to_encode = data.copy()
    if expires_delta:
        expire = datetime.now(timezone.utc) + expires_delta
    else:
        expire = datetime.now(timezone.utc) + timedelta(minutes=ACCESS_TOKEN_EXPIRE_MINUTES)
    
    to_encode.update({"exp": expire})
    encoded_jwt = jwt.encode(to_encode, SECRET_KEY, algorithm=ALGORITHM)
    return encoded_jwt


def verify_token(token: str) -> Dict[str, Any]:
    """
    Verify and decode JWT token.
    
    Args:
        token: JWT token string
        
    Returns:
        Decoded token payload
        
    Raises:
        AuthenticationError: If token is invalid
    """
    try:
        payload = jwt.decode(token, SECRET_KEY, algorithms=[ALGORITHM])
        return payload
    except JWTError as e:
        logger.error(f"JWT verification failed: {e}")
        raise AuthenticationError("Invalid authentication token")


async def get_current_user(
    credentials: HTTPAuthorizationCredentials = Depends(security)
) -> Dict[str, Any]:
    """
    Get current authenticated user from JWT token.
    
    Args:
        credentials: Bearer token from request
        
    Returns:
        User information dict
        
    Raises:
        AuthenticationError: If authentication fails
    """
    token = credentials.credentials
    
    try:
        payload = verify_token(token)
        user_id = payload.get("sub")
        
        if user_id is None:
            raise AuthenticationError("Invalid token payload")
            
        # In a real implementation, fetch user from database
        # For now, return user info from token
        return {
            "user_id": user_id,
            "email": payload.get("email"),
            "organizations": payload.get("organizations", [])
        }
        
    except Exception as e:
        logger.error(f"Authentication failed: {e}")
        raise AuthenticationError("Could not validate credentials")


async def verify_organization_access(
    organization_id: int,
    current_user: Dict[str, Any] = Depends(get_current_user)
) -> bool:
    """
    Verify user has access to the specified organization.
    
    Args:
        organization_id: Organization to check access for
        current_user: Current authenticated user
        
    Returns:
        True if user has access
        
    Raises:
        AuthorizationError: If user lacks access
    """
    user_organizations = current_user.get("organizations", [])
    
    if organization_id not in user_organizations:
        logger.warning(
            f"User {current_user['user_id']} attempted to access "
            f"organization {organization_id} without permission"
        )
        raise AuthorizationError(
            f"You do not have access to organization {organization_id}"
        )
    
    return True


def require_auth(func):
    """
    Decorator to require authentication for an endpoint.
    
    Usage:
        @router.get("/protected")
        @require_auth
        async def protected_endpoint(current_user: Dict = Depends(get_current_user)):
            return {"user": current_user}
    """
    @wraps(func)
    async def wrapper(*args, **kwargs):
        return await func(*args, **kwargs)
    return wrapper


def require_organization_access(func):
    """
    Decorator to require organization access for an endpoint.
    
    The endpoint must have an 'organization_id' parameter.
    
    Usage:
        @router.get("/reports/{organization_id}")
        @require_organization_access
        async def get_report(
            organization_id: int,
            has_access: bool = Depends(verify_organization_access)
        ):
            return {"organization_id": organization_id}
    """
    @wraps(func)
    async def wrapper(*args, **kwargs):
        return await func(*args, **kwargs)
    return wrapper


class RateLimiter:
    """
    Simple rate limiter for API endpoints.
    In production, use Redis-based rate limiting.
    """
    def __init__(self, calls: int = 100, period: int = 60):
        self.calls = calls
        self.period = period
        self.requests: Dict[str, Any] = {}
    
    async def check_rate_limit(
        self,
        request: Request,
        current_user: Dict[str, Any] = Depends(get_current_user)
    ):
        """Check if user has exceeded rate limit."""
        user_id = current_user["user_id"]
        now = datetime.now(timezone.utc)
        
        # Clean old requests
        self.requests = {
            k: v for k, v in self.requests.items()
            if (now - v[-1]).seconds < self.period
        }
        
        # Check rate limit
        if user_id in self.requests:
            user_requests = self.requests[user_id]
            recent_requests = [
                req for req in user_requests
                if (now - req).seconds < self.period
            ]
            
            if len(recent_requests) >= self.calls:
                raise HTTPException(
                    status_code=429,
                    detail="Rate limit exceeded"
                )
            
            self.requests[user_id] = recent_requests + [now]
        else:
            self.requests[user_id] = [now]


# Create rate limiter instances
api_rate_limiter = RateLimiter(calls=100, period=60)  # 100 calls per minute
report_rate_limiter = RateLimiter(calls=10, period=60)  # 10 reports per minute


# Middleware for adding security headers
async def add_security_headers(request: Request, call_next):
    """Add security headers to all responses."""
    response = await call_next(request)
    
    # Security headers
    response.headers["X-Content-Type-Options"] = "nosniff"
    response.headers["X-Frame-Options"] = "DENY"
    response.headers["X-XSS-Protection"] = "1; mode=block"
    response.headers["Strict-Transport-Security"] = "max-age=31536000; includeSubDomains"
    response.headers["Content-Security-Policy"] = "default-src 'self'"
    
    return response