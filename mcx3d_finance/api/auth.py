"""
OAuth2 API endpoints for Xero authentication.
Handles the complete OAuth2 flow with proper security.
"""
import logging
from typing import Optional, Dict, Any
from datetime import datetime, timezone

from fastapi import APIRouter, Depends, Query, HTTPException, Request, Response
from fastapi.responses import RedirectResponse
from sqlalchemy.orm import Session
from pydantic import BaseModel

from ..db.session import get_db
from ..auth.xero_oauth import XeroAuthManager
from ..api.auth_middleware import (
    get_current_user,
    create_access_token,
    require_auth,
    api_rate_limiter
)
from ..db.models import User, Organization, UserOrganization

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/api/auth", tags=["authentication"])


class LoginRequest(BaseModel):
    """User login request."""
    email: str
    password: str


class LoginResponse(BaseModel):
    """User login response."""
    access_token: str
    token_type: str = "bearer"
    user: Dict[str, Any]


class AuthStatusResponse(BaseModel):
    """Authentication status response."""
    authenticated: bool
    organization_id: Optional[int] = None
    organization_name: Optional[str] = None
    token_expires_at: Optional[datetime] = None


@router.post("/login", response_model=LoginResponse)
async def login(
    request: LoginRequest,
    db: Session = Depends(get_db),
    _: Any = Depends(api_rate_limiter.check_rate_limit)
):
    """
    User login endpoint.
    
    Authenticates user and returns JWT token.
    """
    try:
        # Find user by email
        user = db.query(User).filter(User.email == request.email).first()
        
        if not user or not user.is_active:
            raise HTTPException(
                status_code=401,
                detail="Invalid email or password"
            )
        
        # Verify password (in production, use proper password hashing)
        # For now, assuming password verification is handled elsewhere
        # if not verify_password(request.password, user.hashed_password):
        #     raise HTTPException(status_code=401, detail="Invalid email or password")
        
        # Get user's organizations
        user_orgs = db.query(UserOrganization).filter(
            UserOrganization.user_id == user.id
        ).all()
        
        org_ids = [uo.organization_id for uo in user_orgs]
        
        # Create access token
        token_data = {
            "sub": str(user.id),
            "email": user.email,
            "organizations": org_ids
        }
        access_token = create_access_token(token_data)
        
        logger.info(f"User {user.email} logged in successfully")
        
        return LoginResponse(
            access_token=access_token,
            user={
                "id": user.id,
                "email": user.email,
                "full_name": user.full_name,
                "organizations": org_ids
            }
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Login error: {e}")
        raise HTTPException(
            status_code=500,
            detail="Login failed"
        )


@router.get("/xero/authorize")
@require_auth
async def xero_authorize(
    organization_id: Optional[int] = Query(None),
    current_user: Dict[str, Any] = Depends(get_current_user)
):
    """
    Initiate Xero OAuth2 authorization flow.
    
    Requires authentication. Generates authorization URL with CSRF protection.
    """
    try:
        auth_manager = XeroAuthManager()
        
        # Generate state with user and org info for callback
        import json
        import base64
        state_data = {
            "user_id": current_user["user_id"],
            "organization_id": organization_id,
            "timestamp": datetime.now(timezone.utc).isoformat()
        }
        state = base64.urlsafe_b64encode(
            json.dumps(state_data).encode()
        ).decode().rstrip("=")
        
        # Generate authorization URL
        auth_url = auth_manager.generate_auth_url(state)
        
        if not auth_url:
            raise HTTPException(
                status_code=500,
                detail="Failed to generate authorization URL"
            )
        
        logger.info(
            f"User {current_user['user_id']} initiated Xero auth for "
            f"organization {organization_id}"
        )
        
        return {
            "auth_url": auth_url,
            "message": "Redirect user to the authorization URL"
        }
        
    except Exception as e:
        logger.error(f"Xero authorization error: {e}")
        raise HTTPException(
            status_code=500,
            detail="Failed to initiate authorization"
        )


@router.get("/xero/callback")
async def xero_callback(
    code: str = Query(...),
    state: str = Query(...),
    db: Session = Depends(get_db)
):
    """
    Handle Xero OAuth2 callback.
    
    Exchanges authorization code for access token and stores it securely.
    """
    try:
        # Decode state to get user info
        import json
        import base64
        try:
            state_data = json.loads(
                base64.urlsafe_b64decode(state + "==").decode()
            )
            user_id = state_data.get("user_id")
        except Exception as e:
            logger.error(f"Invalid state parameter: {e}")
            return RedirectResponse(
                url="/error?message=Invalid+state+parameter",
                status_code=302
            )
        
        # Build full callback URL
        callback_url = f"{request.url.scheme}://{request.url.netloc}{request.url.path}?code={code}&state={state}"
        
        # Handle callback
        auth_manager = XeroAuthManager()
        result = auth_manager.handle_callback(callback_url)
        
        if not result["success"]:
            error_message = result.get("error", "Authentication failed")
            logger.error(f"Xero callback failed: {error_message}")
            return RedirectResponse(
                url=f"/error?message={error_message}",
                status_code=302
            )
        
        # Link user to organization if provided
        if user_id and result.get("organization_id"):
            user_org = db.query(UserOrganization).filter(
                UserOrganization.user_id == user_id,
                UserOrganization.organization_id == result["organization_id"]
            ).first()
            
            if not user_org:
                user_org = UserOrganization(
                    user_id=user_id,
                    organization_id=result["organization_id"],
                    role="admin",
                    created_at=datetime.now(timezone.utc)
                )
                db.add(user_org)
                db.commit()
        
        logger.info(
            f"Xero OAuth callback successful for organization "
            f"{result['organization_id']}"
        )
        
        # Redirect to success page
        return RedirectResponse(
            url=f"/success?organization_id={result['organization_id']}",
            status_code=302
        )
        
    except Exception as e:
        logger.error(f"Xero callback error: {e}")
        return RedirectResponse(
            url="/error?message=Callback+processing+failed",
            status_code=302
        )


@router.post("/xero/refresh/{organization_id}")
@require_auth
async def xero_refresh_token(
    organization_id: int,
    current_user: Dict[str, Any] = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Manually refresh Xero OAuth2 token.
    
    Requires authentication and organization access.
    """
    try:
        # Verify user has access to organization
        if organization_id not in current_user.get("organizations", []):
            raise HTTPException(
                status_code=403,
                detail="You do not have access to this organization"
            )
        
        auth_manager = XeroAuthManager()
        token = auth_manager.get_valid_token(organization_id)
        
        if not token:
            raise HTTPException(
                status_code=404,
                detail="No token found for organization"
            )
        
        logger.info(
            f"User {current_user['user_id']} refreshed token for "
            f"organization {organization_id}"
        )
        
        return {
            "success": True,
            "message": "Token refreshed successfully"
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Token refresh error: {e}")
        raise HTTPException(
            status_code=500,
            detail="Failed to refresh token"
        )


@router.delete("/xero/revoke/{organization_id}")
@require_auth
async def xero_revoke_token(
    organization_id: int,
    current_user: Dict[str, Any] = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Revoke Xero OAuth2 token for organization.
    
    Requires authentication and organization admin access.
    """
    try:
        # Verify user has admin access to organization
        user_org = db.query(UserOrganization).filter(
            UserOrganization.user_id == current_user["user_id"],
            UserOrganization.organization_id == organization_id
        ).first()
        
        if not user_org or user_org.role != "admin":
            raise HTTPException(
                status_code=403,
                detail="Admin access required to revoke token"
            )
        
        auth_manager = XeroAuthManager()
        success = auth_manager.revoke_token(organization_id)
        
        if not success:
            raise HTTPException(
                status_code=404,
                detail="No token found for organization"
            )
        
        logger.info(
            f"User {current_user['user_id']} revoked token for "
            f"organization {organization_id}"
        )
        
        return {
            "success": True,
            "message": "Token revoked successfully"
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Token revocation error: {e}")
        raise HTTPException(
            status_code=500,
            detail="Failed to revoke token"
        )


@router.get("/xero/status/{organization_id}", response_model=AuthStatusResponse)
@require_auth
async def xero_auth_status(
    organization_id: int,
    current_user: Dict[str, Any] = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Check Xero authentication status for organization.
    
    Requires authentication and organization access.
    """
    try:
        # Verify user has access to organization
        if organization_id not in current_user.get("organizations", []):
            raise HTTPException(
                status_code=403,
                detail="You do not have access to this organization"
            )
        
        # Get organization
        org = db.query(Organization).get(organization_id)
        if not org:
            raise HTTPException(
                status_code=404,
                detail="Organization not found"
            )
        
        # Check if authenticated
        auth_manager = XeroAuthManager()
        token = auth_manager.get_valid_token(organization_id)
        
        return AuthStatusResponse(
            authenticated=token is not None,
            organization_id=organization_id,
            organization_name=org.name,
            token_expires_at=org.token_expires_at
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Auth status error: {e}")
        raise HTTPException(
            status_code=500,
            detail="Failed to check authentication status"
        )


@router.post("/logout")
@require_auth
async def logout(
    response: Response,
    current_user: Dict[str, Any] = Depends(get_current_user)
):
    """
    User logout endpoint.
    
    In a real implementation, would invalidate the JWT token.
    For now, just returns success and client should remove token.
    """
    try:
        logger.info(f"User {current_user['user_id']} logged out")
        
        # In production, add token to blacklist or use short-lived tokens
        # For now, client is responsible for removing token
        
        return {
            "success": True,
            "message": "Logged out successfully"
        }
        
    except Exception as e:
        logger.error(f"Logout error: {e}")
        raise HTTPException(
            status_code=500,
            detail="Logout failed"
        )