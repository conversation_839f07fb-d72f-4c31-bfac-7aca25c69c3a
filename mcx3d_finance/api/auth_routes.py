import logging
from fastapi import <PERSON><PERSON><PERSON><PERSON>, Request, HTTPException
from fastapi.responses import RedirectResponse, JSONResponse
from ..auth.xero_oauth import XeroAuthManager

logger = logging.getLogger(__name__)
router = APIRouter()


@router.get("/xero/login")
async def xero_login():
    """
    Redirects the user to the Xero authorization URL.
    """
    try:
        auth_manager = XeroAuthManager()
        auth_url, state = auth_manager.generate_auth_url()
        
        if not auth_url:
            logger.error("Failed to generate Xero authorization URL")
            raise HTTPException(status_code=500, detail="Could not generate Xero auth URL")
            
        logger.info(f"Redirecting to Xero OAuth with state: {state}")
        return RedirectResponse(url=auth_url)
        
    except Exception as e:
        logger.error(f"Error in Xero login: {e}")
        raise HTTPException(status_code=500, detail=f"OAuth initialization failed: {str(e)}")


@router.get("/xero/callback")
async def xero_callback(request: Request):
    """
    Handles the callback from Xero after user authorization.
    """
    try:
        auth_manager = XeroAuthManager()
        result = auth_manager.handle_callback(str(request.url))
        
        if result["success"]:
            logger.info(f"Successfully authenticated organization: {result.get('tenant_name')}")
            
            # Return JSON response instead of redirect for easier testing
            return JSONResponse({
                "success": True,
                "message": f"✅ Authorization successful! Organization '{result.get('tenant_name')}' connected.",
                "organization_id": result.get("organization_id"),
                "tenant_name": result.get("tenant_name"),
                "next_steps": [
                    "Run: python test_data_import.py",
                    f"Or sync data with org ID: {result.get('organization_id')}"
                ]
            })
        else:
            logger.error(f"OAuth callback failed: {result.get('error')}")
            error_url = f"/auth/error?message={result.get('error', 'Authentication failed')}"
            return RedirectResponse(url=error_url)
            
    except Exception as e:
        logger.error(f"Unexpected error in Xero callback: {e}")
        error_url = f"/auth/error?message=Unexpected authentication error"
        return RedirectResponse(url=error_url)
