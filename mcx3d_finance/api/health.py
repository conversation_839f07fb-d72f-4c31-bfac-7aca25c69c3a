"""
Health check endpoints for MCX3D Financial System.

Provides comprehensive health monitoring including enhanced reporting system status,
resource monitoring, and system capabilities validation.
"""

from fastapi import APIRouter, HTTPException
from fastapi.responses import JSONResponse
from typing import Dict, Any, Optional
import logging
import tempfile
import os
from datetime import datetime, timedelta
from pathlib import Path

from mcx3d_finance.reporting.generator import ReportGenerator
from mcx3d_finance.utils.resource_monitor import ResourceMonitor
from mcx3d_finance.exceptions import MCX3DException
from mcx3d_finance.utils.graceful_degradation import ReportComplexity

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/health", tags=["Health"])


@router.get("/")
async def basic_health_check():
    """
    Basic health check endpoint.
    
    Returns simple status to verify the API is responding.
    """
    return {
        "status": "healthy",
        "timestamp": datetime.now().isoformat(),
        "service": "MCX3D Financials API",
        "version": "2.0.0"
    }


@router.get("/detailed")
async def detailed_health_check():
    """
    Detailed health check including system resources and capabilities.
    
    Provides comprehensive system status including resource usage,
    database connectivity, and service availability.
    """
    try:
        health_data = {
            "status": "healthy",
            "timestamp": datetime.now().isoformat(),
            "service": "MCX3D Financials API",
            "version": "2.0.0",
            "components": {}
        }
        
        # Check system resources
        try:
            resource_monitor = ResourceMonitor()
            with resource_monitor.monitor_resources("health_check") as metrics:
                resource_status = {
                    "status": "healthy",
                    "memory_usage_percent": metrics.memory_usage_mb / 1024 * 100,  # Rough estimate
                    "memory_available_mb": metrics.memory_available_mb,
                    "disk_usage_percent": metrics.disk_usage_percent,
                    "disk_free_mb": metrics.disk_free_mb,
                    "timestamp": datetime.now().isoformat()
                }
                
                # Determine resource health status
                if metrics.memory_usage_mb > 1000 or metrics.disk_usage_percent > 90:
                    resource_status["status"] = "warning"
                    resource_status["warnings"] = []
                    
                    if metrics.memory_usage_mb > 1000:
                        resource_status["warnings"].append("High memory usage detected")
                    
                    if metrics.disk_usage_percent > 90:
                        resource_status["warnings"].append("Low disk space available")
                
                health_data["components"]["resources"] = resource_status
                
        except Exception as e:
            logger.error(f"Resource monitoring failed: {e}")
            health_data["components"]["resources"] = {
                "status": "error",
                "error": f"Resource monitoring unavailable: {str(e)}"
            }
        
        # Check database connectivity
        try:
            from mcx3d_finance.db.session import get_db
            db = next(get_db())
            # Simple query to test connectivity
            db.execute("SELECT 1")
            
            health_data["components"]["database"] = {
                "status": "healthy",
                "timestamp": datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"Database check failed: {e}")
            health_data["components"]["database"] = {
                "status": "error",
                "error": f"Database connectivity failed: {str(e)}"
            }
        
        # Check enhanced reporting system
        try:
            reporting_status = await _check_enhanced_reporting_system()
            health_data["components"]["enhanced_reporting"] = reporting_status
            
        except Exception as e:
            logger.error(f"Enhanced reporting check failed: {e}")
            health_data["components"]["enhanced_reporting"] = {
                "status": "error",
                "error": f"Enhanced reporting system check failed: {str(e)}"
            }
        
        # Determine overall health status
        component_statuses = [comp.get("status", "unknown") for comp in health_data["components"].values()]
        
        if "error" in component_statuses:
            health_data["status"] = "unhealthy"
        elif "warning" in component_statuses:
            health_data["status"] = "degraded"
        else:
            health_data["status"] = "healthy"
        
        # Return appropriate HTTP status code
        if health_data["status"] == "unhealthy":
            return JSONResponse(content=health_data, status_code=503)
        elif health_data["status"] == "degraded":
            return JSONResponse(content=health_data, status_code=200)  # Still operational
        else:
            return JSONResponse(content=health_data, status_code=200)
            
    except Exception as e:
        logger.error(f"Health check failed: {e}")
        return JSONResponse(
            content={
                "status": "error",
                "timestamp": datetime.now().isoformat(),
                "error": f"Health check failed: {str(e)}"
            },
            status_code=503
        )


@router.get("/reporting")
async def enhanced_reporting_health_check():
    """
    Dedicated health check for the enhanced reporting system.
    
    Tests all major components of the enhanced reporting system including
    error handling, graceful degradation, and resource monitoring.
    """
    try:
        reporting_status = await _check_enhanced_reporting_system()
        
        if reporting_status["status"] == "error":
            return JSONResponse(content=reporting_status, status_code=503)
        elif reporting_status["status"] == "warning":
            return JSONResponse(content=reporting_status, status_code=200)
        else:
            return JSONResponse(content=reporting_status, status_code=200)
            
    except Exception as e:
        logger.error(f"Enhanced reporting health check failed: {e}")
        return JSONResponse(
            content={
                "status": "error",
                "timestamp": datetime.now().isoformat(),
                "error": f"Enhanced reporting health check failed: {str(e)}"
            },
            status_code=503
        )


async def _check_enhanced_reporting_system() -> Dict[str, Any]:
    """
    Comprehensive check of the enhanced reporting system.
    
    Returns detailed status of all enhanced reporting components.
    """
    status_data = {
        "status": "healthy",
        "timestamp": datetime.now().isoformat(),
        "components": {},
        "capabilities": {},
        "performance_metrics": {}
    }
    
    try:
        # Test main generator creation
        start_time = datetime.now()
        generator = ReportGenerator()
        creation_time = (datetime.now() - start_time).total_seconds()
        
        status_data["components"]["generator_creation"] = {
            "status": "healthy",
            "creation_time_seconds": creation_time
        }
        
        # Test validation engine
        try:
            if hasattr(generator, 'validation_engine') and generator.validation_engine:
                status_data["components"]["validation_engine"] = {"status": "healthy"}
            else:
                status_data["components"]["validation_engine"] = {
                    "status": "warning",
                    "message": "Validation engine not available"
                }
        except Exception as e:
            status_data["components"]["validation_engine"] = {
                "status": "error",
                "error": str(e)
            }
        
        # Test resource monitoring
        try:
            if hasattr(generator, 'resource_monitor') and generator.resource_monitor:
                # Test resource monitoring functionality
                with generator.resource_monitor.monitor_resources("health_test") as metrics:
                    status_data["components"]["resource_monitoring"] = {
                        "status": "healthy",
                        "current_memory_mb": metrics.memory_usage_mb,
                        "available_memory_mb": metrics.memory_available_mb,
                        "disk_free_mb": metrics.disk_free_mb
                    }
            else:
                status_data["components"]["resource_monitoring"] = {
                    "status": "warning",
                    "message": "Resource monitoring not available"
                }
        except Exception as e:
            status_data["components"]["resource_monitoring"] = {
                "status": "error",
                "error": str(e)
            }
        
        # Test graceful degradation
        try:
            if hasattr(generator, 'degradation_manager') and generator.degradation_manager:
                status_data["components"]["graceful_degradation"] = {"status": "healthy"}
            else:
                status_data["components"]["graceful_degradation"] = {
                    "status": "warning",
                    "message": "Graceful degradation not available"
                }
        except Exception as e:
            status_data["components"]["graceful_degradation"] = {
                "status": "error",
                "error": str(e)
            }
        
        # Test file operations
        try:
            with tempfile.TemporaryDirectory() as temp_dir:
                test_file = os.path.join(temp_dir, "health_test.txt")
                Path(test_file).write_text("health check test")
                
                if Path(test_file).exists():
                    status_data["components"]["file_operations"] = {"status": "healthy"}
                    Path(test_file).unlink()  # Clean up
                else:
                    status_data["components"]["file_operations"] = {
                        "status": "error",
                        "error": "File creation test failed"
                    }
        except Exception as e:
            status_data["components"]["file_operations"] = {
                "status": "error",
                "error": str(e)
            }
        
        # Test report generation capability (minimal test)
        try:
            test_data = {
                "header": {
                    "organization_id": 999,
                    "company_name": "Health Test Company",
                    "statement_title": "HEALTH CHECK REPORT",
                    "reporting_date": datetime.now().strftime("%Y-%m-%d"),
                    "currency": "USD"
                },
                "total_revenue": 1000.00,
                "net_income": 100.00
            }
            
            with tempfile.TemporaryDirectory() as temp_dir:
                test_output = os.path.join(temp_dir, "health_test.json")
                
                # Test minimal complexity JSON generation (fastest/safest test)
                start_time = datetime.now()
                result = generator.generate_income_statement(
                    income_statement_data=test_data,
                    output_path=test_output,
                    output_format="json",
                    complexity=ReportComplexity.MINIMAL
                )
                generation_time = (datetime.now() - start_time).total_seconds()
                
                if result.get("success"):
                    status_data["components"]["report_generation"] = {
                        "status": "healthy",
                        "test_generation_time_seconds": generation_time,
                        "output_format": result.get("format", "json"),
                        "complexity": str(result.get("complexity", "unknown")).replace("ReportComplexity.", "")
                    }
                else:
                    status_data["components"]["report_generation"] = {
                        "status": "error",
                        "error": "Test report generation failed",
                        "details": result.get("errors", [])
                    }
                    
        except Exception as e:
            status_data["components"]["report_generation"] = {
                "status": "error",
                "error": f"Report generation test failed: {str(e)}"
            }
        
        # Check system capabilities
        status_data["capabilities"] = {
            "supported_formats": ["pdf", "excel", "html", "csv", "json"],
            "complexity_levels": ["minimal", "standard", "full"],
            "graceful_degradation": True,
            "resource_monitoring": True,
            "error_recovery": True,
            "validation": True
        }
        
        # Calculate overall performance metrics
        status_data["performance_metrics"] = {
            "generator_creation_time_seconds": creation_time,
            "health_check_completed_at": datetime.now().isoformat()
        }
        
        # Determine overall status
        component_statuses = [comp.get("status", "unknown") for comp in status_data["components"].values()]
        
        if "error" in component_statuses:
            status_data["status"] = "error"
            status_data["issues"] = [
                f"{name}: {comp.get('error', 'Unknown error')}"
                for name, comp in status_data["components"].items()
                if comp.get("status") == "error"
            ]
        elif "warning" in component_statuses:
            status_data["status"] = "warning"
            status_data["warnings"] = [
                f"{name}: {comp.get('message', 'Unknown warning')}"
                for name, comp in status_data["components"].items()
                if comp.get("status") == "warning"
            ]
        else:
            status_data["status"] = "healthy"
            
    except Exception as e:
        logger.error(f"Enhanced reporting system check failed: {e}")
        status_data.update({
            "status": "error",
            "error": f"System check failed: {str(e)}",
            "timestamp": datetime.now().isoformat()
        })
    
    return status_data


@router.get("/readiness")
async def readiness_check():
    """
    Kubernetes-style readiness check.
    
    Verifies that the service is ready to handle requests.
    Used by load balancers to determine if instance should receive traffic.
    """
    try:
        # Quick checks for readiness
        checks = []
        
        # Database connectivity
        try:
            from mcx3d_finance.db.session import get_db
            db = next(get_db())
            db.execute("SELECT 1")
            checks.append(("database", True, None))
        except Exception as e:
            checks.append(("database", False, str(e)))
        
        # Main reporting system basic check
        try:
            generator = ReportGenerator()
            checks.append(("main_reporting", True, None))
        except Exception as e:
            checks.append(("enhanced_reporting", False, str(e)))
        
        # Check results
        failed_checks = [(name, error) for name, success, error in checks if not success]
        
        if failed_checks:
            return JSONResponse(
                content={
                    "ready": False,
                    "timestamp": datetime.now().isoformat(),
                    "failed_checks": {name: error for name, error in failed_checks}
                },
                status_code=503
            )
        else:
            return {
                "ready": True,
                "timestamp": datetime.now().isoformat(),
                "checks_passed": len(checks)
            }
            
    except Exception as e:
        logger.error(f"Readiness check failed: {e}")
        return JSONResponse(
            content={
                "ready": False,
                "timestamp": datetime.now().isoformat(),
                "error": str(e)
            },
            status_code=503
        )


@router.get("/liveness")
async def liveness_check():
    """
    Kubernetes-style liveness check.
    
    Simple check to verify the service is alive and not deadlocked.
    Used by orchestrators to determine if container should be restarted.
    """
    try:
        # Very basic check - just verify we can respond
        current_time = datetime.now()
        
        return {
            "alive": True,
            "timestamp": current_time.isoformat(),
            "uptime_check": "responsive"
        }
        
    except Exception as e:
        logger.error(f"Liveness check failed: {e}")
        return JSONResponse(
            content={
                "alive": False,
                "timestamp": datetime.now().isoformat(),
                "error": str(e)
            },
            status_code=503
        )