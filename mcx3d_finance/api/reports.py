"""
Secured API endpoints for financial reports with authentication and error handling.
"""
from fastapi import APIRouter, Depends, Query, HTTPException
from sqlalchemy.orm import Session
from fastapi.responses import StreamingResponse
from typing import Dict, Any
import logging
import io

from mcx3d_finance.db.session import get_db
from mcx3d_finance.core.financials import income_statement, balance_sheet, cash_flow
from mcx3d_finance.core.valuation import dcf, multiples
from mcx3d_finance.reporting.generator import ReportGenerator
from mcx3d_finance.db import models
from mcx3d_finance.api.auth_middleware import (
    get_current_user,
    verify_organization_access,
    require_auth,
    require_organization_access,
    report_rate_limiter
)
from mcx3d_finance.api.schemas import (
    DCFValuationRequest,
    MultiplesValuationRequest,
    ReportFormat
)
from sqlalchemy import func
from datetime import datetime, timezone

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/api", tags=["reports"])


@router.get("/reports/income-statement")
@require_auth
@require_organization_access
async def get_income_statement(
    organization_id: int,
    start_date: str,
    end_date: str,
    format: ReportFormat = Query(ReportFormat.json),
    db: Session = Depends(get_db),
    current_user: Dict[str, Any] = Depends(get_current_user),
    has_access: bool = Depends(verify_organization_access),
    _: Any = Depends(report_rate_limiter.check_rate_limit)
):
    """
    Generate income statement for specified period.
    
    Requires authentication and organization access.
    """
    try:
        # Validate dates
        start_date_obj = datetime.strptime(start_date, "%Y-%m-%d")
        end_date_obj = datetime.strptime(end_date, "%Y-%m-%d")
        
        if start_date_obj > end_date_obj:
            raise HTTPException(
                status_code=400,
                detail="Start date must be before or equal to end date"
            )
    except ValueError as e:
        logger.error(f"Invalid date format: {e}")
        raise HTTPException(
            status_code=400,
            detail="Invalid date format. Use YYYY-MM-DD"
        )

    try:
        # Fetch transactions
        transactions = (
            db.query(
                models.Account.type,
                func.sum(models.Transaction.amount).label("amount")
            )
            .join(models.Account)
            .filter(
                models.Transaction.organization_id == organization_id,
                models.Transaction.date >= start_date_obj,
                models.Transaction.date <= end_date_obj,
            )
            .group_by(models.Account.type)
            .all()
        )

        data = [{"account_type": t[0], "amount": float(t[1] or 0)} for t in transactions]
        
        logger.info(
            f"User {current_user['user_id']} generated income statement for "
            f"organization {organization_id} from {start_date} to {end_date}"
        )

        if format == ReportFormat.json:
            return {
                "report_name": "Income Statement",
                "organization_id": organization_id,
                "period": {
                    "start_date": start_date,
                    "end_date": end_date
                },
                "data": income_statement.IncomeStatementGenerator(organization_id).generate_income_statement(start_date_obj, end_date_obj),
                "generated_at": datetime.now(timezone.utc).isoformat()
            }

        # Generate report file using main generator
        report_generator = ReportGenerator()
        output_path = f"income_statement_{organization_id}_{end_date}.{format.value}"
        
        # Prepare data using existing generator
        report_data = income_statement.IncomeStatementGenerator(organization_id).generate_income_statement(start_date_obj, end_date_obj)
        
        # Use main generator with format-specific methods
        if format.value == "pdf":
            report_generator.generate_income_statement_pdf(report_data, output_path)
        elif format.value == "excel":
            report_generator.generate_income_statement_excel(report_data, output_path)
        else:
            raise HTTPException(status_code=400, detail=f"Unsupported format: {format.value}")

        # Read the generated file
        with open(output_path, "rb") as f:
            report_buffer = f.read()

        media_types = {
            ReportFormat.pdf: "application/pdf",
            ReportFormat.excel: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
            ReportFormat.html: "text/html",
        }
        headers = {
            "Content-Disposition": f'attachment; filename="income_statement_{organization_id}_{start_date}_{end_date}.{format.value}"'
        }
        return StreamingResponse(
            io.BytesIO(report_buffer),
            media_type=media_types[format],
            headers=headers
        )
        
    except Exception as e:
        logger.error(f"Error generating income statement: {e}")
        raise HTTPException(
            status_code=500,
            detail="Failed to generate income statement"
        )


@router.get("/reports/balance-sheet")
@require_auth
@require_organization_access
async def get_balance_sheet(
    organization_id: int,
    date: str,
    format: ReportFormat = Query(ReportFormat.json),
    db: Session = Depends(get_db),
    current_user: Dict[str, Any] = Depends(get_current_user),
    has_access: bool = Depends(verify_organization_access),
    _: Any = Depends(report_rate_limiter.check_rate_limit)
):
    """
    Generate balance sheet as of specified date.
    
    Requires authentication and organization access.
    """
    try:
        # Validate date
        date_obj = datetime.strptime(date, "%Y-%m-%d")
    except ValueError as e:
        logger.error(f"Invalid date format: {e}")
        raise HTTPException(
            status_code=400,
            detail="Invalid date format. Use YYYY-MM-DD"
        )

    try:
        # Fetch transactions
        transactions = (
            db.query(
                models.Account.type,
                func.sum(models.Transaction.amount).label("amount")
            )
            .join(models.Account)
            .filter(
                models.Transaction.organization_id == organization_id,
                models.Transaction.date <= date_obj,
            )
            .group_by(models.Account.type)
            .all()
        )

        data = [{"account_type": t[0], "amount": float(t[1] or 0)} for t in transactions]
        
        logger.info(
            f"User {current_user['user_id']} generated balance sheet for "
            f"organization {organization_id} as of {date}"
        )
        
        if format == ReportFormat.json:
            return {
                "report_name": "Balance Sheet",
                "organization_id": organization_id,
                "as_of_date": date,
                "data": balance_sheet.BalanceSheetGenerator(organization_id).generate_balance_sheet(date_obj),
                "generated_at": datetime.now(timezone.utc).isoformat()
            }

        # Generate report file using main generator
        report_generator = ReportGenerator()
        output_path = f"balance_sheet_{organization_id}_{date}.{format.value}"
        
        # Prepare data using existing generator
        report_data = balance_sheet.BalanceSheetGenerator(organization_id).generate_balance_sheet(date_obj)
        
        # Use main generator with format-specific methods
        if format.value == "pdf":
            report_generator.generate_balance_sheet_pdf(report_data, output_path)
        elif format.value == "excel":
            report_generator.generate_balance_sheet_excel(report_data, output_path)
        else:
            raise HTTPException(status_code=400, detail=f"Unsupported format: {format.value}")

        # Read the generated file
        with open(output_path, "rb") as f:
            report_buffer = f.read()

        media_types = {
            ReportFormat.pdf: "application/pdf",
            ReportFormat.excel: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
            ReportFormat.html: "text/html",
        }
        headers = {
            "Content-Disposition": f'attachment; filename="balance_sheet_{organization_id}_{date}.{format.value}"'
        }
        return StreamingResponse(
            io.BytesIO(report_buffer),
            media_type=media_types[format],
            headers=headers
        )
        
    except Exception as e:
        logger.error(f"Error generating balance sheet: {e}")
        raise HTTPException(
            status_code=500,
            detail="Failed to generate balance sheet"
        )


@router.get("/reports/cash-flow")
@require_auth
@require_organization_access
async def get_cash_flow(
    organization_id: int,
    start_date: str,
    end_date: str,
    format: ReportFormat = Query(ReportFormat.json),
    db: Session = Depends(get_db),
    current_user: Dict[str, Any] = Depends(get_current_user),
    has_access: bool = Depends(verify_organization_access),
    _: Any = Depends(report_rate_limiter.check_rate_limit)
):
    """
    Generate cash flow statement for specified period.
    
    Requires authentication and organization access.
    """
    try:
        # Validate dates
        start_date_obj = datetime.strptime(start_date, "%Y-%m-%d")
        end_date_obj = datetime.strptime(end_date, "%Y-%m-%d")
        
        if start_date_obj > end_date_obj:
            raise HTTPException(
                status_code=400,
                detail="Start date must be before or equal to end date"
            )
    except ValueError as e:
        logger.error(f"Invalid date format: {e}")
        raise HTTPException(
            status_code=400,
            detail="Invalid date format. Use YYYY-MM-DD"
        )

    try:
        # Fetch transactions
        transactions = (
            db.query(
                models.Account.type,
                func.sum(models.Transaction.amount).label("amount")
            )
            .join(models.Account)
            .filter(
                models.Transaction.organization_id == organization_id,
                models.Transaction.date >= start_date_obj,
                models.Transaction.date <= end_date_obj,
            )
            .group_by(models.Account.type)
            .all()
        )

        data = [{"account_type": t[0], "amount": float(t[1] or 0)} for t in transactions]
        
        logger.info(
            f"User {current_user['user_id']} generated cash flow statement for "
            f"organization {organization_id} from {start_date} to {end_date}"
        )
        
        if format == ReportFormat.json:
            return {
                "report_name": "Cash Flow Statement",
                "organization_id": organization_id,
                "period": {
                    "start_date": start_date,
                    "end_date": end_date
                },
                "data": cash_flow.CashFlowGenerator(db).generate_cash_flow_statement(organization_id, start_date_obj, end_date_obj),
                "generated_at": datetime.now(timezone.utc).isoformat()
            }

        # Generate report file
        report_generator = ReportGenerator()
        output_path = f"cash_flow_{organization_id}_{end_date}.{format.value}"
        # Note: The ReportGenerator does not have a cash flow generation method.
        # This will need to be implemented. For now, we will raise a NotImplementedError.
        raise NotImplementedError("Cash flow report generation is not yet implemented in ReportGenerator")

        media_types = {
            ReportFormat.pdf: "application/pdf",
            ReportFormat.excel: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
            ReportFormat.html: "text/html",
        }
        headers = {
            "Content-Disposition": f'attachment; filename="cash_flow_{organization_id}_{start_date}_{end_date}.{format.value}"'
        }
        return StreamingResponse(
            report_buffer,
            media_type=media_types[format],
            headers=headers
        )
        
    except Exception as e:
        logger.error(f"Error generating cash flow statement: {e}")
        raise HTTPException(
            status_code=500,
            detail="Failed to generate cash flow statement"
        )


@router.post("/valuation/dcf")
@require_auth
async def get_dcf_valuation(
    request: DCFValuationRequest,
    current_user: Dict[str, Any] = Depends(get_current_user),
    _: Any = Depends(report_rate_limiter.check_rate_limit)
):
    """
    Perform DCF (Discounted Cash Flow) valuation.
    
    Requires authentication.
    """
    try:
        # Validate input
        if request.discount_rate <= 0 or request.discount_rate >= 1:
            raise HTTPException(
                status_code=400,
                detail="Discount rate must be between 0 and 1"
            )
        
        if request.terminal_growth_rate < 0 or request.terminal_growth_rate >= request.discount_rate:
            raise HTTPException(
                status_code=400,
                detail="Terminal growth rate must be non-negative and less than discount rate"
            )
        
        # Calculate valuation
        valuation = dcf.DCFValuation().calculate_dcf_valuation(
            request.financial_projections,
            request.discount_rate,
            request.terminal_growth_rate
        )
        
        logger.info(
            f"User {current_user['user_id']} performed DCF valuation"
        )
        
        return {
            "valuation_type": "DCF",
            "inputs": {
                "discount_rate": request.discount_rate,
                "terminal_growth_rate": request.terminal_growth_rate,
                "projection_years": len(request.financial_projections)
            },
            "valuation": valuation,
            "generated_at": datetime.now(timezone.utc).isoformat()
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error performing DCF valuation: {e}")
        raise HTTPException(
            status_code=500,
            detail="Failed to perform DCF valuation"
        )


@router.post("/valuation/multiples")
@require_auth
async def get_multiples_valuation(
    request: MultiplesValuationRequest,
    current_user: Dict[str, Any] = Depends(get_current_user),
    _: Any = Depends(report_rate_limiter.check_rate_limit)
):
    """
    Perform multiples-based valuation.
    
    Requires authentication.
    """
    try:
        # Validate that we have at least one multiple
        if not request.comparable_multiples:
            raise HTTPException(
                status_code=400,
                detail="At least one comparable multiple is required"
            )
        
        # Calculate valuation
        valuation = multiples.calculate_multiples_valuation(
            request.financial_metrics,
            request.comparable_multiples
        )
        
        logger.info(
            f"User {current_user['user_id']} performed multiples valuation"
        )
        
        return {
            "valuation_type": "Multiples",
            "inputs": {
                "metrics_count": len(request.financial_metrics),
                "multiples_count": len(request.comparable_multiples)
            },
            "valuation": valuation,
            "generated_at": datetime.now(timezone.utc).isoformat()
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error performing multiples valuation: {e}")
        raise HTTPException(
            status_code=500,
            detail="Failed to perform multiples valuation"
        )