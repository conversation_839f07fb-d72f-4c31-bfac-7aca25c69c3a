"""
Report data validation for the MCX3D financial system.

Provides comprehensive validation for all data used in report generation,
including DCF valuations, financial statements, and SaaS metrics.
"""

import logging
from datetime import datetime, timedelta
from typing import Dict, Any, List, Tuple, Optional, Union
from decimal import Decimal, InvalidOperation

from mcx3d_finance.exceptions import (
    ReportDataValidationError,
    FinancialDataError,
    ValidationError,
    RequiredFieldError,
    DataTypeValidationError,
    FinancialRangeError
)

logger = logging.getLogger(__name__)


class ReportDataValidator:
    """
    Comprehensive validator for report generation data.
    
    Validates data integrity, business rules, and format requirements
    for all report types in the MCX3D system.
    """
    
    def __init__(self):
        """Initialize the report data validator."""
        self.validation_errors = []
        self.validation_warnings = []
    
    def validate_dcf_data(self, dcf_data: Dict[str, Any]) -> Tuple[bool, List[str], List[str]]:
        """
        Validate DCF valuation input data.
        
        Args:
            dcf_data: Dictionary containing DCF input data
            
        Returns:
            Tuple of (is_valid, errors, warnings)
            
        Raises:
            ReportDataValidationError: If critical validation failures occur
        """
        try:
            self.validation_errors = []
            self.validation_warnings = []
            
            # Required top-level fields
            required_fields = ['projections', 'assumptions', 'company_name']
            self._validate_required_fields(dcf_data, required_fields, 'DCF data')
            
            # Validate company information
            self._validate_company_info(dcf_data)
            
            # Validate financial projections
            if 'projections' in dcf_data:
                self._validate_dcf_projections(dcf_data['projections'])
            
            # Validate DCF assumptions
            if 'assumptions' in dcf_data:
                self._validate_dcf_assumptions(dcf_data['assumptions'])
            
            # Validate market data if provided
            if 'market_data' in dcf_data:
                self._validate_market_data(dcf_data['market_data'])
            
            # Check for data consistency
            self._validate_dcf_consistency(dcf_data)
            
            is_valid = len(self.validation_errors) == 0
            
            if self.validation_errors:
                logger.warning(f"DCF data validation failed with {len(self.validation_errors)} errors")
                raise ReportDataValidationError(
                    f"DCF data validation failed with {len(self.validation_errors)} errors",
                    validation_errors=self.validation_errors,
                    data_type="DCF"
                )
            
            if self.validation_warnings:
                logger.info(f"DCF data validation completed with {len(self.validation_warnings)} warnings")
            
            return is_valid, self.validation_errors, self.validation_warnings
            
        except Exception as e:
            if isinstance(e, ReportDataValidationError):
                raise
            logger.error(f"Unexpected error during DCF validation: {e}")
            raise ValidationError(
                f"Validation process failed: {e}",
                field_name="dcf_data",
                validation_rule="validation_process"
            )
    
    def validate_financial_statement_data(
        self, 
        statement_data: Dict[str, Any], 
        statement_type: str
    ) -> Tuple[bool, List[str], List[str]]:
        """
        Validate financial statement data.
        
        Args:
            statement_data: Dictionary containing financial statement data
            statement_type: Type of statement (balance_sheet, income_statement, cash_flow)
            
        Returns:
            Tuple of (is_valid, errors, warnings)
        """
        try:
            self.validation_errors = []
            self.validation_warnings = []
            
            # Validate based on statement type
            if statement_type == 'balance_sheet':
                self._validate_balance_sheet_data(statement_data)
            elif statement_type == 'income_statement':
                self._validate_income_statement_data(statement_data)
            elif statement_type == 'cash_flow':
                self._validate_cash_flow_data(statement_data)
            else:
                self.validation_errors.append(f"Invalid statement type: {statement_type}")
            
            # Common validations for all statement types
            self._validate_period_data(statement_data)
            self._validate_currency_consistency(statement_data)
            
            is_valid = len(self.validation_errors) == 0
            
            if self.validation_errors:
                raise ReportDataValidationError(
                    f"{statement_type} validation failed with {len(self.validation_errors)} errors",
                    validation_errors=self.validation_errors,
                    data_type=statement_type
                )
            
            return is_valid, self.validation_errors, self.validation_warnings
            
        except Exception as e:
            if isinstance(e, ReportDataValidationError):
                raise
            raise ValidationError(
                f"Financial statement validation failed: {e}",
                field_name=statement_type,
                validation_rule="statement_validation"
            )
    
    def validate_saas_data(self, saas_data: Dict[str, Any]) -> Tuple[bool, List[str], List[str]]:
        """
        Validate SaaS metrics and valuation data.
        
        Args:
            saas_data: Dictionary containing SaaS metrics data
            
        Returns:
            Tuple of (is_valid, errors, warnings)
        """
        try:
            self.validation_errors = []
            self.validation_warnings = []
            
            # Required SaaS fields
            required_fields = ['arr', 'mrr', 'customers']
            self._validate_required_fields(saas_data, required_fields, 'SaaS data')
            
            # Validate SaaS metrics
            self._validate_saas_metrics(saas_data)
            
            # Validate SaaS assumptions if provided
            if 'assumptions' in saas_data:
                self._validate_saas_assumptions(saas_data['assumptions'])
            
            # Check SaaS data consistency
            self._validate_saas_consistency(saas_data)
            
            is_valid = len(self.validation_errors) == 0
            
            if self.validation_errors:
                raise ReportDataValidationError(
                    f"SaaS data validation failed with {len(self.validation_errors)} errors",
                    validation_errors=self.validation_errors,
                    data_type="SaaS"
                )
            
            return is_valid, self.validation_errors, self.validation_warnings
            
        except Exception as e:
            if isinstance(e, ReportDataValidationError):
                raise
            raise ValidationError(
                f"SaaS data validation failed: {e}",
                field_name="saas_data",
                validation_rule="saas_validation"
            )
    
    def _validate_required_fields(self, data: Dict[str, Any], required_fields: List[str], context: str):
        """Validate that required fields are present."""
        missing_fields = [field for field in required_fields if field not in data or data[field] is None]
        
        if missing_fields:
            error_msg = f"Missing required fields in {context}: {', '.join(missing_fields)}"
            self.validation_errors.append(error_msg)
    
    def _validate_company_info(self, dcf_data: Dict[str, Any]):
        """Validate company information."""
        company_name = dcf_data.get('company_name')
        
        if not company_name or not isinstance(company_name, str):
            self.validation_errors.append("Company name must be a non-empty string")
        elif len(company_name.strip()) < 2:
            self.validation_errors.append("Company name must be at least 2 characters long")
        
        # Optional fields validation
        if 'company_info' in dcf_data:
            info = dcf_data['company_info']
            if 'industry' in info and not isinstance(info['industry'], str):
                self.validation_errors.append("Industry must be a string")
            if 'sector' in info and not isinstance(info['sector'], str):
                self.validation_errors.append("Sector must be a string")
    
    def _validate_dcf_projections(self, projections: Union[List, Dict]):
        """Validate DCF financial projections."""
        if isinstance(projections, list):
            # List of annual projections
            if len(projections) < 1:
                self.validation_errors.append("At least one year of projections is required")
                return
            
            for i, projection in enumerate(projections):
                year = i + 1
                self._validate_single_projection(projection, f"Year {year}")
                
        elif isinstance(projections, dict):
            # Single projection or scenario-based projections
            if 'scenarios' in projections:
                for scenario, scenario_data in projections['scenarios'].items():
                    if isinstance(scenario_data, list):
                        for i, projection in enumerate(scenario_data):
                            self._validate_single_projection(projection, f"{scenario} - Year {i+1}")
            else:
                self._validate_single_projection(projections, "Base projection")
        else:
            self.validation_errors.append("Projections must be a list or dictionary")
    
    def _validate_single_projection(self, projection: Dict[str, Any], context: str):
        """Validate a single year's financial projection."""
        required_fields = ['revenue', 'free_cash_flow']
        
        for field in required_fields:
            if field not in projection:
                self.validation_errors.append(f"Missing {field} in {context}")
                continue
            
            value = projection[field]
            if not isinstance(value, (int, float, Decimal)):
                self.validation_errors.append(f"{field} in {context} must be a number")
                continue
            
            # Revenue should be positive
            if field == 'revenue' and value < 0:
                self.validation_errors.append(f"Revenue in {context} cannot be negative: {value}")
            
            # Check for extreme values
            if abs(value) > 1e12:  # $1 trillion
                self.validation_warnings.append(f"Extreme {field} value in {context}: {value}")
    
    def _validate_dcf_assumptions(self, assumptions: Dict[str, Any]):
        """Validate DCF model assumptions."""
        # Discount rate validation
        if 'discount_rate' in assumptions:
            discount_rate = assumptions['discount_rate']
            try:
                rate = float(discount_rate)
                if rate <= 0 or rate >= 1:
                    self.validation_errors.append(f"Discount rate must be between 0 and 1, got: {rate}")
                elif rate < 0.05 or rate > 0.30:
                    self.validation_warnings.append(f"Unusual discount rate: {rate:.1%}. Typical range is 5-30%.")
            except (ValueError, TypeError):
                self.validation_errors.append("Discount rate must be a valid number")
        
        # Terminal growth rate validation
        if 'terminal_growth_rate' in assumptions:
            terminal_rate = assumptions['terminal_growth_rate']
            try:
                rate = float(terminal_rate)
                if rate < 0 or rate >= 0.10:
                    self.validation_errors.append(f"Terminal growth rate must be between 0% and 10%, got: {rate:.1%}")
                elif rate > 0.05:
                    self.validation_warnings.append(f"High terminal growth rate: {rate:.1%}. Consider if sustainable.")
            except (ValueError, TypeError):
                self.validation_errors.append("Terminal growth rate must be a valid number")
        
        # Tax rate validation
        if 'tax_rate' in assumptions:
            tax_rate = assumptions['tax_rate']
            try:
                rate = float(tax_rate)
                if rate < 0 or rate > 1:
                    self.validation_errors.append(f"Tax rate must be between 0% and 100%, got: {rate:.1%}")
            except (ValueError, TypeError):
                self.validation_errors.append("Tax rate must be a valid number")
    
    def _validate_market_data(self, market_data: Dict[str, Any]):
        """Validate market data inputs."""
        if 'risk_free_rate' in market_data:
            try:
                rate = float(market_data['risk_free_rate'])
                if rate < 0 or rate > 0.20:  # 20% seems extreme for risk-free rate
                    self.validation_warnings.append(f"Unusual risk-free rate: {rate:.2%}")
            except (ValueError, TypeError):
                self.validation_errors.append("Risk-free rate must be a valid number")
        
        if 'market_risk_premium' in market_data:
            try:
                premium = float(market_data['market_risk_premium'])
                if premium < 0 or premium > 0.15:  # 15% seems extreme for market risk premium
                    self.validation_warnings.append(f"Unusual market risk premium: {premium:.2%}")
            except (ValueError, TypeError):
                self.validation_errors.append("Market risk premium must be a valid number")
    
    def _validate_dcf_consistency(self, dcf_data: Dict[str, Any]):
        """Validate internal consistency of DCF data."""
        assumptions = dcf_data.get('assumptions', {})
        discount_rate = assumptions.get('discount_rate', 0)
        terminal_rate = assumptions.get('terminal_growth_rate', 0)
        
        # Terminal growth rate should be less than discount rate
        if discount_rate and terminal_rate and terminal_rate >= discount_rate:
            self.validation_errors.append(
                f"Terminal growth rate ({terminal_rate:.1%}) must be less than discount rate ({discount_rate:.1%})"
            )
    
    def _validate_balance_sheet_data(self, data: Dict[str, Any]):
        """Validate balance sheet specific data."""
        # Check for required sections
        required_sections = ['assets', 'liabilities', 'equity']
        for section in required_sections:
            if section not in data:
                self.validation_errors.append(f"Missing balance sheet section: {section}")
    
    def _validate_income_statement_data(self, data: Dict[str, Any]):
        """Validate income statement specific data."""
        # Check for required sections
        required_sections = ['revenue', 'expenses']
        for section in required_sections:
            if section not in data:
                self.validation_errors.append(f"Missing income statement section: {section}")
    
    def _validate_cash_flow_data(self, data: Dict[str, Any]):
        """Validate cash flow statement specific data."""
        # Check for required sections
        required_sections = ['operating', 'investing', 'financing']
        for section in required_sections:
            if section not in data:
                self.validation_errors.append(f"Missing cash flow section: {section}")
    
    def _validate_period_data(self, data: Dict[str, Any]):
        """Validate period and date information."""
        if 'period_end' in data:
            try:
                if isinstance(data['period_end'], str):
                    datetime.fromisoformat(data['period_end'].replace('Z', '+00:00'))
            except ValueError:
                self.validation_errors.append("Invalid period_end date format")
        
        if 'period_start' in data and 'period_end' in data:
            try:
                start = datetime.fromisoformat(data['period_start'].replace('Z', '+00:00'))
                end = datetime.fromisoformat(data['period_end'].replace('Z', '+00:00'))
                if start >= end:
                    self.validation_errors.append("Period start must be before period end")
            except ValueError:
                pass  # Date format errors already caught above
    
    def _validate_currency_consistency(self, data: Dict[str, Any]):
        """Validate currency consistency across the statement."""
        if 'currency' in data:
            currency = data['currency']
            if not isinstance(currency, str) or len(currency) != 3:
                self.validation_errors.append("Currency must be a 3-character currency code (e.g., 'USD')")
    
    def _validate_saas_metrics(self, saas_data: Dict[str, Any]):
        """Validate SaaS-specific metrics."""
        # ARR validation
        if 'arr' in saas_data:
            arr = saas_data['arr']
            if not isinstance(arr, (int, float)) or arr < 0:
                self.validation_errors.append("ARR must be a positive number")
        
        # MRR validation
        if 'mrr' in saas_data:
            mrr = saas_data['mrr']
            if not isinstance(mrr, (int, float)) or mrr < 0:
                self.validation_errors.append("MRR must be a positive number")
        
        # Customer count validation
        if 'customers' in saas_data:
            customers = saas_data['customers']
            if not isinstance(customers, int) or customers < 0:
                self.validation_errors.append("Customer count must be a non-negative integer")
        
        # Churn rate validation
        if 'churn_rate' in saas_data:
            churn = saas_data['churn_rate']
            if not isinstance(churn, (int, float)) or churn < 0 or churn > 1:
                self.validation_errors.append("Churn rate must be between 0 and 1 (0% to 100%)")
    
    def _validate_saas_assumptions(self, assumptions: Dict[str, Any]):
        """Validate SaaS-specific assumptions."""
        if 'arr_multiple' in assumptions:
            multiple = assumptions['arr_multiple']
            if not isinstance(multiple, (int, float)) or multiple <= 0:
                self.validation_errors.append("ARR multiple must be a positive number")
            elif multiple < 1 or multiple > 20:
                self.validation_warnings.append(f"Unusual ARR multiple: {multiple}x. Typical range is 1-20x.")
    
    def _validate_saas_consistency(self, saas_data: Dict[str, Any]):
        """Validate internal consistency of SaaS data."""
        arr = saas_data.get('arr')
        mrr = saas_data.get('mrr')
        
        # ARR should be approximately MRR * 12
        if arr and mrr:
            expected_arr = mrr * 12
            difference_pct = abs(arr - expected_arr) / expected_arr if expected_arr > 0 else 0
            
            if difference_pct > 0.05:  # More than 5% difference
                self.validation_warnings.append(
                    f"ARR ({arr:,.0f}) and MRR ({mrr:,.0f}) appear inconsistent. "
                    f"Expected ARR ≈ {expected_arr:,.0f} (MRR × 12)"
                )