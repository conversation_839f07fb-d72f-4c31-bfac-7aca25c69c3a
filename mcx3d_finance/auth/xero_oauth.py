import logging
import secrets
import json
import base64
import hashlib
from typing import Optional, Dict, Any, Tuple
from urllib.parse import urlparse, parse_qs
from datetime import datetime, timedelta, timezone
import requests
from requests_oauthlib import OAuth2Session

from ..core.config import get_xero_config
from ..db.session import SessionLocal
from ..db.models import Organization
from ..utils.redis_client import get_redis_client

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class XeroAuthManager:
    """Complete Xero OAuth 2.0 authentication manager with proper OAuth2Session implementation."""

    def __init__(self):
        self.config = get_xero_config()
        self.redis_client = get_redis_client()
        
        # Xero OAuth2 endpoints
        self.authorization_base_url = "https://login.xero.com/identity/connect/authorize"
        self.token_url = "https://identity.xero.com/connect/token"

    def _generate_code_verifier(self) -> str:
        """
        Generate a cryptographically random code_verifier for PKCE.
        
        Returns:
            Base64url-encoded random string (43-128 characters)
        """
        # Generate 32 random bytes and encode as base64url (43 characters)
        return base64.urlsafe_b64encode(secrets.token_bytes(32)).decode('utf-8').rstrip('=')

    def _generate_code_challenge(self, code_verifier: str) -> str:
        """
        Generate code_challenge from code_verifier using SHA256.
        
        Args:
            code_verifier: The code verifier string
            
        Returns:
            Base64url-encoded SHA256 hash of the code verifier
        """
        # Create SHA256 hash of code_verifier
        digest = hashlib.sha256(code_verifier.encode('utf-8')).digest()
        # Encode as base64url without padding
        return base64.urlsafe_b64encode(digest).decode('utf-8').rstrip('=')

    def generate_auth_url(self, state: Optional[str] = None) -> Tuple[Optional[str], Optional[str]]:
        """
        Generate Xero authorization URL with PKCE support.

        Args:
            state: Optional state parameter for CSRF protection

        Returns:
            Tuple of (Authorization URL, state) or (None, None) if error
        """
        try:
            # Generate state for CSRF protection if not provided
            if not state:
                state = secrets.token_urlsafe(32)

            # Generate PKCE parameters
            code_verifier = self._generate_code_verifier()
            code_challenge = self._generate_code_challenge(code_verifier)

            # Store state and code_verifier in Redis with 10-minute expiration
            self.redis_client.setex(f"xero_oauth_state:{state}", 600, "valid")
            self.redis_client.setex(f"xero_oauth_verifier:{state}", 600, code_verifier)

            # Create OAuth2Session with insecure transport allowed for localhost
            import os
            os.environ['OAUTHLIB_INSECURE_TRANSPORT'] = '1'
            
            oauth = OAuth2Session(
                client_id=self.config["client_id"],
                redirect_uri=self.config["redirect_uri"],
                scope=self.config["scopes"].split(),
                state=state
            )

            # Generate authorization URL with PKCE parameters
            authorization_url, oauth_state = oauth.authorization_url(
                self.authorization_base_url,
                state=state,
                code_challenge=code_challenge,
                code_challenge_method="S256"
            )

            logger.info(f"Generated Xero auth URL with PKCE - state: {state}")
            return authorization_url, state

        except Exception as e:
            logger.error(f"Error generating Xero auth URL: {e}")
            return None, None

    def handle_callback(self, authorization_response_url: str) -> Dict[str, Any]:
        """
        Handle OAuth callback and exchange authorization code for tokens.

        Args:
            authorization_response_url: Full callback URL with authorization code

        Returns:
            Dictionary with success status and token information
        """
        try:
            logger.info("Processing Xero OAuth callback")

            # Parse the callback URL
            parsed_url = urlparse(authorization_response_url)
            query_params = parse_qs(parsed_url.query)

            # Extract parameters
            code = query_params.get("code", [None])[0]
            state = query_params.get("state", [None])[0]
            error = query_params.get("error", [None])[0]

            if error:
                logger.error(f"OAuth error from Xero: {error}")
                return {
                    "success": False,
                    "error": f"Authorization failed: {error}",
                    "error_description": query_params.get("error_description", [""])[0]
                }

            if not code or not state:
                logger.error("Missing authorization code or state in callback")
                return {
                    "success": False,
                    "error": "Missing authorization code or state parameter"
                }

            # Validate state
            stored_state = self.redis_client.get(f"xero_oauth_state:{state}")
            if not stored_state:
                logger.error("Invalid or expired state parameter")
                return {
                    "success": False,
                    "error": "Invalid or expired authorization state"
                }

            # Get code_verifier for PKCE
            code_verifier = self.redis_client.get(f"xero_oauth_verifier:{state}")
            if not code_verifier:
                logger.error("Missing code_verifier for PKCE flow")
                return {
                    "success": False,
                    "error": "Invalid or expired PKCE verification data"
                }
            
            # Decode Redis bytes to string if needed
            if isinstance(code_verifier, bytes):
                code_verifier = code_verifier.decode('utf-8')

            # Clean up state and verifier from Redis
            self.redis_client.delete(f"xero_oauth_state:{state}")
            self.redis_client.delete(f"xero_oauth_verifier:{state}")

            # Create OAuth2Session for token exchange with insecure transport allowed for localhost
            import os
            os.environ['OAUTHLIB_INSECURE_TRANSPORT'] = '1'
            
            oauth = OAuth2Session(
                client_id=self.config["client_id"],
                redirect_uri=self.config["redirect_uri"],
                state=state
            )

            # Exchange authorization code for tokens with PKCE
            token = oauth.fetch_token(
                self.token_url,
                authorization_response=authorization_response_url,
                client_secret=self.config["client_secret"],
                code_verifier=code_verifier,
                include_client_id=True
            )

            logger.info("Successfully exchanged authorization code for tokens")

            # Get tenant information
            tenant_info = self._get_tenant_info(token["access_token"])

            if not tenant_info:
                return {
                    "success": False,
                    "error": "Failed to retrieve tenant information"
                }

            # Store token information
            result = self._store_token_info(token, tenant_info)

            return {
                "success": True,
                "message": "Authorization successful",
                "organization_id": result.get("organization_id"),
                "tenant_name": result.get("tenant_name"),
                "token_expires_in": token.get("expires_in", 1800)
            }

        except Exception as e:
            logger.error(f"Error processing OAuth callback: {e}")
            return {
                "success": False,
                "error": f"Callback processing failed: {str(e)}"
            }

    def _get_tenant_info(self, access_token: str) -> Optional[Dict[str, Any]]:
        """
        Get tenant information from Xero using access token.

        Args:
            access_token: Valid Xero access token

        Returns:
            Tenant information or None if failed
        """
        try:
            headers = {
                "Authorization": f"Bearer {access_token}",
                "Content-Type": "application/json"
            }

            response = requests.get(
                "https://api.xero.com/connections",
                headers=headers,
                timeout=30
            )

            if response.status_code == 200:
                connections = response.json()
                if connections:
                    # Return the first tenant (most common case)
                    tenant = connections[0]
                    logger.info(f"Retrieved tenant info: {tenant.get('tenantName', 'Unknown')}")
                    return tenant
                else:
                    logger.error("No tenant connections found")
                    return None
            else:
                logger.error(f"Failed to get tenant info: {response.status_code} - {response.text}")
                return None

        except Exception as e:
            logger.error(f"Error getting tenant info: {e}")
            return None

    def _store_token_info(self, token: Dict[str, Any], tenant_info: Dict[str, Any]) -> Dict[str, Any]:
        """
        Store token and tenant information in database.

        Args:
            token: OAuth token information
            tenant_info: Xero tenant information

        Returns:
            Dictionary with storage result
        """
        try:
            db = SessionLocal()

            # Check if organization already exists
            existing_org = db.query(Organization).filter(
                Organization.xero_tenant_id == tenant_info.get("tenantId")
            ).first()

            if existing_org:
                # Update existing organization
                existing_org.name = tenant_info.get("tenantName", "Unknown Organization")
                existing_org.xero_token = json.dumps(token)
                existing_org.token_expires_at = datetime.now(timezone.utc) + timedelta(seconds=token.get("expires_in", 1800))
                existing_org.updated_at = datetime.now(timezone.utc)

                db.commit()
                db.refresh(existing_org)

                logger.info(f"Updated existing organization: {existing_org.name}")
                return {
                    "organization_id": existing_org.id,
                    "tenant_name": existing_org.name,
                    "action": "updated"
                }

            else:
                # Create new organization
                new_org = Organization(
                    name=tenant_info.get("tenantName", "Unknown Organization"),
                    xero_tenant_id=tenant_info.get("tenantId"),
                    xero_tenant_type=tenant_info.get("tenantType", "ORGANISATION"),
                    xero_token=json.dumps(token),
                    token_expires_at=datetime.now(timezone.utc) + timedelta(seconds=token.get("expires_in", 1800)),
                    created_at=datetime.now(timezone.utc),
                    updated_at=datetime.now(timezone.utc),
                    is_active=True
                )

                db.add(new_org)
                db.commit()
                db.refresh(new_org)

                logger.info(f"Created new organization: {new_org.name}")
                return {
                    "organization_id": new_org.id,
                    "tenant_name": new_org.name,
                    "action": "created"
                }

        except Exception as e:
            logger.error(f"Error storing token info: {e}")
            if 'db' in locals():
                db.rollback()
            raise
        finally:
            if 'db' in locals():
                db.close()

    def get_valid_token(self, organization_id: int) -> Optional[str]:
        """
        Get valid access token for organization, refreshing if necessary.

        Args:
            organization_id: Organization ID

        Returns:
            Valid access token or None if failed
        """
        try:
            db = SessionLocal()
            organization = db.query(Organization).get(organization_id)

            if not organization or not organization.xero_token:
                logger.error(f"No token found for organization {organization_id}")
                return None

            token_data = json.loads(organization.xero_token)

            # Check if token is still valid
            if organization.token_expires_at:
                # Ensure both datetimes are timezone-aware
                expires_at = organization.token_expires_at
                if expires_at.tzinfo is None:
                    expires_at = expires_at.replace(tzinfo=timezone.utc)
                
                if expires_at > datetime.now(timezone.utc):
                    return token_data.get("access_token")

            # Token expired, try to refresh
            logger.info("Token expired, attempting refresh")
            refreshed_token = self._refresh_token(token_data)

            if refreshed_token:
                # Update organization with new token
                organization.xero_token = json.dumps(refreshed_token)
                organization.token_expires_at = datetime.now(timezone.utc) + timedelta(seconds=refreshed_token.get("expires_in", 1800))
                organization.updated_at = datetime.now(timezone.utc)

                db.commit()
                logger.info("Token refreshed successfully")
                return refreshed_token.get("access_token")
            else:
                logger.error("Token refresh failed")
                return None

        except Exception as e:
            logger.error(f"Error getting valid token: {e}")
            return None
        finally:
            if 'db' in locals():
                db.close()

    def _refresh_token(self, token_data: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """
        Refresh access token using refresh token.

        Args:
            token_data: Current token data including refresh token

        Returns:
            New token data or None if failed
        """
        try:
            refresh_token = token_data.get("refresh_token")
            if not refresh_token:
                logger.error("No refresh token available")
                return None

            # Create OAuth2Session for token refresh
            oauth = OAuth2Session(
                client_id=self.config["client_id"],
                token=token_data
            )

            # Refresh the token
            new_token = oauth.refresh_token(
                self.token_url,
                refresh_token=refresh_token,
                client_id=self.config["client_id"],
                client_secret=self.config["client_secret"]
            )

            logger.info("Token refreshed successfully")
            return new_token

        except Exception as e:
            logger.error(f"Error refreshing token: {e}")
            return None

    def revoke_token(self, organization_id: int) -> bool:
        """
        Revoke tokens for organization.

        Args:
            organization_id: Organization ID

        Returns:
            True if successful, False otherwise
        """
        try:
            db = SessionLocal()
            organization = db.query(Organization).get(organization_id)

            if not organization:
                logger.error(f"Organization {organization_id} not found")
                return False

            # Clear token data
            organization.xero_token = None
            organization.token_expires_at = None
            organization.updated_at = datetime.now(timezone.utc)

            db.commit()
            logger.info(f"Tokens revoked for organization {organization_id}")
            return True

        except Exception as e:
            logger.error(f"Error revoking tokens: {e}")
            return False
        finally:
            if 'db' in locals():
                db.close()